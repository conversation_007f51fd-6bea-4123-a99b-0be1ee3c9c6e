const DbMongoose = require("../../mixins/dbMongo.mixin");
const OrganizationModel = require("./organization.model");
const BaseService = require("../../mixins/baseService.mixin");
const {SERVICE_NAME} = require("./index");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const {IMAGE_SERVICE} = require("../Image");
const {USER_CODES} = require("../../constants/constant");
const {ObjectId} = require("mongoose").Types;
const AuthRole = require("../../mixins/authRole.mixin");

module.exports = {
  name: SERVICE_NAME,
  mixins: [DbMongoose(OrganizationModel), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {
      name: {type: "string", min: 1},
      email: {type: "string", min: 1},
    },
    populates: {
      "avatarId": 'images.get',
    },
    populateOptions: ["avatarId"],
  },
  hooks: {
    before: {
      "update|upload": "checkPermission",
    },
    after: {
      find(ctx, res) {
        return res;
      }
    }
  },

  actions: {
    upload: {
      role: USER_CODES.ORG_ADMIN,
      // auth: "required",
      async handler(ctx) {
        const {organizationId} = ctx.meta.$multipart;
        const image = await ctx.call(IMAGE_SERVICE.upload, ctx.params, {meta: ctx.meta});
        const organizationUpdated = await this.adapter.updateById(organizationId, {avatarId: image?._id});
        return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, organizationUpdated);
      }
    },
    update: {
      rest: "PUT /:id",
      auth: "required",
      role: USER_CODES.ORG_ADMIN,
      async handler(ctx) {
        return ctx;
      }
    },
    create: {
      rest: "POST /",
      auth: "required",
      params: {
        name: {type: "string", min: 1},
        email: {type: "email"},
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const data = ctx.params;
        // Check email
        const checkMail = await this.adapter.findOne({email: data.email});
        if (checkMail) {
          throw new MoleculerClientError(i18next.t("email_already_exists"));
        }
        const orgData = await this.adapter.insert(data);
        this.broker.emit('organization.created', orgData);
        return orgData;
      }
    },
    detail: {
      rest: {
        method: "GET",
        path: "/:id/detail",
      },
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        const {id} = ctx.params;
        const organization = await this.adapter.findById(id);
        const orgTransformed = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, organization);

        const [subscription, orgPermission] = await Promise.all([
          await ctx.call("subscriptions.getActive", {organizationId: orgTransformed._id}),
          await ctx.call("permissions.getOne", {organizationId: id}),
        ]);
        const organizationUsed = {
          textUsed: orgPermission?.accessLimit.textUsed,
          mediaUsed: orgPermission?.accessLimit.mediaUsed,
          textLimit: orgPermission?.accessLimit.textLimit,
          mediaLimit: orgPermission?.accessLimit.mediaLimit,
          capacityLimit: orgPermission?.accessLimit.capacityLimit,
          capacityUsed: orgPermission?.accessLimit.capacityUsed,
          mediaDurationLimit: orgPermission?.accessLimit.mediaDurationLimit,
          accessRole: orgPermission?.accessRole
        };
        return {...orgTransformed, subscription, organizationUsed};
      }
    },
    statisticalOrganization: {
      rest: {
        method: "GET",
        path: "/statisticalOrganization",
      },
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        const {organizationId, userId} = ctx.params;
        const {page, pageSize} = this.extractParamsList(ctx.params);
        const sort = ctx.params.sort || "-lastVisit";
        const sortAggregate = sort.startsWith('-') ? {[sort.split('-')[1]]: -1} : {[sort]: 1};
        const queryTime = this.extractQueryTime(ctx.params);

        let member;
        if (!userId) {
          member = await ctx.call('users.find', {query: {organizationId: organizationId, isDeleted: false}})
        }
        const memberId = userId ? [ObjectId(userId)] : member.map(item => ObjectId(item._id));
        const [workspace] = await ctx.call("workspaces.find", {query: {organizationId, isDeleted: false}});

        const query = {
          workspaceId: ObjectId(workspace._id),
          isDeleted: false,
          createdAt: queryTime.createdAt
        };

        const [textSubmitRes, mediaSubmitRes, permissionRes] = await Promise.allSettled([
          ctx.call('inputs.statisticInput', {
            query: {...query, inputType: {$nin: ["video", "offline_video", "image", "audio"]}}
          }),
          ctx.call('inputs.statisticInput', {
            query: {...query, inputType: {$in: ["video", "offline_video", "image", "audio"]}}
          }),
          ctx.call("permissions.find", {
            query: {isDeleted: false, organizationId: organizationId, userId: {$in: memberId}}, populate: []
          })
        ]);

        const textSubmit = textSubmitRes.status === 'fulfilled' ? textSubmitRes.value : [];
        const mediaSubmit = mediaSubmitRes.status === 'fulfilled' ? mediaSubmitRes.value : [];
        const permission = permissionRes.status === 'fulfilled' ? permissionRes.value : [];

        return ctx.call("users.statisticUser", {
          query: {memberId, page, pageSize, sortAggregate, permission, textSubmit, mediaSubmit}
        });
      }
    },
    lock: {
      rest: 'POST /:id/lock',
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {id} = ctx.params;
        const organization = await this.adapter.findById(id);
        if(!organization) {
          throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
        }

        if(organization?.active === false) {
          return await this.adapter.updateById(id, {active: true});
        } else {
          return await this.adapter.updateById(id, {active: false});
        }
      }
    },
    getAll: {
      rest: 'GET /manager',
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {name, time} = ctx.params;
        let query;
        if (time) {
          query = this.extractQueryTime(ctx.params);
        }

        const paramsList = this.extractParamsList(ctx.params);

        const sort = paramsList.sort;

        let sortAggregate = {[sort]: 1};
        if (sort.indexOf('-') !== -1) {
          sortAggregate = {[sort.split('-')[1]]: -1};
        }

        const [result] = await this.trackingOrganization(ctx, name, query, paramsList, sortAggregate);
        return result;
      }
    },
    getOne: {
      async handler(ctx) {
        const {id} = ctx.params;
        return await this.adapter.findById(id);
      }
    },
    remove: {
      rest: "DELETE /:id",
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {id} = ctx.params;
        const organization = await this.adapter.findById(id);
        if(!organization) {
          throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
        }
        this.broker.emit('organization.removed', organization);

        // return organization
        return await this.adapter.removeById(id);
      }
    }
  },
  methods: {
    async checkPermission(context) {
      const { action, params, meta } = context;
      const organizationId = params?.id || params?.organizationId || context.meta.$multipart.organizationId;
      const { user } = meta;
      if(user.isSystemAdmin) return;
      delete context.params.email;
      delete context.params.isDeleted;
      delete context.params.active;

      if(user?.organizationId.toString() !== organizationId?.toString()) {
        throw new MoleculerClientError(i18next.t("you_need_to_be_an_admin"), 403, "FORBIDDEN");
      }

      const organization = await this.adapter.findById(organizationId);
      if(!organization) {
        throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
      }

      if(organization?.active === false) {
        throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
      }
    },

    async trackingOrganization(ctx, name, query, paramsList, sortAggregate) {
      const userOrganization = await ctx.call("users.find", {
        query: {organizationId: {$exists: true}, isDeleted: false}, populate: []
      });

      return OrganizationModel.aggregate([
        {
          $match: {
            isDeleted: false,
            ...(query ? {createdAt: query.createdAt} : {}),
            ...(name ? {name: {$regex: name, $options: "i"}} : {}),
          }
        },
        {
          $lookup: {
            from: "Image",
            localField: "avatarId",
            foreignField: "_id",
            as: "avatarId"
          }
        },
        {
          $lookup: {
            from: "User",
            let: { email: "$email" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ["$email", "$$email"]
                  }
                }
              },
              {
                $project: {
                  _id: 1, email: 1, fullName: 1, avatar: 1, active: 1, phone: 1,
                  gender: 1, lastVisit: 1, imageAvatarId: 1, organizationId: 1,
                  isSystemAdmin: 1, lastLogin: 1, type: 1, role: 1, persona: 1
                }
              }
            ],
            as: "email"
          }
        },
        {
          $addFields: {
            active: { $ifNull: ["$active", true] },
            avatarId: {$arrayElemAt: ["$avatarId", 0]},
            totalUser: {
              $size: {
                $filter: {
                  input: userOrganization,
                  as: "member",
                  cond: {$eq: ["$$member.organizationId", "$_id"]}
                }
              }
            },
          }
        },
        {$sort: sortAggregate},
        {
          $facet: {
            rows: [
              {$skip: (paramsList.page - 1) * paramsList.pageSize},
              {$limit: paramsList.pageSize}
            ],
            metadata: [
              {$count: "total"},
              {
                $addFields: {
                  page: paramsList.page,
                  pageSize: paramsList.pageSize,
                  totalPages: {$ceil: {$divide: ["$total", paramsList.pageSize]}}
                }
              }
            ]
          }
        },
        {
          $project: {
            rows: 1,
            metadata: {
              $cond: {
                if: {$eq: [{$size: "$metadata"}, 0]},
                then: [{total: 0, page: 0, pageSize: 0, totalPages: 0}],
                else: "$metadata"
              }
            }
          }
        },
        {$replaceRoot: {newRoot: {$mergeObjects: ["$$ROOT", {$arrayElemAt: ["$metadata", 0]}]}}},
        {$unset: "metadata"}
      ])
    },
  },
};
