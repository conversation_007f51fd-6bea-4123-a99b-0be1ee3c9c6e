const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const DashboardModel = require("./dashboards.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const {MEDIA_INPUT_TYPE} = require("../../constants/constant");
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "dashboards",
  mixins: [DbMongoose(DashboardModel), BaseService, FunctionsCommon, AuthRole],

  hooks: {
    before: {
      "*": "checkPermission",
    }
  },

  actions: {
    toolSubmitedForUser: {
      rest: {
        method: "GET",
        path: "/toolSubmited",
      },
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const query = this.extractQueryTime(ctx.params);
        const workspace = await ctx.call('workspaces.getOneByUser');

        const getInputsCount = async (inputType) => {
          return await ctx.call('inputs.count', {
            query: {
              workspaceId: workspace._id,
              userId: user?._id,
              inputType: {...(inputType === 'MEDIA' ? {$in: MEDIA_INPUT_TYPE} : {$nin: MEDIA_INPUT_TYPE})},
              createdAt: query.createdAt
            }
          });
        };

        const textSubmited = await getInputsCount('TEXT');
        const mediaSubmited = await getInputsCount('MEDIA');

        return [
          {type: "MEDIA", value: mediaSubmited},
          {type: "TEXT", value: textSubmited}
        ];
      }
    },
    toolSubmitedForOrganization: {
      rest: {
        method: "GET",
        path: "/toolSubmitedForOrganization",
      },
      auth: "required",
      permission: true,
      async handler(ctx) {
        const {user} = ctx.meta;
        const {userId} = ctx.params;
        const queryTime = this.extractQueryTime(ctx.params);
        const workspace = await ctx.call('workspaces.getOneByOrganization');

        const query = {
          ...(userId && {userId}),
          workspaceId: workspace._id,
          isDeleted: false,
          createdAt: queryTime.createdAt
        };

        const [textSubmitedRes, mediaSubmitedRes] = await Promise.allSettled([
          ctx.call('inputs.count', {
            query: {...query, inputType: {$nin: MEDIA_INPUT_TYPE}}
          }),
          ctx.call('inputs.count', {
            query: {...query, inputType: {$in: MEDIA_INPUT_TYPE}}
          })
        ]);

        const textSubmited = textSubmitedRes.status === 'fulfilled' ? textSubmitedRes.value : [];
        const mediaSubmited = mediaSubmitedRes.status === 'fulfilled' ? mediaSubmitedRes.value : [];
        return [
          {type: "MEDIA", value: mediaSubmited},
          {type: "TEXT", value: textSubmited}
        ];
      }
    },

    mediaUsedForUser: {
      rest: {
        method: "GET",
        path: "/mediaUsed",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {user} = ctx.meta;
        const {workspaceId} = ctx.params;
        const [subscription, permission] = await Promise.all([
          ctx.call('subscriptions.getActive', {userId: user?._id}),
          ctx.call("permissions.getOne", {userId: user?._id})
        ]);
        const query = {
          userId: user?._id,
          workspaceId: workspaceId,
          inputType: {$in: MEDIA_INPUT_TYPE},
          createdAt: {$gt: subscription.startDate, $lte: new Date()}
        };
        const submited = await ctx.call('inputs.count', {query});

        return [
          {type: "TOTAL", value: +permission.accessLimit.mediaLimit},
          {type: "USED", value: submited}
        ];
      }
    },

    toolsUsedForUser: {
      rest: {
        method: "GET",
        path: "/toolsUsed",
      },
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const query = this.extractQueryTime(ctx.params);
        const workspace = await ctx.call('workspaces.getOneByUser');

        const mediaType = MEDIA_INPUT_TYPE;
        const queryTools = {
          isDeleted: false,
          visible: {$in: ['public', 'developing']},
          groupToolIds: {$exists: true}
        };
        const allTools = await ctx.call('tools.find', {query: queryTools});

        const [textTools, mediaTools, inputs] = await Promise.all([
          allTools.filter(i => !mediaType.includes(i.inputType)),
          allTools.filter(i => mediaType.includes(i.inputType)),
          ctx.call('inputs.find', {
            query: {
              workspaceId: workspace._id,
              userId: user._id,
              toolId: {$exists: true},
              createdAt: query.createdAt
            }
          }),
        ]);
        const inputTextTools = inputs.filter(i => !mediaType.includes(i.inputType));
        const inputMediaTools = inputs.filter(i => mediaType.includes(i.inputType));

        const [groupToolUsed, groupTextToolUsed, groupMediaToolUsed] = await Promise.all([
          this.groupBy(inputs, "toolId"),
          this.groupBy(inputTextTools, "toolId"),
          this.groupBy(inputMediaTools, "toolId")
        ]);

        return [
          {amount: allTools.length, type: 'TOOLS', name: 'TOTAL'},
          {amount: Object.keys(groupToolUsed).length, type: 'TOOLS', name: "USED"},
          {amount: mediaTools.length, type: 'MEDIA_TOOLS', name: 'TOTAL'},
          {amount: Object.keys(groupMediaToolUsed).length, type: 'MEDIA_TOOLS', name: "USED"},
          {amount: textTools.length, type: 'TEXT_TOOLS', name: 'TOTAL'},
          {amount: Object.keys(groupTextToolUsed).length, type: 'TEXT_TOOLS', name: "USED"},
        ];
      }
    },
    toolsUsedForOrganization: {
      rest: {
        method: "GET",
        path: "/toolsUsedForOrganization",
      },
      auth: "required",
      permission: true,
      async handler(ctx) {
        const {user} = ctx.meta;
        const {userId} = ctx.params;
        const query = this.extractQueryTime(ctx.params);

        const workspace = await ctx.call('workspaces.getOneByOrganization');

        const mediaType = MEDIA_INPUT_TYPE;

        const allTools = await ctx.call('tools.find', {
          query: {
            isDeleted: false,
            visible: {$in: ['public', 'developing']},
            groupToolIds: {$exists: true}
          }
        });

        const [textTools, mediaTools, inputs] = await Promise.all([
          allTools.filter(i => !mediaType.includes(i.inputType)),
          allTools.filter(i => mediaType.includes(i.inputType)),
          ctx.call('inputs.find', {
            query: {
              workspaceId: workspace._id,
              toolId: {$exists: true},
              createdAt: query.createdAt,
              ...(userId && {userId})
            }
          }),
        ]);

        const inputTextTools = inputs.filter(i => !mediaType.includes(i.inputType));
        const inputMediaTools = inputs.filter(i => mediaType.includes(i.inputType));

        const [toolUsed, textToolUsed, mediaToolUsed] = await Promise.all([
          this.groupBy(inputs, "toolId"),
          this.groupBy(inputTextTools, "toolId"),
          this.groupBy(inputMediaTools, "toolId")
        ]);

        return [
          {amount: allTools.length, type: 'TOOLS', name: 'TOTAL'},
          {amount: Object.keys(toolUsed).length, type: 'TOOLS', name: "USED"},
          {amount: mediaTools.length, type: 'MEDIA_TOOLS', name: 'TOTAL'},
          {amount: Object.keys(mediaToolUsed).length, type: 'MEDIA_TOOLS', name: "USED"},
          {amount: textTools.length, type: 'TEXT_TOOLS', name: 'TOTAL'},
          {amount: Object.keys(textToolUsed).length, type: 'TEXT_TOOLS', name: "USED"},
        ];
      }
    },

    statisticProjectForUser: {
      rest: {
        method: "GET",
        path: "/project",
      },
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const query = this.extractQueryTime(ctx.params);
        const workspace = await ctx.call('workspaces.getOneByUser');
        const projects = await ctx.call('projects.find', {
          query: {
            ownerId: user._id,
            isDeleted: false,
            isDraft: false,
            workspaceId: workspace._id,
            createdAt: query.createdAt
          }
        });
        const sharedProjects = await ctx.call('share.find', {
          query: {
            userId: user._id, type: "PROJECT",
            createdAt: query.createdAt
          }
        });
        return [
          {amount: projects.length, type: 'MY_PROJECTS'},
          {amount: sharedProjects.length, type: 'SHARE_WITH_ME'},
        ];
      }
    },
    statisticProjectForOrganization: {
      rest: {
        method: "GET",
        path: "/projectForOrganization",
      },
      auth: "required",
      permission: true,
      async handler(ctx) {
        const {user} = ctx.meta;
        const {userId} = ctx.params;
        const query = this.extractQueryTime(ctx.params);
        const workspace = await ctx.call('workspaces.getOneByOrganization');
        const projects = await ctx.call('projects.find', {
          query: {
            ...(userId && {ownerId: userId}),
            isDeleted: false,
            isDraft: false,
            workspaceId: workspace._id,
            createdAt: query.createdAt
          }
        });
        let sharedProjects = await ctx.call('share.find', {
          query: {
            ...(userId && {userId}),
            type: "PROJECT",
            createdAt: query.createdAt
          }
        });

        sharedProjects = sharedProjects.filter((project, index, self) => {
          return project.projectId?.workspaceId.toString() === workspace?._id.toString() &&
            self.findIndex(p => p.projectId?._id.toString() === project.projectId?._id.toString()) === index;
        });

        return [
          {amount: projects.length, type: 'MY_PROJECTS'},
          {amount: sharedProjects.length, type: 'SHARE_WITH_ME'},
        ];
      }
    },

    topToolsUsed: {
      rest: {
        method: "GET",
        path: "/topToolsUsed",
      },
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const workspace = await ctx.call('workspaces.getOneByUser');
        const query = this.extractQueryTime(ctx.params);
        const queryTools = {
          isDeleted: false,
          // visible: {$in: ['public', 'developing']},
          groupToolIds: {$exists: true}
        };
        const [allTools, inputs] = await Promise.all([
          ctx.call('tools.find', {query: queryTools}),
          ctx.call('inputs.find', {
            query: {
              workspaceId: workspace._id,
              userId: user._id,
              toolId: {$exists: true},
              createdAt: query.createdAt
            }
          })
        ]);
        const mapSubmitted = {};
        inputs.forEach(input => {
          mapSubmitted[input.toolId] = (mapSubmitted[input.toolId] || 0) + 1;
        });

        const result = allTools.map(tool => ({
          name: tool.name,
          amount: mapSubmitted[tool._id] || 0,
          type: "TOTAL_SUBMITTED"
        }));

        return result.sort((a, b) => b.amount - a.amount).slice(0, 10);
      }
    },
    topToolsUsedForOrganization: {
      rest: {
        method: "GET",
        path: "/topToolsUsedForOrganization",
      },
      auth: "required",
      permission: true,
      async handler(ctx) {
        const {user} = ctx.meta;
        const query = this.extractQueryTime(ctx.params);
        const {userId} = ctx.params;
        const workspace = await ctx.call('workspaces.getOneByOrganization');

        const [allTools, inputs] = await Promise.all([
          ctx.call('tools.find', {
            query: {
              isDeleted: false,
              visible: {$in: ['public', 'developing']},
              groupToolIds: {$exists: true}
            }
          }),
          ctx.call('inputs.find', {
            query: {
              ...(userId && {userId}),
              workspaceId: workspace._id,
              toolId: {$exists: true},
              createdAt: query.createdAt
            }
          })
        ]);

        const mapSubmitted = inputs.reduce((acc, input) => {
          acc[input.toolId] = (acc[input.toolId] || 0) + 1;
          return acc;
        }, {});

        const result = allTools.map(tool => ({
          name: tool.name,
          amount: mapSubmitted[tool._id] || 0,
          type: "TOTAL_SUBMITTED"
        }));

        return result.sort((a, b) => b.amount - a.amount).slice(0, 10);
      }
    },

    tokensByUser: {
      rest: {
        method: "GET",
        path: "/tokensByUser",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {isDeveloper} = ctx.params;
        const query = this.extractQueryTime(ctx.params);
        const instructions = await ctx.call('instructions.find', {query: {isDeleted: false}});
        let responses = await this.broker.call("responses.findToDashboard", {
          query: {
            ...query,
            state: "done",
            inputId: {$exists: true},
          }, isDeveloper
        });

        const result = await Promise.all(instructions.map(async instruction => {
          const responseInstruction = responses.filter(response => response.inputData?.instructionId === instruction._id);
          const data = await this.getCostFromGPTModel(responseInstruction);
          return {
            shortName: instruction.shortName,
            gptModel: instruction.gptModel,
            ...data,
          };
        }));

        return result.sort((a, b) => b.totalCost - a.totalCost);
      }
    },

    tokensByTool: {
      rest: {
        method: "GET",
        path: "/toolTokens",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {isDeveloper} = ctx.params;
        const query = this.extractQueryTime(ctx.params);
        const queryTools = {
          isDeleted: false,
          visible: {$in: ['public', 'developing']},
          groupToolIds: {$exists: true}
        };

        const tools = await ctx.call('tools.find', {query: queryTools, fields: ["_id", "name", "inputType"]});
        const responses = await this.broker.call("responses.findToDashboard", {
          query: {
            ...query,
            state: "done",
            inputId: {$exists: true},
            toolId: {$exists: true}
          }, isDeveloper
        });

        const result = await Promise.all(tools.map(async tool => {
          const responseInstruction = responses.filter(response => response.toolId.toString() === tool._id);
          const data = await this.getCostFromGPTModel(responseInstruction);
          return {
            name: tool.name,
            ...data,
            averageCost: data.numberOfSubmissions ? +(data.totalCost / data.numberOfSubmissions) : 0,
            avarageMinutes: data.numberOfSubmissions ? +(data.totalMinutes / data.numberOfSubmissions) : 0,
            averageInputTokens: data.numberOfSubmissions ? +(data.promptTokens / data.numberOfSubmissions).toFixed(2) : 0,
            averageOutputTokens: data.numberOfSubmissions ? +(data.completionTokens / data.numberOfSubmissions).toFixed(2) : 0,
          };
        }));

        return result.sort((a, b) => b.totalCost - a.totalCost);
      }
    },

    mostToolsUsed: {
      rest: {
        method: "GET",
        path: "/mostToolsUsed",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {workspaceId} = ctx.params;
          // Fetch tools with specified query and fields
          const tools = await ctx.call('tools.find', {
            query: {isDeleted: false, visible: {$in: ['public', 'private']}},
            fields: ["_id", "name", "localization"]
          });

          // Prepare an array of actions for counting inputs for each tool
          const mcallActions = tools.map(tool => ({
            action: 'inputs.count',
            params: {query: {workspaceId: workspaceId, toolId: tool._id, isDeleted: false}}
          }));

          // Execute multiple service calls concurrently
          const mcallRes = await ctx.mcall(mcallActions);

          // Combine the results with their respective tools
          return tools.map((tool, i) => {
            return {
              name: tool.name,
              count: mcallRes[i]
            };
          });

        } catch (error) {
          console.error(error);
          throw error; // Rethrow the error for the caller to handle
        }
      }
    },

    mostToolFeedback: {
      rest: {
        method: "GET",
        path: "/mostToolFeedback",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const [tools, responses] = await Promise.all([
            ctx.call('tools.find', {
              query: {isDeleted: false, visible: {$in: ['public', 'private']}},
              fields: ["_id", "name", "localization"]
            }),
            ctx.call('responses.find', {
              query: {rating: {$in: ['like', 'dislike']}, isDeleted: false, inputId: {$exists: true}},
              fields: ["inputId", "rating"]
            })
          ]);

          const mapToolRating = responses.reduce((map, r) => {
            const {toolId} = r.inputId;
            if (r.rating && toolId) {
              const mapEntry = map[toolId] || {like: 0, dislike: 0};
              if (r.rating === "like") {
                mapEntry.like = (mapEntry.like || 0) + 1;
              } else if (r.rating === "dislike") {
                mapEntry.dislike = (mapEntry.dislike || 0) + 1;
              }
              map[toolId] = mapEntry;
            }
            return map;
          }, {});

          return tools.map(tool => {
            const {_id, name} = tool;
            const {like = 0, dislike = 0} = mapToolRating[_id] || {like: 0, dislike: 0};
            return {name, like, dislike};
          }).sort((a, b) => b.like - a.like);

        } catch (error) {
          console.error(error);
          throw error;
        }
      }
    },

    userSubscriptions: {
      rest: {
        method: "GET",
        path: "/userSubscriptions",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const [users, subscriptions] = await Promise.all([
          ctx.call('users.find', {
            query: {isDeleted: false, active: true},
            fields: ["_id", "fullName", "lastLogin", "lastVisit", "email"],
          }),
          ctx.call('subscriptions.find', {
            query: {isDeleted: false, status: "ACTIVE"},
            fields: ["_id", "customerId.userId", "packageId.name", "packageId.features", "packageId.prices", "startDate", "endDate", "unitPrice"],
          }),
        ]);

        const mapSubscriptions = subscriptions.reduce((map, subscription) => {
          if (subscription.customerId) {
            const {userId} = subscription.customerId;
            map[userId] = {
              packageId: subscription.packageId,
              startDate: subscription.startDate,
              endDate: subscription.endDate,
              unitPrice: subscription.unitPrice
            };
          }
          return map;
        }, {});

        return users.map(user => ({
          ...user,
          subscription: mapSubscriptions[user._id]
        }));
      }

    },

    totalCost: {
      rest: {
        method: "GET",
        path: "/totalCost",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        const responses = await this.broker.call("responses.findToDashboard", {
          query: {
            createdAt: {
              $gte: startOfMonth,
              $lte: endOfMonth
            },
            state: "done",
            inputId: {$exists: true},
          },
        });

        return this.getCostFromGPTModel(responses);
      }
    },

    statisticUsers: {
      rest: {
        method: "GET",
        path: "/statisticUsers",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {email, fullName} = ctx.params;
        const userQuery = {
          isDeleted: false,
          active: true,
          ...(email && {email}),
          ...(fullName && {fullName})
        };

        const query = this.extractQueryTime(ctx.params);

        const [users, projects, submits] = await Promise.all([
          ctx.call('users.getAllWithoutPagination', {
            query: JSON.stringify(userQuery),
            searchFields: "fullName,email",
          }),
          ctx.call('projects.statisticProject', {query}),
          ctx.call('inputs.statisticInput', {
            query: {
              createdAt: query.createdAt,
            }
          }),
        ]);
        const mapSubmits = this.convertObject(submits, '_id');
        const mapProjects = this.convertObject(projects, '_id');

        const result = users.map(user => {
          return {
            ...user,
            numberOfProjects: mapProjects[user._id] ? mapProjects[user._id].numberProjects : 0,
            numberOfSubmits: mapSubmits[user._id] ? mapSubmits[user._id].numberSubmit : 0
          };
        });
        return result.sort((a, b) => b.numberOfSubmits - a.numberOfSubmits);
      }
    },

    statisticsMedia: {
      rest: {
        method: "GET",
        path: "/openAICost",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const query = this.extractQueryTime(ctx.params);
        const {isDeveloper} = ctx.params;
        const responses = await this.broker.call("responses.findToDashboard", {
          query,
          isDeveloper
        });
        const mediaResponses = responses.filter(response => ["whisper-1", "tts-1"].includes(response.gptModel));
        const imageResponses = responses.filter(response => response.gptModel === "gpt-4-vision-preview");
        const textResponses = responses.filter(response => !["whisper-1", "tts-1", "gpt-4-vision-preview"].includes(response.gptModel));

        const [mediaCost, imageCost, textCost, shadowingCost] = await Promise.all([
          this.getCostFromGPTModel(mediaResponses),
          this.getCostFromGPTModel(imageResponses),
          this.getCostFromGPTModel(textResponses),
          this.broker.call("exercisesubmissions.calculateShadowingCost", {query: {createdAt: query.createdAt}})
        ]);
        return {
          mediaCost: {
            ...mediaCost,
            minutesPerSubmit: mediaCost.totalMinutes / mediaCost.totalSubmits,
            costPerSubmit: mediaCost.totalCost / mediaCost.totalSubmits,
            promptTokensPerSubmit: mediaCost.promptTokens / mediaCost.totalSubmits,
            completionTokensPerSubmit: mediaCost.completionTokens / mediaCost.totalSubmits,
          },
          imageCost: {
            ...imageCost,
            costPerSubmit: imageCost.totalCost / imageCost.totalSubmits,
            promptTokensPerSubmit: imageCost.promptTokens / imageCost.totalSubmits,
            completionTokensPerSubmit: imageCost.completionTokens / imageCost.totalSubmits,
          },
          textCost: {
            ...textCost,
            costPerSubmit: textCost.totalCost / textCost.totalSubmits,
            promptTokensPerSubmit: textCost.promptTokens / textCost.totalSubmits,
            completionTokensPerSubmit: textCost.completionTokens / textCost.totalSubmits,
          },
          // shadowingCost:{
          //   ...shadowingCost,
          //   numberOfSubmissions: shadowingCost.numberSubmit,
          //   totalSubmits: shadowingCost.numberSubmit
          // }
        };
      }
    },

    statisticFeedback: {
      rest: "GET /statisticFeedback",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        return await ctx.call("userFeedbacks.statisticUserFeedback", ctx.params);
      }
    },
    analysisFeedback: {
      rest: "GET /analysisFeedback",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        return await ctx.call("userFeedbacks.analysisUserFeedback", ctx.params);
      }
    },


    studentToolCost: {
      rest: "GET /studentToolCost",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const query = this.extractQueryTime(ctx.params);
        const studentTools = await ctx.call("inputs.statisticInputStudentTool", {query: {createdAt: query.createdAt}});
        const shadowingCost = await ctx.call("exercisesubmissions.calculateShadowingCost", {query: {createdAt: query.createdAt}});
        const speakingAdvanceCost = await ctx.call("spksessions.speakingAdvanceCost", {query: {createdAt: query.createdAt}});
        return [
          ...studentTools,
          shadowingCost,
          speakingAdvanceCost
        ]
      }
    }
  },

  methods: {
    checkPermission(ctx) {
      const action = ctx.action;
      const {user} = ctx.meta;

      if (user.isSystemAdmin) return

      if (action?.permission && !user.organizationId) {
        throw new MoleculerClientError(i18next.t("permission_denied"), 403)
      }
    },
    getPriceGPTModel(model, gptPrice) {
      const mapModel = gptPrice.reduce((acc, item) => {
        acc[item.gptModel] = item;
        return acc;
      }, {});
      return mapModel[model] || mapModel['tuning-gpt-3.5-turbo'];
    },

    async getCostFromGPTModel(responses) {
      const gptPrice = await this.broker.call('gptmodelprice.find', {});

      return responses.reduce((acc, item) => {
        const {priceInput, priceOutput, tokenUnit, unit} = this.getPriceGPTModel(item.gptModel, gptPrice);

        if (unit === 'minute') {
          if (item.inputData) {
            const duration = (item.inputData.cutEnd - item.inputData.cutStart) / tokenUnit;
            acc.totalMinutes += duration;
            acc.totalCost += duration * priceInput;
            acc.numberOfSubmissions++;
          }
        } else if (item.promptTokens) {
          const cost = (item.promptTokens * priceInput + item.completionTokens * priceOutput) / tokenUnit;
          acc.promptTokens += item.promptTokens;
          acc.completionTokens += item.completionTokens;
          acc.totalTokens += item.totalTokens;
          acc.totalCost += cost;
          acc.totalSubmits++;
          acc.numberOfSubmissions++
        }

        return acc;
      }, {
        totalMinutes: 0,
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0,
        totalSubmits: 0,
        totalCost: 0,
        numberOfSubmissions: 0,
      });
    }
  }
};

