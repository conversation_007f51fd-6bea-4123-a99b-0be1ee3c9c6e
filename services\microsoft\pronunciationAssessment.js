const difflib = require("difflib");
const sdk = require("microsoft-cognitiveservices-speech-sdk");
const _ = require("lodash");
const {getConfig} = require("../../config/config");
const config = getConfig(process.env.NODE_ENV);


module.exports.pronunciationAssessment = async (params) => {
  const {audioStream, socket, callback, inputData, contentFunction} = params
  const {topic} = inputData
  const {serviceRegion, speechKey, confidenceThreshold} = params
  console.log("serviceRegion, speechKey, confidenceThreshold", serviceRegion, speechKey, confidenceThreshold)
  const audioConfig = sdk.AudioConfig.fromStreamInput(audioStream);
  const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey || config.speechKey, serviceRegion || config.serviceRegion);

  speechConfig.speechRecognitionLanguage = "en-US";

  const pronunciationAssessmentConfig = new sdk.PronunciationAssessmentConfig(
    "",
    sdk.PronunciationAssessmentGradingSystem.HundredMark,
    sdk.PronunciationAssessmentGranularity.Phoneme,
    false
  );
  pronunciationAssessmentConfig.enableProsodyAssessment = true;
  pronunciationAssessmentConfig.enableContentAssessmentWithTopic(topic);
  pronunciationAssessmentConfig.phonemeAlphabet = "IPA"


  // create the speech recognizer.
  const reco = new sdk.SpeechRecognizer(speechConfig, audioConfig);
  // (Optional) get the session ID
  // reco.sessionStarted = (_s, e) => {
  //   console.log(`SESSION ID: ${e.sessionId}`);
  // };

  pronunciationAssessmentConfig.applyTo(reco);

  pronunciationAssessmentScore(socket, reco, callback, inputData, confidenceThreshold, topic, contentFunction);

}

function getProsodyError(errorType, word, lastWord, confidenceThreshold = 0.5) {
  const feedbackProsody = word.PronunciationAssessment.Feedback.Prosody;
  if (errorType === "MissingBreak" || errorType === "UnexpectedBreak") {
    const breakErrorInfo = feedbackProsody.Break[errorType] || "null";

    if (breakErrorInfo === "null") {
      return false;
    }

    if (errorType === "MissingBreak") {
      if (breakErrorInfo.Confidence >= confidenceThreshold && (lastWord?.Punctuation || false)) {
        return true;
      }
    }

    if (errorType === "UnexpectedBreak") {
      if (breakErrorInfo.Confidence >= confidenceThreshold && !(lastWord?.Punctuation || false)) {
        return true;
      }
    }

    return false;
  } else if (
    errorType === "Monotone" &&
    feedbackProsody.Intonation.ErrorTypes.includes(errorType)
  ) {
    return true;
  } else {
    return false;
  }
}

function setErrorTypes(word, wordIndex, jsonWords, confidenceThreshold) {
  const lastWord = wordIndex > 0 ? jsonWords[wordIndex - 1] : null;

  if (getProsodyError("MissingBreak", word, lastWord, confidenceThreshold)) {
    word?.PronunciationAssessment?.Feedback?.Prosody?.Break?.ErrorTypes?.push("MissingBreak");
  }
  if (getProsodyError("UnexpectedBreak", word, lastWord, confidenceThreshold)) {
    word.PronunciationAssessment?.Feedback?.Prosody?.Break?.ErrorTypes?.push("UnexpectedBreak");
  }
  if (getProsodyError("Monotone", word, lastWord, confidenceThreshold)) {
    word.PronunciationAssessment?.Feedback?.Prosody?.Break?.ErrorTypes?.push("Monotone");
  }
}

function setPunctuation(jsonWords, displayText) {
  const words = displayText.split(" ");

  words.forEach((word, idx) => {
    if (!word) return;
    const lastChar = word[word.length - 1];
    if (lastChar && !/^[a-zA-Z0-9]$/.test(lastChar) && idx < jsonWords.length) {
      jsonWords[idx]["Punctuation"] = lastChar;
    }
  });

  return jsonWords;
}


function pronunciationAssessmentScore(socket, reco, callback, inputData, confidenceThreshold, topic, contentFunction) {
  let results = [];
  let recognizedText = "";
  const scoreNumber = {
    accuracyScore: 0,
    fluencyScore: 0,
    compScore: 0,
    prosodyScore: 0,
  };
  const allWords = [];
  let currentText = [];
  let contentResult = {};
  let startOffset = 0;
  let recognizedWords = [];
  let fluencyScores = [];
  let prosodyScores = [];
  let durations = [];
  let jo = {};
  const jsonWords = []

  reco.recognizing = function (s, e) {
    const str = "(recognizing) Reason: " + sdk.ResultReason[e.result.reason] + " Text: " + e.result.text;
    // console.log(str);
    socket.send({state: "recognizing", recognizing: e.result.text})
  };

  reco.recognized = function (s, e) {
    // console.log("pronunciation assessment for: ", e.result.text);
    const pronunciationResult = sdk.PronunciationAssessmentResult.fromResult(e.result);

    jo = JSON.parse(e.result.properties.getProperty(sdk.PropertyId.SpeechServiceResponse_JsonResult));

    recognizedText += jo.DisplayText + ' ';
    results.push(jo);

    const nb = jo["NBest"][0];
    startOffset = nb.Words[0].Offset;
    const localtext = _.map(nb.Words, (item) => item.Word.toLowerCase());
    currentText = currentText.concat(localtext);
    fluencyScores.push(nb.PronunciationAssessment.FluencyScore);
    prosodyScores.push(nb.PronunciationAssessment.ProsodyScore);
    const isSucceeded = jo.RecognitionStatus === 'Success';
    const nBestWords = jo.NBest[0].Words;
    jsonWords.push(...nBestWords)

    const durationList = [];
    _.forEach(nBestWords, (word, index) => {
      recognizedWords.push(word);
      durationList.push(word.Duration);
    });
    durations.push(_.sum(durationList));

    socket.send({state: "sentence_score", pronunciationResult, results: jo})
    if (isSucceeded && nBestWords) {
      allWords.push(...nBestWords);
    }
  };

  async function onRecognizedResult() {
    socket.send({state: "recognized_text", recognizedText})
    contentResult = await contentFunction({topic, recognizedText})
    results[results.length - 1].NBest[0].ContentAssessment = contentResult;
    socket.send({state: "content_score", contentResult})
  }

  function calculateOverallPronunciationScore() {
    setPunctuation(jsonWords, recognizedText)
    _.forEach(jsonWords, (word, index) => {
      setErrorTypes(word, index, jsonWords, confidenceThreshold)
    });

    const language = "en-US"
    const resText = currentText.join(" ");
    let wholelyricsArry = [];
    let resTextArray = [];


    let resTextProcessed = (resText.toLocaleLowerCase() ?? "").replace(new RegExp("[!\"#$%&()*+,-./:;<=>?@[^_`{|}~]+", "g"), "").replace(new RegExp("]+", "g"), "");
    let wholelyrics = (recognizedText.toLocaleLowerCase() ?? "").replace(new RegExp("[!\"#$%&()*+,-./:;<=>?@[^_`{|}~]+", "g"), "").replace(new RegExp("]+", "g"), "");
    wholelyricsArry = wholelyrics.split(" ");
    resTextArray = resTextProcessed.split(" ");

    const wholelyricsArryRes = _.map(
      _.filter(wholelyricsArry, (item) => !!item),
      (item) => item.trim()
    );

    const diff = new difflib.SequenceMatcher(null, wholelyricsArryRes, resTextArray);
    const lastWords = [];
    for (const d of diff.getOpcodes()) {
      if (d[0] === "insert" || d[0] === "replace") {
        for (let j = d[3]; j < d[4]; j++) {
          if (allWords && allWords.length > 0 && allWords[j].PronunciationAssessment.ErrorType !== "Insertion") {
            allWords[j].PronunciationAssessment.ErrorType = "Insertion";
          }
          lastWords.push(allWords[j]);
        }
      }
      if (d[0] === "delete" || d[0] === "replace") {
        if (
          d[2] === wholelyricsArryRes.length &&
          !(jo.RecognitionStatus === "Success" || jo.RecognitionStatus === "Failed")
        )
          continue;
        for (let i = d[1]; i < d[2]; i++) {
          const word = {
            Word: wholelyricsArryRes[i],
            PronunciationAssessment: {
              ErrorType: "Omission",
            },
          };
          lastWords.push(word);
        }
      }
      if (d[0] === "equal") {
        for (let k = d[3], count = 0; k < d[4]; count++) {
          lastWords.push(allWords[k]);
          k++;
        }
      }
    }

    let reference_words = wholelyricsArryRes;

    let recognizedWordsRes = []
    _.forEach(recognizedWords, (word) => {
      if (word.PronunciationAssessment.ErrorType === "None") {
        recognizedWordsRes.push(word)
      }
    });

    let compScore = Number(((recognizedWordsRes.length / reference_words.length) * 100).toFixed(0));
    if (compScore > 100) {
      compScore = 100;
    }
    scoreNumber.compScore = compScore;

    const accuracyScores = [];
    _.forEach(lastWords, (word) => {
      if (word && word?.PronunciationAssessment?.ErrorType !== "Insertion") {
        accuracyScores.push(Number(word?.PronunciationAssessment.AccuracyScore ?? 0));
      }
    });
    scoreNumber.accuracyScore = Number((_.sum(accuracyScores) / accuracyScores.length).toFixed(0));

    if (startOffset) {
      const sumRes = [];
      _.forEach(fluencyScores, (x, index) => {
        sumRes.push(x * durations[index]);
      });
      scoreNumber.fluencyScore = _.sum(sumRes) / _.sum(durations);
    }
    scoreNumber.prosodyScore = Number((_.sum(prosodyScores) / prosodyScores.length).toFixed(0));

    const sortScore = Object.keys(scoreNumber).sort(function (a, b) {
      return scoreNumber[a] - scoreNumber[b];
    });
    if (jo.RecognitionStatus === "Success" || jo.RecognitionStatus === "Failed") {
      scoreNumber.pronScore = Number(
        (
          scoreNumber[sortScore["0"]] * 0.4 +
          scoreNumber[sortScore["1"]] * 0.2 +
          scoreNumber[sortScore["2"]] * 0.2 +
          scoreNumber[sortScore["3"]] * 0.2
        ).toFixed(0)
      );
    } else {
      scoreNumber.pronScore = Number((scoreNumber.accuracyScore * 0.5 + scoreNumber.fluencyScore * 0.5).toFixed(0));
    }
    const submitData = {
      ...inputData,
      vocabularyScore: contentResult.vocabularyScore,
      grammarScore: contentResult.grammarScore,
      topicScore: contentResult.topicScore,
      pronScore: scoreNumber.pronScore,
      fluencyScore: scoreNumber.fluencyScore,
      prosodyScore: scoreNumber.prosodyScore,
      accuracyScore: scoreNumber.accuracyScore,
      ...contentResult,
      recognizedText,
      results,
      overallPronunciationScore: scoreNumber
    }
    const total = submitData.vocabularyScore + submitData.grammarScore + submitData.pronScore + submitData.fluencyScore;

    const average = total / 4;

    submitData.overallScore = Math.round(average * 2) / 2;
    socket.send({state: "overall_score", overallPronunciationScore: {...scoreNumber, ...contentResult}, results})
    callback(submitData)
  }

  reco.canceled = function (s, e) {
    if (e.reason === sdk.CancellationReason.Error) {
      const str = "(cancel) Reason: " + sdk.CancellationReason[e.reason] + ": " + e.errorDetails;
      // console.log(str);
      socket.send({state: "error", error: sdk.CancellationReason[e.reason]})
    }
    reco.stopContinuousRecognitionAsync();
  };

  reco.sessionStopped = async function (s, e) {
    reco.stopContinuousRecognitionAsync();
    reco.close();
    await onRecognizedResult();
    calculateOverallPronunciationScore()
  };

  reco.startContinuousRecognitionAsync();
}
