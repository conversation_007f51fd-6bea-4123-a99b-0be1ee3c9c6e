const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {ERROR_REPORTS, USER, FILE,} = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  userId: {type: Schema.Types.ObjectId, required: true, ref: USER},
  imageIds: [
    {type: Schema.Types.ObjectId, required: true, ref: FILE},
  ],
  videoId: {type: Schema.Types.ObjectId, ref: FILE},
  description: {type: String},
  impactLevel: {type: String, enum: ['low', 'medium', 'high'], default: 'low'},
  errorUrl: {type: String},
  notified: {type: Boolean, default: false},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.index({userId: 1});
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ERROR_REPORTS, schema, ERROR_REPORTS);

