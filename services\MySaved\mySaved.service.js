const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./mySaved.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "mySaved",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "userId": 'users.get',
      "projectId": 'projects.get',
      "folderId": 'folders.get',
    },
    populateOptions: ["projectId.folderId", "projectId.ownerId", "folderId", "folderId.ownerId", "userId"]
  },

  hooks: {
    before: {
      "save|unsave": "checkFolderOrProjectExist",
    }
  },

  actions: {
    save: {
      rest: {
        method: "POST",
        path: "/save",
      },
      auth: "required",
      async handler(ctx) {
        const userId = ctx.meta.user?._id;
        const { projectId, folderId } = ctx.params;
        const existingRecord = await this.adapter.findOne({ userId, projectId, folderId });

        if (existingRecord) {
          return this.adapter.updateById(existingRecord._id, { $set: { userId, projectId, folderId } });
        } else {
          return this.adapter.insert({ userId, projectId, folderId });
        }
      }
      // async handler(ctx) {
      //   const { userId, projectId, folderId } = ctx.params;
      //   const isProject = !!projectId;
      //   const entity = isProject ? "projects" : "folders";
      //   const { ownerId } = await ctx.call(`${ entity }.get`, { id: isProject ? projectId : folderId });
      //   if (ownerId === userId.toString()) {
      //     return await this.adapter.insert({ userId, projectId, folderId });
      //   }
      //   const { _id: newId } = await ctx.call(`${ entity }.copy`, { [isProject ? "projectId" : "folderId"]: isProject ? projectId : folderId });
      //   return await this.adapter.insert({ userId, [isProject ? "projectId" : "folderId"]: newId });
      // }
    },
    unsave: {
      rest: {
        method: "POST",
        path: "/unsave",
      },
      auth: "required",
      async handler(ctx) {
        const userId = ctx.meta.user?._id;
        const { projectId, folderId } = ctx.params;
        const starred = await this.adapter.findOne({ userId, projectId, folderId });
        await this.adapter.removeMany({ userId, projectId, folderId });
        return starred;
      }
    },
    allSaved: {
      rest: {
        method: "GET",
        path: "/all",
      },
      auth: "required",
      async handler(ctx) {
        const userId = ctx.meta.user?._id;
        const query = { userId, isDeleted: false };
        const sort = "-updatedAt";
        const limit = ctx.params.limit ? +ctx.params.limit : undefined;
        const mySaved = await ctx.call("mySaved.find", { query, sort, limit });

        const folderIds = mySaved
          .filter(({ folderId }) => folderId)
          .map(({ folderId }) => folderId._id.toString());

        const allProjects = await ctx.call("projects.find", {
          query: { folderId: { $in: folderIds }, isDeleted: false },
          populate: []
        });

        const projectGroupByFolder = this.groupBy(allProjects, "folderId");

        return mySaved.filter(({ projectId, folderId }) => {
          if (folderId) {
            folderId.projects = projectGroupByFolder[folderId._id.toString()]?.length || 0;
          }
          return folderId || (projectId && !folderIds.includes(projectId?.folderId?._id.toString()));
        });
      }
    },
    removeMany: {
      rest: {
        method: "POST",
        path: "/removeMany",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { projectIds, userId, folderIds } = ctx.params;
        if (projectIds) {
          await this.adapter.removeMany({ projectId: projectIds, userId });
        }
        if (folderIds) {
          await this.adapter.removeMany({ folderId: folderIds, userId });
        }
        return { success: true };
      }
    }
  },
  methods: {
    async checkFolderOrProjectExist(context) {
      const { projectId, folderId } = context.params;

      const folder = folderId ? await context.call("folders.get", { id: folderId }) : {};
      if (folder.isDeleted) {
        throw new MoleculerClientError(i18next.t("folder_was_deleted"), 404);
      }

      const project = projectId ? await context.call("projects.get", { id: projectId }) : {};
      if (project.isDeleted) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
      }
    }
  },
  events: {
    async "share.removeShared"(payload) {
      const promises = payload.map(({ userId, projectId, folderId }) => {
        return this.adapter.removeMany({ userId, projectId, folderId });
      });
      await Promise.all(promises);
    },
    async "folder.deleted"(payload, sender, event, ctx) {
      return this.adapter.removeMany({ folderId: payload });
    },
    async "project.deleted"(payload, sender, event, ctx) {
      return this.adapter.removeMany({ projectId: payload });
    },
    async removeProject({ projectId, userId }) {
      return await this.adapter.removeMany({ projectId, userId });
    },
  },
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {

  },
  async afterDisconnected() {
  },
};
