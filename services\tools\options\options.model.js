const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {OPTIONS, INSTRUCTION, KNOWLEDGE} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    instructionId: {type: Schema.Types.ObjectId, ref: INSTRUCTION},
    code: {type: String, required: true},
    name: {type: String},
    type: String, // text, textarea, number, select
    rule: String, // required, min, max
    placeholder: String,
    defaultValue: String,
    selectOptions: [{
      label: {type: Schema.Types.Mixed},
      value: String
    }],
    instruction: String,
    localization: {
      name: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      }
    },
    knowledgeIds: [{type: Schema.Types.ObjectId, ref: KNOWLEDGE}],
    isExamOption: {
      type: Boolean,
      default: false
    },
    optionTypes: [{
      type: String,
      enum: ['EXAM_SCHOOL', "EXAM_IELTS", "MARK_TEST_SCHOOL", "MARK_TEST_IELTS", 'NORMAL'],
    }],
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(OPTIONS, schema, OPTIONS);
