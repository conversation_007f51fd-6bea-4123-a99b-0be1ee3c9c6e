"use strict";

const projectModel = require("./project.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {PERMISSION_ACCESS} = require("../../constants/constant.js");
const {IMAGE_SERVICE} = require("../Image");
const i18next = require("i18next");
const {INPUT_TYPE, DOCUMENT_TYPE} = require("../../constants/constant");
const {MoleculerClientError} = require("moleculer").Errors;

const {ObjectId} = require("mongoose").Types;

const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");


module.exports = {
  name: "projects",
  mixins: [DbMongoose(projectModel), BaseService, FunctionsCommon, AuthRole],
  settings: {
    populates: {
      "ownerId": "users.get",
      "folderId": "folders.get",
      "imageId": "images.get",
    },
    populateOptions: ["ownerId", "folderId", "imageId"]
  },
  dependencies: [],
  hooks: {
    before: {
      "*": "checkPermission",
      "create|update|remove|copy|move|createContentBlock|createFromTemplate|createContentFromTemplate|permissionAccess": "checkProjectOrFolderExist",
      "createFromTool|insertContentBlock|createContentBlock": "checkToolPermission",
    },
    after: {
      "*": "activityLogger",
      async list(ctx, res) {
        await this.checkStarred(ctx, res.rows);
        return res;
      },
      getAllWithoutPagination(ctx, res) {
        this.checkStarred(ctx, res);
        const {folderId} = ctx.params.query;

        return folderId ? this.checkShared(ctx, res) : res;
      }
    }
  },
  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    },
    upload: {
      // auth: "required",
      // activityLogger: true,
      async handler(ctx) {
        try {
          const {projectId} = ctx.meta.$multipart;
          ctx.meta.bypassCapacityAddition = true;
          const image = await ctx.call(IMAGE_SERVICE.upload, ctx.params, {meta: ctx.meta});
          const projectUpdated = await this.adapter.updateById(projectId, {imageId: image?._id});
          return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, projectUpdated);
        } catch (e) {
          console.log(e)
          throw new MoleculerClientError(e.message, 500);
        }
      }
    },

    create: {
      rest: {
        method: "POST",
        path: "/"
      },
      auth: "required",
      params: {},
      activityLogger: true,
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        const entity = ctx.params;
        entity.ownerId = ctx.meta.user?._id?.toString();
        entity.lastModified = Date.now();

        if (!entity.workspaceId && !entity.folderId) {
          const workspaces = await ctx.call('workspaces.find', {
            query: {userId: ctx.meta.user?._id, isDeleted: false},
            limit: 1
          });
          entity.workspaceId = workspaces[0]?._id;
        } else {
          if (entity.folderId) {
            const folder = await ctx.call('folders.get', {id: entity.folderId});
            entity.workspaceId = folder.workspaceId;
          }
          const [workspaces] = await ctx.call('workspaces.find', {query: {_id: entity.workspaceId, isDeleted: false}});
          if (workspaces?.type === "ORGANIZATIONAL") {
            const organization = await ctx.call('organizations.getOne', {id: workspaces.organizationId});
            if (organization?.active === false) {
              throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
            }
          }
        }

        if (entity.examCode) {
          entity.commonOptions = {
            grade: entity.examCode
          };
        }

        await this.validateEntity(entity);

        const dataRes = await this.adapter.insert(entity);

        ctx.emit('project.created', {projectId: dataRes._id});

        return dataRes;
      }
    },
    update: {
      rest: {
        method: "PUT",
        path: "/:id"
      },
      permission: PERMISSION_ACCESS.EDITOR,
      params: {},
      auth: "required",
      activityLogger: true,
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        const {id} = ctx.params;
        const entity = ctx.params;
        entity.isDraft = false;
        entity.lastModified = Date.now();
        return await this.adapter.updateById(id, entity);
      }
    },
    createFromTool: {
      rest: "POST /createFromTool",
      params: {
        toolId: {type: "string"},
      },
      auth: "required",
      async handler(ctx) {
        try {
          const {toolId, folderId, projectName} = ctx.params;
          let {workspaceId} = ctx.params;
          const {lang} = ctx.meta
          if (!workspaceId) {
            const workspaces = await ctx.call('workspaces.find', {
              query: {userId: ctx.meta.user?._id, isDeleted: false}
            });

            workspaceId = workspaces?.[0]?._id;
          }
          const tool = await ctx.call('tools.get', {id: toolId.toString()});

          const ownerId = ctx.meta.user?._id;

          let defaultName = tool.localization?.name[lang] || tool.name
          switch (tool.inputType) {
            case INPUT_TYPE.MARK_TEST_TASK_1:
            case INPUT_TYPE.MARK_TEST_TASK_2:
            case INPUT_TYPE.STUDENT_TASK_1:
            case INPUT_TYPE.SearchFilter:
              defaultName = "Untitled essay";
              break;
            case INPUT_TYPE.AUDIO_STREAM:
            case INPUT_TYPE.STUDENT_SPEAKING:
              defaultName = "Untitled speech";
              break;
          }

          const newProject = {
            projectName: projectName || defaultName,
            folderId,
            workspaceId,
            ownerId,
            type: tool.type,
          };
          newProject.isDraft = tool.inputType !== INPUT_TYPE.AUDIO_STREAM
          const dataRes = await this.adapter.insert(newProject);

          ctx.emit('project.createdFromTool', {
            toolId,
            projectId: dataRes._id,
            contentIndex: 1,
          });

          ctx.emit('project.created', {projectId: dataRes._id});

          return dataRes;
        } catch (error) {
          // console.error(error);
          if (error instanceof MoleculerClientError) {
            throw error;
          }
          return error;
        }
      }
    },
    createWritingFromTool: {
      rest: "POST /createWritingFromTool",
      params: {
        toolId: {type: "string"},
      },
      auth: "required",
      async handler(ctx) {
        try {
          const {toolId, folderId, projectName} = ctx.params;
          let {workspaceId} = ctx.params;
          const {lang} = ctx.meta
          if (!workspaceId) {
            const workspaces = await ctx.call('workspaces.find', {
              query: {userId: ctx.meta.user?._id, isDeleted: false}
            });

            workspaceId = workspaces?.[0]?._id;
          }
          const tool = await ctx.call('tools.get', {id: toolId.toString()});

          const ownerId = ctx.meta.user?._id;

          const newProject = {
            projectName: projectName || tool.localization?.name[lang] || tool.name,
            folderId,
            workspaceId,
            ownerId,
            type: tool.type,
          };
          newProject.isDraft = tool.inputType !== INPUT_TYPE.AUDIO_STREAM
          const dataRes = await this.adapter.insert(newProject);

          const contentEntity = {
            toolId,
            projectId: dataRes._id,
            contentIndex: 1,
          }

          const {contentTitle, localization} = tool
          const title = localization?.contentTitle?.[lang] || contentTitle || i18next.t("response_of_content")
          const content = await ctx.call('contents.insert', {entity: {...contentEntity, title}})
          ctx.emit('project.created', {projectId: dataRes._id});
          const dataTransformed = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, dataRes);
          return {...dataTransformed, contentId: content._id};

        } catch (error) {
          console.error(error);
          return error;
        }
      }
    },
    createContentBlock: {
      rest: "PUT /:id/createContentBlock",
      params: {
        toolId: {type: "string", required: true},
      },
      permission: 'EDITOR',
      auth: "required",
      async handler(ctx) {
        const {id, toolId, inputData, inputType} = ctx.params;
        const {lang} = ctx.meta;
        const contentIndexCurrent = await ctx.call('contents.count', {query: {projectId: id, isDeleted: false}});
        const {contentTitle, localization} = await this.broker.call('tools.get', {id: toolId});

        const contentObj = {
          projectId: id,
          toolId,
          contentIndex: contentIndexCurrent + 1,
          title: localization?.contentTitle?.[lang] || contentTitle || i18next.t("response_of_content"),
          lastInput: {
            inputData,
            inputType
          }
        };

        const contentRes = await ctx.call('contents.insert', {entity: contentObj});

        if (inputData) {
          const inputObj = {
            contentId: contentRes._id,
            inputData,
            inputType,
            toolId,
          };
          await ctx.call('inputs.insert', {entity: inputObj});
        }

        return await ctx.call('contents.get', {id: contentRes._id, populate: ['toolId']});
      }
    },
    insertContentBlock: {
      rest: "PUT /:id/insertContentBlock",
      permission: 'EDITOR',
      auth: "required",
      async handler(ctx) {
        const {id, toolId, inputData, inputType, contentIndex = 1} = ctx.params;
        const {lang} = ctx.meta;
        let title = i18next.t("response_of_content");
        if (toolId) {
          const {contentTitle, localization} = await this.broker.call('tools.get', {id: toolId});
          title = localization?.contentTitle?.[lang] || contentTitle || title;
        }

        const contentObj = {
          projectId: id,
          toolId,
          contentIndex,
          title,
          lastInput: {
            inputData,
            inputType
          }
        };

        await ctx.call('contents.updateMany', {
          query: {projectId: id, contentIndex: {$gte: contentIndex}},
          update: {$inc: {contentIndex: 1}},
        });

        const contentRes = await ctx.call('contents.insert', {entity: contentObj});

        if (inputData) {
          const inputObj = {
            contentId: contentRes._id,
            inputData,
            inputType,
            toolId,
          };
          await ctx.call('inputs.insert', {entity: inputObj});
        }

        return await ctx.call('contents.get', {id: contentRes._id, populate: ['toolId']});
      }
    },
    copy: {
      rest: {
        method: "POST",
        path: "/copy",
      },
      permission: 'VIEWER',
      auth: "required",
      activityLogger: true,
      async handler(ctx) {
        const {projectId} = ctx.params;
        const myWorkspaces = await ctx.call('workspaces.find', {
          query: {userId: ctx.meta.user?._id, isDeleted: false},
        });

        const oldProject = await this.adapter.findOne({_id: projectId});
        const checkOwner = oldProject.ownerId.toString() === ctx.meta?.user?._id.toString();
        const {
          folderId,
          projectName,
          description,
          imageId,
          type,
          examCode,
          commonOptions,
          activeExam,
          numberOfExams
        } = oldProject;

        const projectCopy = {
          projectName: `${projectName} (Copy)`,
          ownerId: ctx.meta?.user?._id,
          lastModified: Date.now(),
          workspaceId: myWorkspaces[0]._id,
          description, imageId, type,
          examCode,
          commonOptions,
          activeExam,
          numberOfExams
        };

        if (checkOwner) {
          projectCopy.folderId = folderId;
          projectCopy.workspaceId = oldProject.workspaceId;
        }
        const newProject = await this.adapter.insert(projectCopy);

        ctx.emit('project.created', {projectId: newProject._id});
        ctx.emit('project.copied', {oldProject, newProject});

        return this.transformDocuments(ctx, {populate: ['ownerId', 'folderId', 'imageId']}, newProject);
      }
    },
    move: {
      rest: {
        method: "PUT",
        path: "/:id/move",
      },
      auth: "required",
      permission: 'EDITOR',
      activityLogger: true,
      async handler(ctx) {
        const entity = ctx.params;
        entity.lastModified = Date.now();
        // const { workspaceId } = await ctx.call('folders.get', { id: entity.folderId });
        // entity.workspaceId = workspaceId;
        const project = await ctx.call("projects.get", {id: entity.id});
        ctx.params.oldFolderId = project?.folderId;

        const afterMove = await this.adapter.updateById(entity.id, entity);
        ctx.emit('project.moved', {projectId: entity.id});

        return this.transformDocuments(ctx, {populate: ['ownerId', "folderId"]}, afterMove);
      }
    },
    details: {
      rest: {
        method: "GET",
        path: "/:id/details",
      },
      auth: "required",
      permission: 'VIEWER',
      async handler(ctx) {
        const {id} = ctx.params;
        const userId = ctx.meta?.user?._id;
        const [project, content] = await Promise.all([
          this.adapter.findOne({_id: id, isDeleted: false}),
          ctx.call('contents.details', {projectId: id})
        ]);
        if (!project) {
          throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
        }
        const transformedProject = await this.transformDocuments(ctx, {populate: ['folderId', 'ownerId']}, project);
        ctx.emit('lastViewedProject', {id});
        this.broker.emit('recentProject', {projectId: id, userId});
        const mySavedCount = await ctx.call('mySaved.count', {query: {projectId: id, userId}});
        return {...transformedProject, content, isSaved: mySavedCount > 0, permission: ctx.params.permission};
      }
    },
    remove: {
      rest: "DELETE /:id",
      auth: "required",
      params: {
        id: "string",
      },
      // permission: 'EDITOR',
      activityLogger: true,
      role: USER_CODES.NORMAL,
      /** @param {Context} ctx */
      async handler(ctx) {
        const {id} = ctx.params;
        const userId = ctx.meta.user?._id;
        const project = await this.adapter.findOne({_id: id});
        const isOwner = project?.ownerId?.toString() === userId;

        const workspace = await ctx.call('workspaces.get', {id: project?.workspaceId?.toString()});
        if (workspace?.type === "ORGANIZATIONAL") {
          const organization = await ctx.call('organizations.getOne', {id: workspace?.organizationId});
          if (!organization) {
            throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
          }
          if (organization?.active === false) {
            throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403);
          }
        }

        if (!isOwner) {
          await this.broker.call('share.unshare', {projectId: id, userId});
          await this.broker.emit('removeProject', {projectId: id, userId});

          if (project.folderId) {
            const folders = await ctx.call('folders.get', {id: project.folderId.toString()});
            const isMyFolder = folders?.ownerId?.toString() === userId;
            if (isMyFolder) {
              return await this.adapter.updateById(id, {isDeleted: true}, {new: true});
            }
          }
          return project;
        }


        const dataRes = await this.adapter.updateById(id, {isDeleted: true}, {new: true});
        await ctx.emit('project.deleted', id);
        return dataRes;
      }
    },

    permissionAccess: {
      rest: {
        method: "GET",
        path: "/:id/permission",
      },
      auth: "required",
      async handler(ctx) {
        const {id} = ctx.params;
        const {user} = ctx.meta;
        let project;
        try {
          project = await this.adapter.findOne({_id: id});
        } catch (e) {
          console.error(e);
        }
        if (!project) {
          throw new MoleculerClientError(i18next.t("error_project_not_found"), 404);
        }
        const ownerId = project?.ownerId?.toString();
        const userId = ctx.meta.user?._id.toString();

        // system admin can access all projects
        if (user?.isSystemAdmin) {
          return PERMISSION_ACCESS.OWNER;
        }
        let isLock = false;
        const workspace = await ctx.call('workspaces.get', {id: project.workspaceId.toString()});
        if (workspace?.type === "ORGANIZATIONAL") {
          const organization = await ctx.call('organizations.getOne', {id: workspace.organizationId}) || {};
          isLock = organization?.active === false;
        }

        if (ownerId === userId) {
          if (isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.OWNER;
        }

        if (user?.role !== "normal" && user?.organizationId?.toString() === workspace?.organizationId?.toString()) {
          if (isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.EDITOR;
        }

        const [shared, generalAccess] = await Promise.all([
          this.broker.call('share.find', {query: {projectId: id, isDeleted: false, userId}}),
          this.broker.call('generalAccess.find', {
            query: {projectId: id, isDeleted: false},
            populate: []
          })
        ]);
        const sharedPermissions = this.checkSharedPermission(shared);
        const generalPermissions = this.checkGeneralAccessPermission(ctx.meta.user, generalAccess);

        if (sharedPermissions === PERMISSION_ACCESS.NO_PERMISSION && [PERMISSION_ACCESS.EDITOR, PERMISSION_ACCESS.VIEWER].includes(generalPermissions)) {
          ctx.call('share.insert', {
            entity: {
              projectId: id,
              userId,
              permission: generalPermissions,
              type: "PROJECT",
              isGeneral: true
            }
          });
        }

        const combinedPermissions = [sharedPermissions, generalPermissions];
        if (combinedPermissions.includes(PERMISSION_ACCESS.EDITOR)) {
          if (isLock) {
            return PERMISSION_ACCESS.VIEWER;
          }
          return PERMISSION_ACCESS.EDITOR;
        }
        if (combinedPermissions.includes(PERMISSION_ACCESS.VIEWER)) {
          return PERMISSION_ACCESS.VIEWER;
        }
        return PERMISSION_ACCESS.NO_PERMISSION;
      }
    },
    createFromTemplate: {
      rest: {
        method: "POST",
        path: "/createFromTemplate",
      },
      auth: "required",
      async handler(ctx) {
        const {templateId, projectName, folderId} = ctx.params;
        const {_id: userId} = ctx.meta?.user;
        const [template, workspace] = await Promise.all([
          ctx.call('templates.get', {id: templateId, isDeleted: false}),
          ctx.call('workspaces.find', {query: {userId, isDeleted: false}})
        ]);

        if (template?.type === "ORGANIZATION") {
          const organization = await ctx.call('organizations.getOne', {id: template.organizationId});
          if (organization?.active === false) {
            throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
          }
        }

        const project = await this.adapter.findById(template.projectId.toString());

        const newProjectObj = {
          projectName: projectName || template.name,
          type: project.type,
          examCode: template.folderCode,
          ownerId: userId,
          folderId,
          lastModified: Date.now(),
          workspaceId: workspace[0]._id,
          description: project.description,
          activeExam: template?.projectDetail?.activeExam,
          numberOfExams: template?.projectDetail?.numberOfExams,
          commonOptions: template?.projectDetail?.commonOptions
        };

        const newProject = await this.adapter.insert(newProjectObj);

        const projectDetail = template.projectDetail || {};
        const contents = projectDetail.contents || [];

        await Promise.all(contents.map(content =>
          ctx.call('contents.createFromTemplate', {
            ...content,
            projectId: newProject._id
          })
        ));

        return await ctx.call('projects.details', {id: newProject._id});
      }
    },

    createContentFromTemplate: {
      rest: {
        method: "PUT",
        path: "/:projectId/createContentsFromTemplate",
      },
      auth: "required",
      async handler(ctx) {
        const {projectId, templateId} = ctx.params;
        const templateDetail = await ctx.call('templates.get', {id: templateId, isDeleted: false});
        const {projectDetail} = templateDetail;
        const promises = projectDetail.contents?.map(content => {
          return ctx.call('contents.createFromTemplate', {
            ...content,
            projectId
          });
        });
        await Promise.all(promises);
        return await ctx.call('projects.details', {id: projectId});
      }
    },

    statisticProject: {
      rest: {
        method: "GET",
        path: "/statisticProject",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        return this.countProjectByUser(ctx.params.query);
      }
    },

    removeThumbnail: {
      rest: {
        method: "DELETE",
        path: "/:projectId/removeThumbnail",
      },
      auth: "required",
      async handler(ctx) {
        const {projectId} = ctx.params;
        return this.adapter.updateById(projectId, {imageId: null}, {new: true});
      }
    },

    getAllProjectByStudent: {
      rest: "GET /getAllProjectByStudent",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const query = ctx.params.query;
        const parsedQuery = query ? JSON.parse(query) : {}
        const {tag, documentType, inputType, category} = parsedQuery;

        const paramsList = this.extractParamsPage(ctx.params);

        const [userProjects, sharedProjects] = await Promise.all([
          ctx.call('projects.find', {
            query: {ownerId: user._id, isDeleted: false}, populate: [], fields: ["_id"]
          }),
          ctx.call('share.find', {
            query: {userId: user._id, type: "PROJECT"}, populate: [], fields: ["projectId"]
          }),
        ]);

        const userProjectIds = userProjects.map(project => ObjectId(project._id));
        const sharedProjectIds = sharedProjects.map(project => ObjectId(project.projectId));

        const projectIds = documentType
          ? documentType === DOCUMENT_TYPE.MY_FILE
            ? userProjectIds
            : sharedProjectIds
          : [...userProjectIds, ...sharedProjectIds];

        const contentData = await ctx.call("contents.studentDetails", {projectIds, inputType});
        const contentMap = new Map();
        contentData.forEach(content => {
          contentMap.set(content.projectId.toString(), content);
        });
        const allProjectIds = contentData.map(content => ObjectId(content.projectId));

        const conditions = [];
        if (tag) conditions.push({tag: {$elemMatch: {$in: tag.split(",").map(type => type.trim())}}});
        if (category) conditions.push({category: {$elemMatch: {$in: category.split(",").map(type => type.trim())}}});

        const params = {
          ...paramsList,
          query: JSON.stringify({
            _id: {$in: allProjectIds},
            isDeleted: false,
            ...(conditions.length > 0 && {$or: conditions})
          }),
        }

        const allProject = await ctx.call("projects.list", params);
        allProject.rows = allProject.rows.map(project => {
          return {
            ...project,
            content: contentMap.get(project._id.toString())
          }
        });
        return allProject;
      }
    },
    studentProjectTagCategories: {
      rest: "GET /studentProjectTagCategories",
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const query = ctx.params.query;
        const parsedQuery = query ? JSON.parse(query) : {}
        const {type} = parsedQuery;

        const [userProjects, sharedProjects] = await Promise.all([
          ctx.call('projects.find', {
            query: {ownerId: user._id, isDeleted: false}, populate: [], fields: ["_id"]
          }),
          ctx.call('share.find', {
            query: {userId: user._id, type: "PROJECT"}, populate: [], fields: ["projectId"]
          }),
        ]);

        const userProjectIds = userProjects.map(project => ObjectId(project._id));
        const sharedProjectIds = sharedProjects.map(project => ObjectId(project.projectId));

        const projectIds = [...userProjectIds, ...sharedProjectIds];

        if (type === "writing") {
          const [writingTask1, writingTask2] = await Promise.all([
            this.getTagAndCategory(projectIds, `${INPUT_TYPE.MARK_TEST_TASK_1}, ${INPUT_TYPE.STUDENT_TASK_1}`),
            this.getTagAndCategory(projectIds, `${INPUT_TYPE.MARK_TEST_TASK_2}, ${INPUT_TYPE.STUDENT_TASK_2}`)
          ]);
          return {
            "writing_1": writingTask1,
            "writing_2": writingTask2
          }
        } else if (type === "speaking") {
          const speaking = await this.getTagAndCategory(projectIds, `${INPUT_TYPE.AUDIO_STREAM}, ${INPUT_TYPE.STUDENT_SPEAKING}`);
          return {"speaking": speaking};
        } else {
          throw new MoleculerClientError(i18next.t("error_project_type_not_found"), 500);
        }
      }
    }
  },

  events: {

    listenProject: {
      params: {
        projectId: "string",
      },
      async handler(context) {
        const {projectId, responses} = context.params;

        if (responses) {
          await this.handleStreamMarkTest(responses, projectId)
        } else {
          context.emit(`sse.${projectId}`, {state: "processing"});
        }

      },
    },

    lastModifiedProject: {

      async handler(ctx) {
        const {id} = ctx.params;
        return this.adapter.updateById(id, {
          lastModified: new Date(),
          lastModifiedBy: ctx.meta.user?._id
        }, {new: true});
      }
    },
    lastViewedProject: {

      async handler(ctx) {
        const {id} = ctx.params;
        return this.adapter.updateById(id, {
          lastViewed: new Date(),
          lastViewedBy: ctx.meta.user?._id
        }, {new: true});
      }
    },

    async "folder.copied"(payload, sender, event, ctx) {
      await this.folderCopied(payload, sender, event, ctx);
    },
    async "folder.deleted"(payload, sender, event, context) {
      this.logger.info("payload", payload, sender, event);
      const projectQuery = {folderId: payload, isDeleted: false};
      const projects = await this.adapter.find({query: projectQuery});
      const projectsDelete = projects.map(project => {
        return {
          action: 'projects.remove',
          params: {id: project._id?.toString()}
        };
      });
      context.mcall(projectsDelete);
    }
  },

  methods: {

    async handleStreamMarkTest(responses, projectId) {
      async function checkStatus(responses, checkFunction, interval = 500) {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              let allDone = true;
              for (let record of responses) {
                const state = await checkFunction(record);
                if (state === 'processing') {
                  allDone = false;
                }
              }

              if (allDone) {
                clearInterval(intervalId);
                resolve('Done');
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      }


      const res = await checkStatus(responses, this.checkFunction)
      if (res) {
        const allResponses = await this.broker.call("responses.find", {
          query: {
            _id: {$in: responses.map(response => response._id)},
            isDeleted: false
          },
        });
        await this.broker.emit(`sse.${projectId}`, {state: "done", responses: allResponses});
      }
    },
    async checkFunction(record) {
      const response = await this.broker.call("responses.get", {id: record._id});
      return response.state;
    },


    async checkPermission(context) {
      const {action, params} = context;
      const {id, projectId} = params;

      if (action?.permission) {
        const permissionAccess = await context.call('projects.permissionAccess', {id: id || projectId});

        const {permission} = action;

        const isEditAccess = permission === PERMISSION_ACCESS.EDITOR;
        const isViewAccess = permission === PERMISSION_ACCESS.VIEWER;
        const noEditAccess = isEditAccess && !this.editAccess(permissionAccess);
        const noViewAccess = isViewAccess && !this.hasViewAccess(permissionAccess);

        if (noEditAccess || noViewAccess) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }

        context.params.permission = permissionAccess;
      }
    },
    async folderCopied(payload, sender, event, ctx) {
      const projectQuery = {folderId: payload?.oldFolderId, isDeleted: false};
      const projects = await this.adapter.find({query: projectQuery});

      const copyProjects = projects.map(project => ({
        action: 'projects.copy',
        params: {projectId: project._id}
      }));

      const mcallProjects = await ctx.mcall(copyProjects);

      const updateProjects = mcallProjects.map(project => ({
        action: 'projects.update',
        params: {
          id: project._id,
          folderId: payload.newFolder._id,
          ownerId: payload.newFolder.ownerId,
          workspaceId: payload.newFolder.workspaceId
        }
      }));

      return ctx.mcall(updateProjects);
    },
    async checkStarred(ctx, projects) {
      const projectIds = projects.map(project => project._id);
      const projectStarreds = await ctx.call('mySaved.find', {
        query: {
          projectId: {$in: projectIds},
          userId: ctx.meta.user?._id
        },
        populate: []
      });

      const starredMap = this.createStarredMap(projectStarreds);

      this.markProjectsAsSaved(projects, starredMap);

      return projects;
    },

    createStarredMap(projectStarreds) {
      return projectStarreds.reduce((map, project) => {
        map[project.projectId] = project._id;
        return map;
      }, {});
    },

    markProjectsAsSaved(projects, starredMap) {
      projects.forEach(project => {
        project.isSaved = !!starredMap[project._id];
      });
    },
    async checkShared(ctx, data) {
      const folderDetail = await ctx.call('folders.get', {id: ctx.params.query.folderId});
      const isFolderOwner = folderDetail.ownerId.toString() === ctx.meta.user._id;

      if (isFolderOwner || ctx.meta.user.role !== "normal") {
        return data;
      }

      const projectIds = data.map(project => project._id);
      const [projectShareds, projectGeneralAccess] = await Promise.all([
        ctx.call('share.find', {
          query: {
            projectId: {$in: projectIds},
            userId: ctx.meta.user._id
          },
          populate: []
        }),
        ctx.call('generalAccess.find', {
          query: {
            projectId: {$in: projectIds},
            $or: [
              {userId: ctx.meta.user._id},
              {organizationId: ctx.meta.user.organizationId}
            ]
          },
          populate: []
        })
      ]);

      return data.filter(project =>
        projectShareds.some(item => item.projectId.toString() === project._id.toString()) ||
        projectGeneralAccess.some(item => item.projectId.toString() === project._id.toString()) ||
        project.ownerId._id.toString() === ctx.meta.user._id
      );
    },

    checkSharedPermission(shared) {
      if (!shared.length) return PERMISSION_ACCESS.NO_PERMISSION;
      if (shared.some(item => item.permission === PERMISSION_ACCESS.EDITOR)) return PERMISSION_ACCESS.EDITOR;
      return PERMISSION_ACCESS.VIEWER;
    },

    checkGeneralAccessPermission(user, generalAccess) {
      const typeAccess = generalAccess[0]?.typeAccess;
      if (typeAccess === "ANYONE_WITH_LINK") {
        return generalAccess[0]?.permission;
      } else if (typeAccess === "ORGANIZATIONAL") {
        return this.checkOrganizationPermission(user, generalAccess[0]);
      } else {
        return PERMISSION_ACCESS.NO_PERMISSION;
      }
    },

    checkOrganizationPermission(user, generalAccess) {
      const userOrgId = user.organizationId?.toString();
      const genOrgId = generalAccess.organizationId.toString();
      if (userOrgId && userOrgId === genOrgId) {
        return generalAccess.permission;
      }
      return PERMISSION_ACCESS.NO_PERMISSION;
    },

    countProjectByUser(query) {
      query.isDraft = false;
      query.isDeleted = false;

      return projectModel.aggregate([
        {$match: query},
        {
          $group: {
            _id: '$ownerId',
            numberProjects: {
              $sum: 1,
            },
          },
        },
      ]);
    },

    async activityLogger(context, res) {
      const {action, params} = context;

      if (action.activityLogger && res && params?.workspaceId) {
        const workspace = await context.call('workspaces.get', {
          id: params?.workspaceId.toString(),
          isDeleted: false
        });
        if (workspace.type === "ORGANIZATIONAL") {
          const metadata = {};
          switch (action.rawName) {
            case 'copy':
              metadata.copyProjectId = params?.projectId;
              break;
            case 'move':
              metadata.oldFolderId = params?.oldFolderId;
              break;
          }
          context.emit('activities.logger', {
            metadata,
            folderId: params?.folderId || res.folderId?._id || res?.folderId,
            projectId: res._id,
            action: action.rawName
          });
        }
      }
      return res;
    },
    async checkProjectOrFolderExist(context) {
      const {params} = context;
      const {folderId} = context.params;

      const folder = folderId ? await context.call('folders.get', {id: folderId}) : {};
      if (folder.isDeleted) {
        throw new MoleculerClientError(i18next.t("folder_was_deleted"), 404);
      }

      const projectId = params.projectId || params?.id;
      const project = projectId ? await this.adapter.findById({_id: projectId}) : {};
      if (project.isDeleted) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
      }

      const templateId = params?.templateId;
      const template = templateId ? await context.call("templates.get", {id: templateId}) : {};
      if (template.isDeleted) {
        throw new MoleculerClientError(i18next.t("template_was_deleted"), 404);
      }
    },
    async checkToolPermission(ctx) {
      let {toolId} = ctx.params;
      const {user} = ctx.meta;
      if (user?.isSystemAdmin) return;
      return await ctx.call("tools.checkToolPermission", {id: toolId});
    },
    async getTagAndCategory(projectIds, inputType) {
      const contentData = await this.broker.call("contents.studentDetails", {projectIds, inputType});
      const allProjectIds = contentData.map(content => ObjectId(content.projectId));
      const projects = await this.broker.call("projects.find", {query: {_id: {$in: allProjectIds}, isDeleted: false}});
      const tags = [];
      const categories = [];
      projects.forEach(project => {
        tags.push(project.tag);
        categories.push(project.category);
      });

      return {
        tags: [...new Set(tags.flat())],
        categories: [...new Set(categories.flat())]
      }
    }
  },

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
