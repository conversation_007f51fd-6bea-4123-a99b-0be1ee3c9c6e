const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { EMAIL_LOGS, EMAIL_CAMPAIGNS, USER } = require('../../../constants/dbCollections');

const schema = new Schema({
  campaignId: { type: Schema.Types.ObjectId, ref: EMAIL_CAMPAIGNS },
  userId: { type: Schema.Types.ObjectId, ref: USER },
  email: { type: String, required: true },
  subject: { type: String },
  content: { type: String }, // Content with replaced variables
  status: {
    type: String,
    enum: ['queued', 'sent', 'failed', 'opened', 'clicked', 'converted'],
    default: 'queued'
  },
  errorMessage: { type: String },
  openedAt: { type: Date },
  clickedAt: { type: Date },
  convertedAt: { type: Date },
  trackingId: { type: String }, // ID for tracking email
  isDeleted: { type: <PERSON>olean, default: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(EMAIL_LOGS, schema, EMAIL_LOGS);
