const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {WHITE_LIST, ORGANIZATION} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    email: {
      type: String,
      trim: true,
      unique: true,
      index: true,
      lowercase: true,
      required: true
    },
    isDeleted: { type: Boolean, default: false },
    organizationId: { type: Schema.Types.ObjectId, ref: ORGANIZATION },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(WHITE_LIST, schema, WHITE_LIST);
