const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {EMAIL_GROUPS, USER} = require('../../../constants/dbCollections');
const {AUTOMATIC_TYPE, GROUP_TYPE} = require("../emailMarketing.constants");
const schema = new Schema({
  name: {type: String, required: true},
  description: {type: String},
  type: {
    type: String,
    enum: Object.values(GROUP_TYPE),
    default: GROUP_TYPE.MANUAL
  },
  // For automatic groups
  automaticType: {
    type: String,
    enum: Object.values(AUTOMATIC_TYPE),
    optional: true
  },
  // For manual groups
  conditions: {
    registrationDateRange: {
      from: {type: Date},
      to: {type: Date}
    },
    paidRegistrationDateRange: {
      from: {type: Date},
      to: {type: Date}
    },
    packageExpiryDateRange: {
      from: {type: Date},
      to: {type: Date}
    },
    feedbackStatus: {type: String, enum: ['upgrade', 'not_upgrade']},
    totalUsageDays: {
      min: {type: Number},
      max: {type: Number}
    },
    daysNotLoggedIn: {
      min: {type: Number},
      max: {type: Number}
    },
    packageType: [{type: String}],
    usedPromotionCode: [{type: String}]
  },
  priority: {type: Number, default: 0},
  userCount: {type: Number, default: 0},
  lastUpdatedAt: {type: Date},
  createdBy: {type: Schema.Types.ObjectId, ref: USER},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(EMAIL_GROUPS, schema, EMAIL_GROUPS);
