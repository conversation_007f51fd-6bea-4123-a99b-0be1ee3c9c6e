const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {INSTRUCTION, OPTIONS, OUTPUT_TYPE} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    instruction: {type: String, required: true},
    shortName: {type: Schema.Types.Mixed, required: true},
    schemaInstruction: {type: Schema.Types.Mixed},
    responseFormat: {
      type: String,
      enum: ["json_object", "markdown", "text"],
    },
    chatType: {
      type: String,
      enum: ["text", "image"],
    },
    name: {type: String},
    responseHeadline: {type: String},
    showAdditionalRequest: {type: Boolean, default: false},
    outputType: String,
    gptModel: String,
    inputMessage: String,
    localization: {
      name: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      shortName: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
      responseHeadline: {
        en: {type: String, validate: /\S+/},
        vi: {type: String, validate: /\S+/},
      },
    },
    isDeleted: {type: Boolean, default: false},
    temperature: {type: Number, default: 0.7},
    maxTokens: {type: Number,},
    optionIds: [{type: Schema.Types.ObjectId, ref: OPTIONS}],
    outputTypeId: {type: Schema.Types.ObjectId, ref: OUTPUT_TYPE},
    isSubInstruction: {type: Boolean, default: false},
    instructionType: {
      type: String,
      enum: ["normal", "explain"],
      default: "normal"
    },
    subInstructionIds: [{type: Schema.Types.ObjectId, ref: INSTRUCTION}],
    explainInstructionIds: [{type: Schema.Types.ObjectId, ref: INSTRUCTION}],
    systemMessage: String
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(INSTRUCTION, schema, INSTRUCTION);
