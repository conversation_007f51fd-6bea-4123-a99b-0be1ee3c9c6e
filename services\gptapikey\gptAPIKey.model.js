const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {GPT_API_KEY} = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  apiKey: {type: String},
  modelInterface: {type: String},
  requestsPerDay: {type: Number},
  requestsPerMinute: {type: Number},
  url: {type: String},
  currentMinuteRequestUsed: {type: Number},
  currentDayRequests: {type: Number},
  isDeleted: {type: Boolean, default: false},

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(GPT_API_KEY, schema, GPT_API_KEY);

