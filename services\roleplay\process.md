# Quy trình thực hiện Role Play chi tiết

Dưới đây là các bư<PERSON><PERSON> chính trong một phiên role play, dựa trên PRD và kế hoạch phát triển:

**Phase 1: Chuẩn bị và Bắt đầu**

1.  **Học viên xem và chọn Khóa học:**
    *   **Client (Học viên):**
        *   Truy cập vào màn hình danh sách các khóa học.
        *   Xem thông tin các khóa học (tên, mô tả, thời gian gi<PERSON> lậ<PERSON>, loại gi<PERSON> lập).
        *   Chọn một khóa học cụ thể.
        *   Xem chi tiết khóa học, bao gồm danh sách các nhiệm vụ (tasks) cấu thành khóa học và tài liệu tham khảo (nếu có).
    *   **Server:**
        *   **<PERSON><PERSON><PERSON> thức:** HTTP/REST
        *   **`Courses Service`:**
            *   API `GET /courses`: Cung cấp danh sách các khóa học cho client.
            *   API `GET /courses/{courseId}`: Cung cấp chi tiết một khóa học.
            *   API `GET /courses/{courseId}/tasks`: Lấy danh sách các nhiệm vụ (tasks) liên quan đến khóa học.
        *   **`Tasks Service`:**
            *   API `GET /tasks/{taskId}`: Cung cấp thông tin chi tiết về một nhiệm vụ cụ thể (nếu cần truy cập riêng lẻ).

2.  **(Tùy chọn) Video Call giới thiệu với Clickee Persona:**
    *   **Client (Học viên):**
        *   Sau khi chọn khóa học, client có thể được đưa vào một video call ngắn.
        *   Giao diện hiển thị avatar của Clickee Persona và video của học viên.
        *   Client kết nối với server thông qua WebSocket.
        *   Gửi một sự kiện (event) qua WebSocket để bắt đầu phiên giới thiệu (ví dụ: `client:start_introduction`).
        *   Nhận luồng âm thanh (speech) từ Clickee Persona và phát lại.
        *   Học viên có thể nói để tương tác (nếu kịch bản giới thiệu cho phép).
    *   **Server:**
        *   **`WebSocket Service`:**
            *   Quản lý kết nối WebSocket từ client.
            *   Nhận sự kiện `client:start_introduction`.
            *   Phối hợp với các service khác để Clickee Persona giới thiệu về khóa học và AI Persona sắp tới.
        *   **`AI Personas Service`:** Cung cấp thông tin của '''Clickee Persona''' (một AI Persona đặc biệt dùng để hướng dẫn).
        *   **`OpenAI Service` (hoặc service LLM tương tự):**
            *   Tạo kịch bản/lời thoại giới thiệu cho Clickee Persona dựa trên thông tin khóa học (bao gồm tóm tắt các nhiệm vụ).
        *   **`Speech Processing Service`:**
            *   Sử dụng Text-to-Speech (TTS) để chuyển văn bản lời thoại của Clickee Persona thành âm thanh.
            *   Gửi luồng âm thanh này về client qua WebSocket (ví dụ: event `server:ai_speech_chunk`).
            *   Nếu học viên tương tác, nhận âm thanh từ client, dùng Speech-to-Text (STT) để xử lý và chuyển cho `OpenAI Service` nếu cần.
        *   **Giao thức:** WebSocket cho tương tác real-time, có thể có HTTP/REST để lấy thông tin ban đầu của Clickee Persona.

**Phase 2: Thực hành Role Play chính**

3.  **Bắt đầu phiên Role Play với AI Persona của Khóa học:**
    *   **Client (Học viên):**
        *   Chuyển sang giao diện video call chính. Một nửa màn hình hiển thị avatar tĩnh của AI Persona, nửa còn lại là video của học viên.
        *   Gửi sự kiện `client:start_session` qua WebSocket, kèm theo các thông tin sau:
            *   `sessionId`: Một UUID do client tạo ra để định danh ban đầu cho phiên.
            *   `userId`: ID của người dùng đang thực hiện (thường là `studentId`).
            *   `courseId`: ID của khóa học được chọn.
            *   `personaId`: ID của AI Persona sẽ tương tác (có thể được cấu hình ở cấp Course hoặc do người dùng chọn nếu có nhiều option).
            *   `studentId`: ID của học viên.
        *   **Liên tục ghi âm và gửi các đoạn âm thanh (`audioChunk` dạng Buffer PCM 16-bit) của học viên lên server qua WebSocket sự kiện `client:student_speech_chunk`, kèm theo thông tin `format` (sampleRate, channels, bitsPerSample) của audio đó.**
        *   **Liên tục nhận các đoạn âm thanh phản hồi (`server:ai_speech_chunk`) từ AI Persona và phát lại ngay lập tức. Cũng nhận các sự kiện text (`server:student_text_response`, `server:ai_text_response`) để hiển thị transcript, và sự kiện `server:ai_interrupted` nếu AI bị ngắt lời.**
        *   (Tùy chọn) Hiển thị bản ghi (transcript) của cuộc hội thoại, cập nhật từng phần (interim results) cho cả học viên và AI.
    *   **Server:**
        *   **`WebSocket Service` & `RolePlay Sessions Service` (Action `startSession`):**
            *   Khi nhận `client:start_session`:
                *   Server (action `startSession`) tạo một bản ghi `RolePlaySession` mới trong MongoDB. `_id` của bản ghi này trở thành `sessionId` chính thức, được sử dụng nội bộ và trong `connectionState`. `clientSessionId` (UUID từ client) cũng được lưu lại.
                *   Các thông tin như `userId`, `courseId`, `personaId`, `socketId` (ID của kết nối WebSocket hiện tại), `studentId`, `startedAt`, `isActive=true` được lưu vào session entity. Có thể lưu thêm danh sách `taskIds` thuộc `courseId` này.
                *   Trạng thái kết nối (`connectionState`) cho `socketId` này được khởi tạo/cập nhật, lưu trữ `sessionId` (từ DB), `userId`, `courseId`, `personaId`, `studentId` và tham chiếu tới `socket` object. **Thêm cờ `isAiResponding = false` và `isAiInterruptedByStudent = false` vào state.**
                *   Hàm `generateConversationPrompt(state)` được gọi để tạo "system prompt" dựa trên `courseId` (bao gồm tất cả các tasks trong course đó) và `personaId`. Prompt này được lưu làm mục đầu tiên trong `state.conversationHistory`.
                *   Server gửi sự kiện `server:session_started` về client với đầy đủ thông tin session entity đã tạo từ DB.
                *   Server gửi sự kiện `server:ready` ({status: 'ready'}) để báo client biết server đã sẵn sàng nhận audio.
        *   **Xử lý `client:student_speech_chunk` (trong `_handleStudentAudioChunk` và `processVoiceActivityAndStreamSTT` của `RolePlay Sessions Service`):**
            *   Server sử dụng một hàng đợi (`state.chunkQueue`) để xử lý các `audioChunk` nhận được một cách tuần tự, tránh xung đột.
            *   Thông tin `format` của audio client (`state.clientAudioFormat`) được cập nhật.
            *   Mỗi `audioChunk` nhận được cũng được đẩy vào `state.allAudioChunksForSession` (để lưu toàn bộ file audio của học viên ở cuối phiên).
            *   **Voice Activity Detection (VAD):**
                *   Audio chunks được tích lũy vào `state.audioBuffer`.
                *   Server sử dụng một thư viện VAD (ví dụ: Sherpa-ONNX với model Silero VAD, được quản lý trong `state.sherpaVad`) để phân tích `state.audioBuffer` và phát hiện các đoạn có giọng nói (speech segments).
                *   Khi VAD phát hiện một speech segment:
                    *   Nếu đây là bắt đầu một lượt nói mới của học viên (`!state.isStudentSpeaking`):
                        *   `state.isStudentSpeaking` được đặt thành `true`.
                        *   `state.currentStudentAudioChunks` (danh sách các buffer audio cho lượt nói hiện tại) và `state.currentStudentTranscript` (buffer text trung gian) được reset.
                        *   **Kiểm tra ngắt lời AI:** Nếu `state.isAiResponding` là `true` (AI đang trong quá trình tạo phản hồi hoặc TTS), đặt `state.isAiInterruptedByStudent = true`. AI sẽ kiểm tra cờ này và dừng lại (chi tiết ở mục xử lý LLM & TTS).
                        *   Server gọi `this.broker.call('roleplay.speechprocessing.initializeSpeechStream', { language: 'vi-VN', sessionId: state.sessionId })` để khởi tạo một luồng Speech-to-Text (STT) mới với `speechprocessing.service`. `sttStreamId` trả về được lưu vào `state`.
                    *   Segment audio (đã được VAD xử lý, dạng Buffer PCM16) được thêm vào `state.currentStudentAudioChunks`.
                    *   Segment audio này được đẩy tới dịch vụ STT thông qua `this.broker.call('roleplay.speechprocessing.pushAudioToStream', { streamId: state.sttStreamId, audioChunk: segmentAudioBuffer })`.
                    *   `state.lastVoiceActivity` (thời điểm cuối cùng phát hiện giọng nói) được cập nhật.
                *   Khi VAD phát hiện một khoảng lặng đủ dài (ví dụ, `AUDIO_PROCESSING_DEFAULTS.silenceThreshold` ms) sau một speech segment:
                    *   Điều này báo hiệu kết thúc lượt nói của học viên.
                    *   Server gọi `this.broker.call('roleplay.speechprocessing.closeSpeechStream', { streamId: state.sttStreamId })` để báo cho dịch vụ STT rằng lượt nói đã kết thúc và chờ kết quả final.
                    *   `state.sttStreamId` được đặt lại thành `null`, `state.isStudentSpeaking` thành `false`.
        *   **Xử lý sự kiện từ `speechprocessing.service` (STT results):**
            *   `speech.stream.recognizing` (Event từ `speechprocessing.service`): Nhận văn bản trung gian (interim transcript) từ STT.
                *   `RolePlay Sessions Service` gửi sự kiện `server:student_text_response` ({ sessionId, text, role: 'user', isFinal: false }) về client để cập nhật giao diện.
            *   `speech.stream.recognized` (Event từ `speechprocessing.service`): Nhận văn bản hoàn chỉnh (final transcript) của lượt nói, kèm theo `duration` (thời lượng của audio được nhận dạng).
                *   `state.currentStudentTranscript` được reset.
                *   Server gọi `this.saveStudentTurnAudio(state, [...state.currentStudentAudioChunks])`. Hàm này sẽ sử dụng `_saveAudioChunksToFile` với `audioType: 'student_turn'` để lưu file audio của lượt nói này của học viên. Kết quả trả về gồm `fileId` (được dùng làm `turnAudioId`) và `duration` (thời lượng file audio).
                *   `state.currentStudentAudioChunks` được reset.
                *   Nếu văn bản nhận dạng được không rỗng:
                    *   Thông tin lượt nói (role: 'user', content: text.trim(), turnAudioId, duration (tính bằng giây), speakSpeed (từ/giây)) được thêm vào `state.conversationHistory`.
                    *   Server gửi sự kiện `server:student_text_response` ({ sessionId, text: text.trim(), role: 'user', isFinal: true, turnAudioId }) về client.
                    *   Server gọi `this.processLLMResponse(state, socket)` để AI xử lý và phản hồi. **Trước khi gọi, `state.isAiInterruptedByStudent` được reset về `false` (vì lượt nói của student đã được xử lý xong).**
            *   `speech.stream.nomatch` (Event từ `speechprocessing.service`): STT không nhận dạng được giọng nói trong lượt nói vừa rồi.
                *   `RolePlay Sessions Service` gửi sự kiện `server:stt_nomatch` ({ sessionId, message: "Không nhận dạng được giọng nói." }) về client. `state.currentStudentAudioChunks` được reset.
            *   `speech.stream.error` (Event từ `speechprocessing.service`): Có lỗi xảy ra từ dịch vụ STT.
                *   `RolePlay Sessions Service` gửi sự kiện `server:error` ({ sessionId, message: "Lỗi STT streaming..." }) về client. Stream STT (nếu có) có thể được đóng. `state.currentStudentAudioChunks` được reset.
        *   **`AI Personas Service`:** Được truy vấn (bởi `generateConversationPrompt` trong `RolePlay Sessions Service`) để lấy thông tin chi tiết của AI Persona.
        *   **`Courses Service` & `Tasks Service`:** Được truy vấn (bởi `generateConversationPrompt` trong `RolePlay Sessions Service`) để lấy thông tin chi tiết của Khóa học và các Nhiệm vụ trong khóa học đó, làm cơ sở cho AI tương tác.
        *   **`OpenAI Service` & `RolePlay Sessions Service` (Xử lý ngôn ngữ tự nhiên với LLM và TTS trong `processLLMResponse`):**
            *   **Bắt đầu lượt AI:** `state.isAiResponding = true` được đặt.
            *   Hàm `generateConversationPrompt(state)` được gọi để chuẩn bị danh sách `messages` (bao gồm system prompt được thiết kế cho toàn bộ `course` và toàn bộ `state.conversationHistory` trước đó).
            *   **Kiểm tra ngắt lời trước khi gọi LLM:** Nếu `state.isAiInterruptedByStudent` là `true`, AI sẽ không gọi LLM. Server gửi sự kiện `server:ai_interrupted` ({ sessionId, message: "AI bị ngắt lời bởi người dùng." }). `state.isAiResponding = false` và `state.isAiInterruptedByStudent = false` được reset. Hàm kết thúc sớm.
            *   Server gọi `this.broker.call('roleplay.openai.chatCompletionStream', { messages, model: "gpt-4o-mini", ... })` để lấy phản hồi từ mô hình LLM dưới dạng stream.
            *   Server gửi sự kiện `server:ai_tts_started` ({ sessionId }) cho client (thời điểm này có thể điều chỉnh, ví dụ sau khi LLM có text đầu tiên).
            *   Khi nhận từng `chunk` (phần dữ liệu) từ stream LLM:
                *   **Kiểm tra ngắt lời trong khi LLM stream:** Nếu `state.isAiInterruptedByStudent` là `true`, dừng việc nhận thêm chunk từ LLM. Server gửi sự kiện `server:ai_interrupted` ({ sessionId, message: "AI bị ngắt lời bởi người dùng khi đang tạo văn bản." }). `state.isAiResponding = false` và `state.isAiInterruptedByStudent = false` được reset. Xử lý phần văn bản đã nhận được (nếu có) để lưu lại (không TTS) và kết thúc sớm.
                *   Nội dung text (`content`) từ chunk được ghép vào `accumulatedText` (buffer text cho câu hiện tại) và `fullAiResponseText` (toàn bộ phản hồi của AI trong lượt này).
                *   Hàm `this.extractSentencesAndRemainder(accumulatedText)` được sử dụng để tách các câu hoàn chỉnh (`sentence`) ra khỏi `accumulatedText`.
                *   **Với mỗi câu hoàn chỉnh (`sentence`) được tách ra:**
                    *   Server gửi sự kiện `server:ai_text_response` ({ sessionId, text: sentence, role: 'assistant', turnId: llmResponseId }) về client. `llmResponseId` là một ID duy nhất được tạo cho mỗi lượt phản hồi của AI.
                    *   **Kiểm tra ngắt lời trước khi TTS cho câu:** Nếu `state.isAiInterruptedByStudent` là `true`, không gọi TTS cho câu này và các câu sau. Server gửi `server:ai_interrupted` ({ sessionId, message: "AI bị ngắt lời bởi người dùng trước khi TTS câu." }). `state.isAiResponding = false` và `state.isAiInterruptedByStudent = false` được reset. Xử lý phần văn bản đã nhận được (không TTS) và kết thúc sớm.
                    *   Câu này được chuyển đổi thành giọng nói bằng cách gọi `this.processSingleSentenceToSpeech(state, sentence, socket)`.
                        *   Bên trong `processSingleSentenceToSpeech`: Gọi `this.broker.call('tts.textToSpeech', { provider: "google_gemini", text: sentence })` để lấy audio từ `tts.service`.
                        *   Audio buffer (dạng MP3 hoặc WAV tùy nhà cung cấp) trả về từ TTS được chia thành các chunk nhỏ và gửi liên tục về client qua sự kiện `server:ai_speech_chunk` ({ sessionId, audioChunk }). **Trong vòng lặp gửi chunk, kiểm tra `state.isAiInterruptedByStudent`. Nếu `true`, dừng gửi audio chunks, thông báo cho client và kết thúc sớm.**
                        *   Audio buffer hoàn chỉnh của câu này được trả về từ `processSingleSentenceToSpeech` và được thêm vào `state.currentAiTurnAudioChunks` (để lưu audio của lượt AI hiện tại) và cũng được thêm vào `state.allAiAudioChunksForSession` (để lưu toàn bộ audio của AI trong cả phiên).
                *   `accumulatedText` được cập nhật bằng phần `remainder` (phần text còn lại chưa tạo thành câu hoàn chỉnh).
            *   Sau khi stream LLM kết thúc (và không bị ngắt lời), nếu `accumulatedText` vẫn còn (phần text cuối cùng chưa thành câu), nó cũng được xử lý TTS và gửi tương tự, **có kiểm tra ngắt lời trước và trong khi TTS.**
            *   Toàn bộ văn bản phản hồi của AI (`fullAiResponseText`) đã được ghi nhận.
            *   **Nếu không bị ngắt lời:**
                *   Server gọi `this.saveAiTurnAudio(state, [...state.currentAiTurnAudioChunks])`. Hàm này sẽ sử dụng `_saveAudioChunksToFile` với `audioType: 'ai_turn'` để lưu file audio của toàn bộ lượt nói này của AI. Kết quả trả về gồm `fileId` (được dùng làm `aiTurnAudioId`) và `duration` (thời lượng file audio).
                *   `state.currentAiTurnAudioChunks` được reset.
                *   Thông tin lượt nói của AI (role: 'assistant', content: fullAiResponseText.trim(), turnAudioId, duration (tính bằng giây), speakSpeed (từ/giây)) được thêm vào `state.conversationHistory`.
                *   Server gửi sự kiện `server:ai_response_completed` ({ sessionId, fullText: fullAiResponseText.trim(), turnAudioId, duration }) về client, báo hiệu AI đã nói xong lượt của mình.
            *   Server gửi sự kiện `server:ai_tts_completed` ({ sessionId }) về client, báo hiệu toàn bộ quá trình TTS cho lượt AI đã hoàn tất (hoặc bị ngắt).
            *   **Cuối cùng (trong `finally` block hoặc tương đương):** `state.isAiResponding = false` và `state.isAiInterruptedByStudent = false` được reset.
        *   **(Nếu có yêu cầu ghi video) `Video Processing Service`:**

4.  **Tương tác qua lại giữa Học viên và AI Persona:**
    *   Quá trình ở bước 3 (Client gửi giọng nói -> Server VAD -> Server STT -> Server LLM -> Server TTS -> Client nhận text và giọng nói AI) lặp đi lặp lại.
    *   **`RolePlay Sessions Service`:** Liên tục cập nhật `state.conversationHistory` và các trạng thái khác của phiên.

5.  **Kết thúc phiên Role Play:**
    *   **Client (Học viên):**
        *   Học viên nhấn nút kết thúc hoặc hết thời gian quy định.
        *   Gửi sự kiện `client:end_session` qua WebSocket, có thể kèm `reason`.
    *   **Server:**
        *   **`WebSocket Service` & `RolePlay Sessions Service` (Action `endSession` hoặc xử lý `disconnect`):**
            *   Khi nhận `client:end_session` (hoặc khi sự kiện `socket.on('disconnect')` được kích hoạt):
                *   Action `endSession` (hoặc `handleDisconnectedSession` trong trường hợp `disconnect`) được gọi.
                *   **Lưu audio toàn bộ của học viên:** Server gọi `this.saveAudioFile(state)` (sử dụng toàn bộ các chunks trong `state.allAudioChunksForSession`). Hàm này gọi `_saveAudioChunksToFile` với `audioType: 'student_full'`. `fileId` trả về được cập nhật vào trường `recordingId` của session entity trong DB. `state.allAudioChunksForSession` được reset sau đó.
                *   **Lưu audio toàn bộ của AI:** Server gọi `this.saveAiAudioFile(state)` (sử dụng toàn bộ các chunks trong `state.allAiAudioChunksForSession`). Hàm này gọi `_saveAudioChunksToFile` với `audioType: 'ai_full'`. `fileId` trả về được cập nhật vào trường `aiRecordingId` của session entity trong DB. `state.allAiAudioChunksForSession` được reset sau đó.
                *   **Lưu transcripts:** `state.conversationHistory` (đã được lọc bỏ system prompt, các mục không phải 'user'/'assistant', và 'user' được đổi thành 'student') được chuẩn bị. Mỗi mục transcript trong lịch sử này chứa: `role` ('student' hoặc 'ai'), `content` (text), `timestamp`, `audioId` (chính là `turnAudioId` của lượt nói đó), `duration`, `speakSpeed`. Toàn bộ mảng transcripts này được lưu vào trường `transcripts` của session entity trong DB.
                *   Cập nhật trạng thái session trong DB: `status: 'completed'`, `endTime`, `duration` (tính toán từ `startedAt` và `endTime`), `isCompleted: true`. Nếu là ngắt kết nối, có thể cập nhật `status` khác và `endReason: 'client_disconnected'`, `isActive: false`.
                *   Server gửi sự kiện `server:session_ended` về client (nếu kết thúc bình thường từ `client:end_session`).
                *   Trong trường hợp `disconnect`, `connectionState` của client đó được xóa khỏi Map.
                *   Kích hoạt quá trình phân tích (xem Phase 3).
        *   **(Nếu có) `Video Processing Service`:** Hoàn tất việc ghi và lưu trữ video. API `POST /videos/{videoId}/finalize`.

**Phase 3: Phân tích và Hiển thị Kết quả**

6.  **Server thực hiện phân tích tự động:**
    *   **`Analysis Service`:**
        *   Được kích hoạt bởi `RolePlay Sessions Service` sau khi phiên kết thúc.
        *   API (nội bộ) `POST /analysis/analyze` hoặc `POST /sessions/{sessionId}/analyze`.
        *   **Logic phân tích nhiệm vụ/khóa học:** Dựa trên `transcripts` và thông tin `courseId` (bao gồm các `tasks` của course đó), so sánh với mục tiêu của toàn bộ khóa học đã cấu hình. Đánh giá mức độ hoàn thành các mục tiêu của khóa học.
        *   **Phân tích tốc độ nói và chất lượng:** Xử lý `transcripts` (và có thể cả file audio gốc từ `recordingId`, `aiRecordingId` hoặc các `turnAudioId`) để tính tốc độ từ/phút, từ đệm, độ dài câu.
        *   **Tích hợp OpenAI cho phân tích nội dung:** Gửi `transcripts`, mô tả khóa học, và các tiêu chí đánh giá cho OpenAI để nhận xét về kiến thức, điểm mạnh/yếu, gợi ý cải thiện (AI Trainer Feedback, Knowledge Analysis) trong phạm vi toàn bộ khóa học.
        *   Lưu kết quả phân tích vào cơ sở dữ liệu, liên kết với `RolePlaySession`.
    *   **`Emotion Analysis Service`:**
        *   Được kích hoạt bởi `Analysis Service` hoặc `RolePlay Sessions Service`.
        *   API (nội bộ) `POST /emotion-analysis/analyze`.
        *   Tích hợp với OpenAI (hoặc một mô hình chuyên biệt) để phân tích cảm xúc từ transcript.
        *   (Nâng cao) Phân tích giọng nói (tone, cường độ) từ file audio đã lưu để đánh giá năng lượng (Energy).
        *   Lưu kết quả phân tích.
    *   **`Soft Skills Analysis Service` (Nếu có phân tích video và ảnh):**
        *   Được kích hoạt. API (nội bộ) `POST /soft-skills-analysis/analyze`.
        *   **`Video Processing Service`:** Có thể cần trích xuất các khung hình (frames) từ video đã lưu để `Soft Skills Analysis Service` xử lý.
        *   Phân tích hình ảnh/video để đánh giá ngôn ngữ cơ thể, giao tiếp bằng mắt, biểu cảm khuôn mặt, vị trí cơ thể.
        *   Lưu kết quả phân tích.

7.  **Học viên xem kết quả đánh giá:**
    *   **Client (Học viên):**
        *   Sau khi server hoàn tất phân tích, client được chuyển đến màn hình kết quả hoặc nhận thông báo.
        *   Gọi API để lấy chi tiết kết quả đánh giá cho phiên vừa thực hiện.
        *   Hiển thị các mục như trong PRD:
            *   Conversation Simulation (thông tin phiên, điểm số).
            *   AI Trainer Feedback.
            *   Knowledge Analysis (điểm kiến thức, đánh giá chi tiết, quy trình, yếu tố then chốt, gợi ý).
            *   Style Analysis (rõ ràng, tốc độ, từ đệm, độ dài câu, năng lượng).
            *   Video Analysis (xem lại video, tải video - nếu có).
            *   Soft Skills Analysis (ngôn ngữ cơ thể, giao tiếp mắt, biểu cảm, vị trí cơ thể - nếu có).
    *   **Server:**
        *   **`RolePlay Sessions Service`:**
            *   API `GET /sessions/{sessionId}/details` (hoặc `/sessions/{sessionId}/results`): Cung cấp chi tiết phiên và tất cả các kết quả phân tích liên quan (từ `Analysis Service`, `Emotion Analysis Service`, `Soft Skills Analysis Service`).
            *   API `GET /sessions/history`: Cho phép học viên xem lại lịch sử các phiên đã thực hiện.
        *   **`Analysis Service`:**
            *   API `GET /analysis/results/{sessionId}` (có thể được gọi bởi `RolePlay Sessions Service` để tổng hợp kết quả).
        *   **(Nếu có) `Video Processing Service`:**
            *   API `GET /videos/{videoId}` hoặc `GET /videos/{videoId}/stream`: Cho phép client xem lại hoặc tải video.

**Sơ đồ Tóm tắt các Services và Giao thức chính:**

| Giai đoạn         | Client <=> Server Interaction                                  | Giao thức chính | Server Services Chính Tham Gia                                                                                                                                                                                                                                                                                               |
| :---------------- | :------------------------------------------------------------- | :--------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Chuẩn bị**      | Lấy danh sách/chi tiết khóa học, nhiệm vụ                       | HTTP/REST        | `Courses Service`, `Tasks Service`                                                                                                                                                                                                                                                                                         |
|                   | (Tùy chọn) Video call giới thiệu                              | WebSocket, HTTP  | `WebSocket Service`, `AI Personas Service`, `OpenAI Service` (LLM cho kịch bản giới thiệu course), `tts.service` (TTS cho Clickee Persona), `speechprocessing.service` (STT nếu có tương tác)                                                                                                                                            |
| **Thực hành**    | Bắt đầu phiên (với `courseId`), gửi/nhận audio/text chunks liên tục, kết thúc phiên | WebSocket, HTTP  | `WebSocket Service` (quản lý luồng events, data), `RolePlay Sessions Service` (quản lý state, logic chính, VAD, gọi các services khác), `AI Personas Service`, `Courses Service`, `Tasks Service` (để `generateConversationPrompt` lấy thông tin course/tasks), `OpenAI Service` (LLM xử lý logic hội thoại cho course, streaming response), `speechprocessing.service` (streaming STT cho user audio), `tts.service` (TTS từng câu cho agent audio), `files.service` (lưu audio chunks/files), (`Video Processing Service`) |
| **Phân tích**     | (Server tự động) Phân tích hội thoại, cảm xúc, kỹ năng mềm dựa trên toàn bộ course    | (Nội bộ)         | `RolePlay Sessions Service` (kịch hoạt), `Analysis Service` (phân tích theo `courseId`), `Emotion Analysis Service`, `Soft Skills Analysis Service`, `OpenAI Service` (LLM cho phân tích nội dung course), (`Video Processing Service`)                                                                                                                               |
| **Xem Kết quả** | Lấy và hiển thị chi tiết kết quả cho course, lịch sử, xem/tải video      | HTTP/REST        | `RolePlay Sessions Service`, `Analysis Service`, `Emotion Analysis Service`, `Soft Skills Analysis Service`, (`Video Processing Service`), `files.service` (cung cấp đường dẫn file audio/video)                                                                                                                   |

**Lưu ý quan trọng:**

*   **Tên API và Event:**
    *   **Client Events:** `client:start_session`, `client:student_speech_chunk`, `client:end_session`.
    *   **Server Events:** `server:ready`, `server:session_started`, `server:error`, `server:student_text_response` (isFinal: false/true), `server:stt_nomatch`, `server:ai_text_response` (có `turnId`), `server:ai_speech_chunk`, `server:ai_tts_started`, `server:ai_tts_completed`, `server:ai_response_completed`, `server:session_ended`, **`server:ai_interrupted` (mới thêm)**, **`server:ai_speech_interrupted` (mới thêm)**.
*   **Lưu trữ:**
    *   **Transcripts, thông tin phiên, kết quả phân tích:** MongoDB (trong `RolePlaySession` entity).
    *   **Audio Files:**
        *   `RolePlaySession.recordingId`: File ID (từ `files.service`) của toàn bộ audio của học viên trong phiên (`audioType: 'student_full'`).
        *   `RolePlaySession.aiRecordingId`: File ID (từ `files.service`) của toàn bộ audio của AI trong phiên (`audioType: 'ai_full'`).
        *   `RolePlaySession.transcripts[].audioId`: File ID (từ `files.service`) của audio cho từng lượt nói cụ thể (`audioType: 'student_turn'` hoặc `audioType: 'ai_turn'`).
        *   Tất cả các file audio được lưu thông qua `files.service` (action `createAudioSessionFile`) vào một thư mục được cấu hình (ví dụ: `roleplay_audio_sessions`).
    *   **Avatar AI Persona, tài liệu khóa học, video đã ghi:** MongoDB GridFS, hệ thống file cục bộ, hoặc dịch vụ lưu trữ cloud (S3, Azure Blob Storage).
*   **Xác thực và Phân quyền:** Cần được tích hợp vào API Gateway và các services để đảm bảo chỉ người dùng có quyền mới truy cập được dữ liệu/chức năng tương ứng (đã có trong kế hoạch ở mục "Triển khai APIs cho Frontend").
*   **MVP Focus:** Theo PRD, tất cả các chức năng này là cần thiết cho MVP, nhưng thứ tự ưu tiên sẽ quyết định cái nào làm trước. "Mức độ hoàn thành nhiệm vụ" là chỉ số thành công chính. 
