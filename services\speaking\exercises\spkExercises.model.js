const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {SPEAKING_EXERCISES, FILE, USER} = require("../../../constants/dbCollections");
const {SPEAKING_PARTS} = require("../speaking.constants");

const schema = new Schema(
  {
    title: {
      type: String,
      required: true,
      maxlength: 100
    },
    topic: {
      type: String,
      required: true,
      index: true // Add index for better query performance
    },
    avatarId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'hidden'],
      default: 'draft',
      index: true // Add index for status filtering
    },
    parts: [
      {
        part: {
          type: String,
          enum: Object.values(SPEAKING_PARTS),
          required: true
        },
        topic: {type: String},
        cueCard: {
          text: String,
          audioId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: FILE,
          },
        },
        questions: [
          {
            text: {type: String, required: true},
            audioId: {
              type: mongoose.Schema.Types.ObjectId,
              ref: FILE,
            },
            hint: {type: String},
          },
        ],
      }
    ],

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
    isDeleted: {type: Boolean, default: false, index: true},
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Create compound indexes for better query performance
schema.index({ status: 1, isDeleted: 1 }); // For published exercises filtering
schema.index({ topic: 1, status: 1 }); // For topic-based queries with status
schema.index({ createdAt: -1, status: 1 }); // For sorting by creation date with status

module.exports = mongoose.model(SPEAKING_EXERCISES, schema, SPEAKING_EXERCISES);
