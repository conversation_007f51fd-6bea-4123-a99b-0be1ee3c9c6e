const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {USER_FEEDBACK, SUBMIT_FEEDBACK, FEEDBACK, PROJECT} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    submitFeedbackId: {type: Schema.Types.ObjectId, ref: SUBMIT_FEEDBACK},
    feedbackId: {type: Schema.Types.ObjectId, ref: FEEDBACK},
    rating: {type: Number},
    bool: {type: Boolean},
    comment: {type: String},
    projectId: {type: Schema.Types.ObjectId, ref: PROJECT},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(USER_FEEDBACK, schema, USER_FEEDBACK);
