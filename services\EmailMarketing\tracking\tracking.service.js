const i18next = require("i18next");
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'mkttracking',

  settings: {},

  actions: {
    /**
     * Track email open - endpoint tương thích với format yêu cầu
     */
    trackOpen: {
      rest: {
        method: 'GET',
        path: '/:trackingId/open'
      },
      auth: false,
      async handler(ctx) {
        const {trackingId} = ctx.params;
        const log = await this.broker.call('mktlogs.findOne', {trackingId});
        if (!log) {
          throw new MoleculerClientError(i18next.t('Log not found'), 404);
        }
        const campaignId = log.campaignId;
        // Update campaign statistics
        const campain = await this.broker.call('mktcampaigns.get', {id: campaignId.toString()});
        if (!campain) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }
        const statistics = {
          ...campain.statistics,
          totalOpened: campain.statistics.totalOpened + 1
        };

        // Update log status
        await this.broker.call('mktlogs.update', {
          id: log._id,
          status: 'opened'
        });
        await this.broker.call('mktcampaigns.update', {id: campaignId, statistics});

        return this.sendTrackingPixel(ctx);
      }
    },

    /**
     * Track email link click - endpoint tương thích với format yêu cầu
     */
    trackClick: {
      rest: {
        method: 'GET',
        path: '/:trackingId/click'
      },
      auth: false,
      async handler(ctx) {
        const {trackingId, url} = ctx.params;
        const log = await this.broker.call('mktlogs.findOne', {trackingId});
        if (!log) {
          throw new MoleculerClientError(i18next.t('Log not found'), 404);
        }
        const campaignId = log.campaignId;
        // Update campaign statistics
        const campain = await this.broker.call('mktcampaigns.get', {id: campaignId.toString()});
        if (!campain) {
          throw new MoleculerClientError(i18next.t('Campaign not found'), 404);
        }
        const statistics = {
          ...campain.statistics,
          totalClicked: campain.statistics.totalClicked + 1
        };

        // Update log status
        await this.broker.call('mktlogs.update', {
          id: log._id,
          status: 'clicked'
        });
        await this.broker.call('mktcampaigns.update', {id: campaignId, statistics});

        // Vẫn redirect đến URL ngay cả khi có lỗi
        return this.redirectToUrl(ctx, url);
      }
    },
  },

  methods: {
    /**
     * Send a 1x1 transparent pixel for tracking
     */
    sendTrackingPixel(ctx) {
      // Create a 1x1 transparent GIF
      const pixel = Buffer.from("R0lGODlhAQABAPAAAP///wAAACwAAAAAAQABAEACAkQBADs=", 'base64');

      // Trả về 1x1 transparent GIF
      ctx.meta.$responseType = "image/gif";
      ctx.meta.$responseHeaders = {
        "Content-Type": "image/gif",
        "Cache-Control": "no-cache"
      };

      return pixel;
    },

    /**
     * Redirect to a URL
     */
    redirectToUrl(ctx, url) {
      ctx.meta.$statusCode = 301;
      ctx.meta.$responseHeaders = {
        Location: url
      };
      return null;
    },
  }
};
