const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./publishedTemplate.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: "publishedtemplate",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "docxTemplateId": 'docxtemplates.get',
      "publishedBy": 'users.get',
      "organizationId": 'organizations.get',
    },
    populateOptions: ["docxTemplateId.templateId", "docxTemplateId.organizationId.avatarId", "publishedBy", "organizationId"],
  },

  hooks: {},

  actions: {
    create: {
      rest: "POST /",
      async handler(ctx) {
        const { docxTemplateId, organizationId } = ctx.params;
        const data = {
          docxTemplateId,
          organizationId,
          publishedBy: ctx.meta.user._id
        };
        const publishedTemplate = await this.adapter.insert(data);
        return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, publishedTemplate);
      }
    },
    makePublished: {
      rest: "POST /makePublished",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { docxTemplateId, organizationId } = ctx.params;
        const publishedTemplate = await this.adapter.findOne({ docxTemplateId, organizationId });
        if (!publishedTemplate) {
          return await this.adapter.insert({ docxTemplateId, organizationId, publishedBy: ctx.meta.user._id });
        }
        return await this.adapter.updateById(publishedTemplate._id, { publishedBy: ctx.meta.user._id });
      }
    },

    makeUnpublished: {
      rest: "POST /makeUnpublished",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { docxTemplateId, organizationId } = ctx.params;
        return await this.adapter.removeMany({ docxTemplateId, organizationId });
      }
    },
  },
  methods: {},
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
