version: "3.2"
services:
  api:
    image: "clickeedevapi:latest"
    deploy:
      replicas: 1
      placement:
        constraints: [ node.hostname==k8sbiai1 ]
      # placement:
        # constraints: [node.labels.environment==development]
      restart_policy:
        condition: any
    environment:
      PORT: 3000
      NODE_ENV: "production"
      LD_LIBRARY_PATH: "/app/node_modules/sherpa-onnx-linux-x64"
      SERVICE_3000_NAME: "clickeedevapi"
      SERVICE_NAME: "clickeedevapi"
      SERVICE_TAGS: "clickeedevapi"
      DOMAIN: 'https://clickee-dev.thinklabs.com.vn'
      CLIENT_ID: '413043088046-qai6ejq602e2ng5gimdku28s2e19gbch.apps.googleusercontent.com'
      CLIENT_SECRET: '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'
      REDIRECT_URI: 'https://clickee-dev.thinklabs.com.vn/auth/login-google'
      FORM_CLIENT_ID: '413043088046-n6ikic605fa6ml238c69mi9kaqc88bh6.apps.googleusercontent.com'
      FORM_CLIENT_SECRET: '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'
      FORM_REDIRECT_URI: 'https://clickee-dev.thinklabs.com.vn/google-form-callback'
      MONGO_URI: '********************************************************************************************************************************'
      OPENAI_API_KEY: '***************************************************'
      VNPAY_TMN: 'CLKETEST'
      VNPAY_HASH: 'SQV53YQ7G82ASHBAQ7KO1SFZZCYSD39Y'
      VNPAY_URL: 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html'
      VNPAY_WHITELIST: '**************,**************,************'
    ports:
      - target: 3000
        published: 4065
        mode: host
      - target: 3001
        published: 5065
        mode: host
    volumes:
      - uploadsfile:/app/services/File/storage

volumes:
  uploadsfile:
    driver: local
#volumes:
#  uploadsfile:
#    driver: local
