const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {EMAIL_TEMPLATES, USER} = require('../../../constants/dbCollections');
const {TEMPLATE_CATEGORY} = require("../emailMarketing.constants");
const schema = new Schema({
  name: {type: String, required: true},
  description: {type: String},
  subject: {type: String, required: true},
  content: {type: String, required: true}, // HTML content
  variables: [{type: String}], // List of variables used in the template
  createdBy: {type: Schema.Types.ObjectId, ref: USER},
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(EMAIL_TEMPLATES, schema, EMAIL_TEMPLATES);
