const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { TOOL, USER, USER_TOOL } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  userId: { type: Schema.Types.ObjectId, required: true, ref: USER },
  toolId: { type: Schema.Types.ObjectId, required: true, ref: TOOL },
  isFavorite: { type: Boolean, default: false },
  isActivate: { type: Boolean, default: false },
  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.index({ userId: 1 });
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(USER_TOOL, schema, USER_TOOL);

