"use strict";

const DbMongoose = require("../../../mixins/dbMongo.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FileMixin = require("../../../mixins/file.mixin");
const QuestionModel = require("./questions.model");
const { SPEAKING_PARTS, QUESTIONS_COUNT } = require("../speaking.constants");
const { MoleculerClientError } = require("moleculer").Errors;
const i18next = require("i18next");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "spkquestions",
  mixins: [DbMongoose(QuestionModel), FunctionsCommon, BaseService, FileMixin],

  /**
   * Settings
   */
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    // Enable caching
    cache: {
      enabled: true,
      ttl: 60 * 30 // 30 minutes
    }
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Create a hint for a question using AI
     *
     * @param {String} id - Question ID
     * @returns {Object} Updated question with hint
     */
    createHint: {
      rest: {
        method: "POST",
        path: "/:id/hint"
      },
      auth: "required",
      params: {
        id: { type: "string" }
      },
      async handler(ctx) {
        try {
          const { id } = ctx.params;

          // Get the question
          const question = await this.adapter.findById(id);
          if (!question) {
            throw new MoleculerClientError(`Question not found: ${id}`, 404, "QUESTION_NOT_FOUND");
          }

          // Generate hint using AI
          const prompt = `Generate a helpful hint for the following IELTS speaking question: "${question.questionText}". The hint should guide the student on how to approach the question without giving a complete answer. Keep it concise (max 2 sentences).`;

          const messages = [
            {
              role: "system",
              content: "You are an AI assistant that helps create concise, helpful hints for IELTS speaking questions."
            },
            {
              role: "user",
              content: prompt
            }
          ];

          const aiResult = await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: 0.7,
            max_tokens: 100,
          });

          // Update the question with the generated hint
          const updatedQuestion = await this.adapter.updateById(id, {
            hint: aiResult
          });

          return this.transformDocuments(ctx, {}, updatedQuestion);
        } catch (error) {
          if (error instanceof MoleculerClientError) throw error;

          this.logger.error("Error creating hint:", error);
          throw new MoleculerClientError("Failed to create hint", 500, "CREATE_HINT_ERROR");
        }
      }
    }
  },

  /**
   * Events
   */
  events: {
    /**
     * Event handler for sessionCreated event
     * Selects random questions from exercises based on tag and creates questions for the session
     *
     * @param {Object} ctx - Context
     * @param {Object} ctx.params - Event parameters
     * @param {String} ctx.params.id - Session ID
     * @param {String} ctx.params.tag - Tag to filter exercises
     * @param {String} ctx.params.part - Session part type
     */
    "sessionCreated": {
      async handler(ctx) {
        try {
          const { id, tag, part } = ctx.params;
          const sessionId = id;

          if (!sessionId) {
            this.logger.error("Session ID is required for sessionCreated event");
            return;
          }

          // Find exercises matching the tag
          const query = { status: "published", isDeleted: false };
          if (tag) {
            query.topic = tag;
          }

          const exercises = await this.broker.call("spkexercises.find", { query });

          if (!exercises || exercises.length === 0) {
            this.logger.warn(`No exercises found with tag: ${tag}`);
            return;
          }

          // Prepare questions for each part
          const selectedQuestions = await this.selectRandomQuestions(exercises, part);

          // Create questions for the session
          if (selectedQuestions.length > 0) {
            const questionEntities = selectedQuestions.map(question => ({
              sessionId,
              part: question.part,
              questionText: question.text,
              questionIndex: question.index, // Sử dụng index đã được tính toán riêng cho mỗi phần
              hint: question.hint,
              audioId: question.audioId,
              tags: tag
            }));

            await this.adapter.insertMany(questionEntities);
            this.logger.info(`Created ${questionEntities.length} questions for session ${sessionId}`);
          } else {
            this.logger.warn(`No questions selected for session ${sessionId}`);
          }
        } catch (error) {
          this.logger.error("Error handling sessionCreated event:", error);
        }
      }
    }
  },

  /**
   * Methods
   */
  methods: {
    /**
     * Select random questions from exercises based on part type
     *
     * @param {Array} exercises - List of exercises
     * @param {String} sessionPart - Session part type
     * @returns {Array} Selected questions
     */
    async selectRandomQuestions(exercises, sessionPart) {
      const selectedQuestions = [];

      // If it's a full test or specific part, select questions accordingly
      const needsPart1 = sessionPart === SPEAKING_PARTS.FULL_TEST || sessionPart === SPEAKING_PARTS.PART1;
      const needsPart2 = sessionPart === SPEAKING_PARTS.FULL_TEST || sessionPart === SPEAKING_PARTS.PART2;
      const needsPart3 = sessionPart === SPEAKING_PARTS.FULL_TEST || sessionPart === SPEAKING_PARTS.PART3;

      // Tạo các mảng riêng cho từng phần
      const part1Pool = [];
      const part2Pool = [];
      const part3Pool = [];

      // Process each exercise
      for (const exercise of exercises) {
        if (!exercise.parts || exercise.parts.length === 0) continue;

        // Process each part in the exercise
        for (const partData of exercise.parts) {
          // Skip if we don't need questions from this part
          if ((partData.part === SPEAKING_PARTS.PART1 && !needsPart1) ||
              (partData.part === SPEAKING_PARTS.PART2 && !needsPart2) ||
              (partData.part === SPEAKING_PARTS.PART3 && !needsPart3)) {
            continue;
          }

          // Skip if no questions in this part
          if (!partData.questions || partData.questions.length === 0) continue;

          // Add questions to the appropriate pool
          partData.questions.forEach(q => {
            const questionData = {
              part: partData.part,
              text: q.text,
              hint: q.hint,
              audioId: q.audioId
            };

            if (partData.part === SPEAKING_PARTS.PART1) {
              part1Pool.push(questionData);
            } else if (partData.part === SPEAKING_PARTS.PART2) {
              part2Pool.push(questionData);
            } else if (partData.part === SPEAKING_PARTS.PART3) {
              part3Pool.push(questionData);
            }
          });
        }
      }

      // Hàm trộn ngẫu nhiên mảng
      const shuffleArray = (array) => {
        for (let i = array.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
      };

      // Trộn và chọn số lượng câu hỏi cần thiết cho mỗi phần
      const selectedPart1 = shuffleArray([...part1Pool]).slice(0, QUESTIONS_COUNT.PART1); // Lấy 5 câu cho part 1
      const selectedPart2 = shuffleArray([...part2Pool]).slice(0, QUESTIONS_COUNT.PART2); // Lấy 1 câu cho part 2
      const selectedPart3 = shuffleArray([...part3Pool]).slice(0, QUESTIONS_COUNT.PART3); // Lấy 3 câu cho part 3

      // Thêm questionIndex cho từng phần
      const part1WithIndex = selectedPart1.map((q, index) => ({
        ...q,
        index: index + 1 // Index bắt đầu từ 1
      }));

      const part2WithIndex = selectedPart2.map((q, index) => ({
        ...q,
        index: index + 1 // Index bắt đầu từ 1
      }));

      const part3WithIndex = selectedPart3.map((q, index) => ({
        ...q,
        index: index + 1 // Index bắt đầu từ 1
      }));

      // Kết hợp tất cả câu hỏi đã chọn
      const result = [];

      if (needsPart1) result.push(...part1WithIndex);
      if (needsPart2) result.push(...part2WithIndex);
      if (needsPart3) result.push(...part3WithIndex);

      return result;
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {},

  /**
   * Service started lifecycle event handler
   */
  async started() {},

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {}
};
