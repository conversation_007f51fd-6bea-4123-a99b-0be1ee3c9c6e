const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { AUDIO, FILE } = require("../../constants/dbCollections");

const schema = new Schema(
  {
    audioName: { type: String, validate: /\S+/ },
    displayName: { type: String, validate: /\S+/ },
    audioPath: { type: String, validate: /\S+/ },
    audioFileId: { type: Schema.Types.ObjectId, ref: FILE },
    transcripts: [
      {
        cutStart: { type: Number, default: 0 },
        cutEnd: { type: Number, default: 0 },
        text: { type: String, default: "" },
      },
    ],
    duration: { type: Number },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
module.exports = mongoose.model(AUDIO, schema, AUDIO);
