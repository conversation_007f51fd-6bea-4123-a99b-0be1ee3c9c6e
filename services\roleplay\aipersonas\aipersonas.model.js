const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {ROLEPLAY_AIPERSONAS, FILE} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      maxlength: 100
    },
    age : {
      type: Number,
      min: 0,
      max: 100
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other'],
      trim: true,
    },
    avatarId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE
    },
    role: {
      type: String,
      maxlength: 100
    },
    mood: {
      type: String,
      maxlength: 100
    },
    organization: {
      type: String,
      maxlength: 255
    },
    smallTalkLikely: {
      type: Number,
      default: 0
    },
    filterWords: {
      type: [String],
      default: []
    },
    personaBackground: {
      type: String,
      maxlength: 2000
    },
    personaConcern: {
      type: String,
      maxlength: 2000
    },
    voice: {
      type: String,
      maxlength: 100
    },
    voiceStyle: {
      type: String,
      maxlength: 2000
    },
    voiceProvider: {
      type: String,
      maxlength: 500
    },
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
  },
);
module.exports = mongoose.model(ROLEPLAY_AIPERSONAS, schema, ROLEPLAY_AIPERSONAS);
