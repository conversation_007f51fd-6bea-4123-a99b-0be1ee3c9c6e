const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { TOOL_GROUP, TOOL } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    groupName: { type: Schema.Types.Mixed, required: true },
    description: { type: Schema.Types.Mixed },
    localization:{
      groupName:{
        en: { type: String, validate: /\S+/ },
        vi: { type: String, validate: /\S+/ },
      },
      description:{
        en: { type: String, validate: /\S+/ },
        vi: { type: String, validate: /\S+/ },
      },
    },
    code: { type: String },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(TOOL_GROUP, schema, TOOL_GROUP);
