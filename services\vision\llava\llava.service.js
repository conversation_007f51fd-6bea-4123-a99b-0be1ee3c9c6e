"use strict";

const fs = require("fs");
const {USER_CODES} = require("../../../constants/constant");
/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */

const APP_URL = "https://llava.hliu.cc/";

module.exports = {
  /**
   * Settings
   */
  settings: {
  },
  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */
    describeImage: {
      auth: "required",
      timeout: 2 * 60 * 1000,
      rest: {
        method: "GET",
        path: "/describeImage",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      // visibility: "protected",
      async handler(ctx) {
        const {filePath, instruction, imageBuffer} = ctx.params;
        try {
          if (filePath) {
            const sizeCheck = await this.checkSize(filePath);
            if (sizeCheck > 9.96) {
              return {
                error: "File size is greater than 10MB, try smaller video",
              };
            }
          }
          const base64 = imageBuffer ? this.base64FromBuffer(imageBuffer) : this.base64FromFile(filePath);
          console.log("========================instruction=======================", instruction);
          return this.describeImage(base64, instruction);
        } catch (err) {
          return err;
        }
      },
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        return fileSizeInBytes / (1024 * 1024);
      } catch (error) {
        return error;
      }
    },
    base64FromFile: (filePath) => {
      try {
        const base64 = fs.readFileSync(filePath, {encoding: "base64"});
        return `data:image/png;base64,${base64}`;
      } catch (error) {
        return error;
      }
    },
    base64FromBuffer: (imageBuffer) => {
      try {
        const base64 = imageBuffer.toString("base64");
        return `data:image/png;base64,${base64}`;
      } catch (error) {
        return error;
      }
    },
    describeImage: async (base64Image, instruction) => {
      try {
        const {client} = await import('@gradio/client');
        const app = await client(APP_URL);
        console.log(JSON.stringify(await app.view_api(), null, 2))
        // const result = await fetch("https://llava.hliu.cc/run/predict", {
        //   "headers": {
        //     "accept": "*/*",
        //     "accept-language": "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7",
        //     "content-type": "application/json",
        //     "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"",
        //     "sec-ch-ua-mobile": "?0",
        //     "sec-ch-ua-platform": "\"Windows\"",
        //     "sec-fetch-dest": "empty",
        //     "sec-fetch-mode": "cors",
        //     "sec-fetch-site": "same-origin",
        //     "cookie": "_ga_R1FN4KJKJH=GS1.1.1705311232.4.0.1705311232.0.0.0; _ga=GA1.2.1804557543.1704353427; _gid=GA1.2.1315076273.1705311233; _gat_gtag_UA_156449732_1=1",
        //     "Referer": "https://llava.hliu.cc/",
        //     "Referrer-Policy": "strict-origin-when-cross-origin"
        //   },
        //   "body": "{\"data\":[null,\"describe image\",\"data:image/png;base64,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\",\"Default\"],\"event_data\":null,\"fn_index\":7,\"session_hash\":\"wyp5lktkcvn\"}",
        //   "method": "POST"
        // });
        const result = await app.predict(7, [
          "describe image",
          base64Image,
          "Default"
        ]);

        console.log("==============================RESULT1=================================", JSON.stringify(result));
        const result2 = await app.predict(8, [
          null,
          "llava-v1.5-13b",
          0.2,
          0.7,
          512
        ]);
        console.log("==============================RESULT2=================================", JSON.stringify(result));
        return result2.data[0][0][1];
      } catch (e) {
        console.log(e);
      }
      try {
        const {client} = await import('@gradio/client');
        const app = await client(APP_URL);
        console.log(APP_URL)
        const result2 = await app.predict(8, [
          null,
          "llava-v1.5-13b",
          0.2,
          0.7,
          512
        ]);
        console.log("===============================================================", JSON.stringify(result2));
        return result2.data[0][0][1];
      } catch (e) {
        console.log(e);
        return e;
      }
    }
  },

  name: "llava",

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
