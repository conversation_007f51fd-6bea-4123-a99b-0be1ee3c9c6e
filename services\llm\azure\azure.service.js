const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");

const {AzureOpenAI} = require("openai");


/** @type {ServiceSchema} */
module.exports = {
  name: "azureopenai",

  mixins: [FunctionsCommon],
  settings: {
    apiKey: "DnAIKlVLm8vvtQV9LlzOkObSCFfAFYWUd6tacukYdJzJZToFK7p5JQQJ99BAACHYHv6XJ3w3AAAAACOG6EFf",
    region: "eastus",
    endpoint: "https://clickee.openai.azure.com/",
  },
  hooks: {}, /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */

    chatCompletion: {
      timeout: 5 * 60 * 1000, rest: {
        method: "POST",
        path: "/chatCompletion",
      }, // visibility: "protected",
      async handler(ctx) {
        try {
          let {
            messages,
            model,
            schema,
            responseFormat,
            responseId,
            temperature,
            max_tokens,
            apiKey,
            endpoint
          } = ctx.params;
          const setting = await ctx.call("settings.findOne");
          const client = new AzureOpenAI({
            endpoint: endpoint || setting?.azureEndpoint || this.settings.endpoint,
            apiKey: apiKey,
            deployment: "gpt-4o",
            apiVersion: "2024-05-01-preview",
          });
          let body = {
            model: model || "gpt-4o",
            messages: [...messages],
            max_tokens: 8192
          };
          if (responseFormat === 'json_object') {
            body = {
              ...body,
              messages: [...messages],
              tools: [{
                type: "function",
                function: {name: "show_response", description: "Show the response", parameters: schema}
              }],
              tool_choice: {type: "function", function: {name: "show_response"}},
              temperature
            };
          }
          const completion = await client.chat.completions.create(body);
          const {completion_tokens, prompt_tokens, total_tokens} = completion.usage;
          this.broker.emit("llmGenerateCompleted", {
            id: responseId,
            completionTokens: completion_tokens,
            promptTokens: prompt_tokens,
            totalTokens: total_tokens,
            gptModel: model
          })

          if (responseFormat === 'json_object') {
            const generatedText = completion.choices[0].message.tool_calls[0].function.arguments;
            // console.log("generatedText", JSON.parse(generatedText));
            return JSON.parse(generatedText);
          }
          return completion.choices[0].message.content;
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {

    async getAPIKey(ctx) {
      const setting = await ctx.call("settings.findOne");
      ctx.meta.apiKey = setting?.apiKeyOpenAI || process.env.OPENAI_API_KEY;
      return setting
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
