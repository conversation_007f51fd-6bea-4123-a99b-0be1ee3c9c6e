const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./favoriteTools.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "userTools",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {
      userId: { type: "string", min: 1 },
      toolId: { type: "string", min: 1 },
    },
    populates: {
      "toolId": 'tools.get',
      "userId": 'users.get',
    },
    populateOptions: ["toolId.instructionIds", "userId"]
  },

  hooks: {},

  actions: {
    getAllToolByUserId: {
      rest: {
        method: "GET",
        path: "/:userId/tools",
      },
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { userId } = ctx.params;
        const [userTools, tools] = await Promise.all([
          this.adapter.find({ query: { userId, isDeleted: false, isActivate: true } }),
          ctx.call('tools.detailTools')
        ]);
        const toolMaps = this.convertObject(tools, "_id");
        const transformedTools = await this.transformDocuments(ctx, {}, userTools);
        return transformedTools.map(item => {
          return {
            ...item,
            toolId: toolMaps[item.toolId.toString()]
          };
        });
      }
    }
  },
  methods: {
    async seedDB() {
      const users = await this.broker.call("users.find", { query: { isDeleted: false } });
      const tools = await this.broker.call("tools.find", {
        query: {
          isDeleted: false,
          isFavorite: true,
          visible: 'public'
        }
      });

      const insertPromises = users.map(user => {
        const insertPromisesForUser = tools.map(tool => {
          return this.adapter.insert({
            userId: user._id,
            toolId: tool._id,
          });
        });
        return Promise.all(insertPromisesForUser);
      });

      return await Promise.all(insertPromises);
    }
  },
  events: {
    async "orgTools.deleted"(payload) {
      const allUserBelongOrg = await this.broker.call("users.find", {
        query: { organizationId: payload.organizationId, isDeleted: false }
      });
      const promises = allUserBelongOrg.map(user => {
        return this.adapter.removeMany({ userId: user._id, toolId: payload.toolId });
      });
      await Promise.all(promises);
    },
    insertManyOrgTools: {
      params: {
        organizationId: "string",
        toolIds: "array"
      },
      async handler(ctx) {
        const { organizationId, toolIds } = ctx.params;
        const orgUsers = await this.broker.call("users.find", {
          query: { organizationId, isDeleted: false }
        });
        if (!orgUsers.length) {
          throw new MoleculerClientError(i18next.t("organization_users_not_found"));
        }
        const insertData = orgUsers.flatMap(user =>
          toolIds.map(toolId => ({ userId: user._id, toolId }))
        );
        return await Model.bulkWrite(insertData.map(row => ({
          updateOne: {
            filter: { userId: row.userId, toolId: row.toolId },
            update: {
              $set: {
                ...row,
                isDeleted: false,
                isActivate: true,
                isFavorite: false
              }
            },
            upsert: true
          }
        })));
      }
    },
    unpublicTool: {
      params: {
        toolId: "string",
      },
      async handler(ctx) {
        const { toolId } = ctx.params;
        await this.adapter.updateMany(
          {
            toolId
          },
          {
            $set: { isActivate: false }
          }
        );
      }
    },
    publicTool: {
      params: {
        toolId: "string",
      },
      async handler(ctx) {
        const { toolId } = ctx.params;
        const users = await this.broker.call("users.find", {
          query: { isDeleted: false }
        });
        const bulkWriteOperations = users.map(user => ({
          updateOne: {
            filter: { userId: user._id, toolId },
            update: { $set: { userId: user._id, toolId, isActivate: true, isDeleted: false } },
            upsert: true
          }
        }));

        await Model.bulkWrite(bulkWriteOperations);
      }
    }

  },
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      console.log('======================> SEEDING USER TOOL DATA');
      return this.seedDB();
    }
  },
};
