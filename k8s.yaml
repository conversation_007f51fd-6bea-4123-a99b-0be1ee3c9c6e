#########################################################
# Common Environment variables ConfigMap
#########################################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: common-env
data:
  NAMESPACE: ""
  LOGLEVEL: info
  SERVICEDIR: services
  TRANSPORTER: nats://nats:4222
  
  
  
  
  
  
  
  
  MONGO_URI: mongodb://mongo/tradar-g-be-microservices

---
#########################################################
# Service for Moleculer API Gateway service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: api
spec:
  selector:
    app: api
  ports:
  - port: 3000
    targetPort: 3000

---
#########################################################
# Ingress for Moleculer API Gateway
#########################################################
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  #annotations:
  #  kubernetes.io/ingress.class: nginx
spec:
  rules:
  - host: tradar-g-be-microservices.127.0.0.1.nip.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api
            port:
              number: 3000

---
#########################################################
# API Gateway service
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  selector:
    matchLabels:
      app: api
  replicas: 2
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
      - name: api
        image: tradar-g-be-microservices
        imagePullPolicy: IfNotPresent
        envFrom:
        - configMapRef:
            name: common-env
        env:
          - name: SERVICES
            value: api

---
#########################################################
# Greeter service
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: greeter
spec:
  selector:
    matchLabels:
      app: greeter
  replicas: 2
  template:
    metadata:
      labels:
        app: greeter
    spec:
      containers:
      - name: greeter
        image: tradar-g-be-microservices
        imagePullPolicy: IfNotPresent
        envFrom:
        - configMapRef:
            name: common-env
        env:
          - name: SERVICES
            value: greeter

---
#########################################################
# Products service
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: products
spec:
  selector:
    matchLabels:
      app: products
  replicas: 2
  template:
    metadata:
      labels:
        app: products
    spec:
      containers:
      - name: products
        image: tradar-g-be-microservices
        imagePullPolicy: IfNotPresent
        envFrom:
        - configMapRef:
            name: common-env
        env:
          - name: SERVICES
            value: products

---
#########################################################
# MongoDB server
#########################################################
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongo
  labels:
    app: mongo
spec:
  selector:
    matchLabels:
      app: mongo
  replicas: 1
  serviceName: mongo
  template:
    metadata:
      labels:
        app: mongo
    spec:
      containers:
        - image: mongo
          name: mongo
          ports:
            - containerPort: 27017
          resources: {}
          volumeMounts:
            - mountPath: /data/db
              name: mongo-data
      volumes:
        - name: mongo-data
          persistentVolumeClaim:
            claimName: mongo-data

---
#########################################################
# Persistent volume for MongoDB
#########################################################
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongo-data
  labels:
    name: mongo-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Mi

---
#########################################################
# MongoDB service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: mongo
  labels:
    app: mongo
spec:
  ports:
    - port: 27017
      targetPort: 27017
  selector:
    app: mongo          


---
#########################################################
# NATS transporter service
#########################################################
apiVersion: v1
kind: Service
metadata:
  name: nats
spec:
  selector:
    app: nats
  ports:
  - port: 4222
    name: nats
    targetPort: 4222

---
#########################################################
# NATS transporter
#########################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats
spec:
  selector:
    matchLabels:
      app: nats
  replicas: 1
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: nats
    spec:
      containers:
      - name: nats
        image: nats
        ports:
        - containerPort: 4222
          name: nats






