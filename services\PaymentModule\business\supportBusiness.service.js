const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./supportBusiness.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {sendEmail} = require("../../../helpers/emailHelper");
const {getConfig} = require("../../../config/config");
const config = getConfig(process.env.NODE_ENV);
const MailMixin = require("../../../mixins/mailSupport.mixin");
module.exports = {
  name: "supportbusiness",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, MailMixin],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: []
  },

  hooks: {
    //thực hiện sendmail sau khi tạo mới 1 ban ghi
    after: {
      async create(ctx, res) {
        await this.sendSupportBusinessMail(res);
        return res;
      }
    }
  },

  actions: {},
  methods: {
    async sendSupportBusinessMail(data) {
      const formHtml = this.createSupportBusinessEmail(data);
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
        to: config.mail.auth.user, // list of receivers
        subject: 'Hỗ trợ khách hàng doanh nghiệp', // Subject line
        html: formHtml,
      };
      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });
      this.broker.emit("mail.sent", {
        to: config.mail.auth.user,
        subject: 'Hỗ trợ khách hàng doanh nghiệp',
        status: "success",
        data: data,
      });
    }
  },
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {

  },
};
