const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {EXERCISES, EXERCISE_STATISTICS} = require("../../../constants/dbCollections");

const schema = new Schema({
    exerciseId: {type: mongoose.Schema.Types.ObjectId, ref: EXERCISES, required: true},
    completions: {
      type: Number,
      default: 0 // Số lần hoàn thành
    },
    averageScore: {
      type: Number,
      default: 0 // Điểm trung bình
    },
    commonErrors: [{
      word: String,
      frequency: Number // Từ sai phổ biến và tần suất
    }],
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
module.exports = mongoose.model(EXERCISE_STATISTICS, schema, EXERCISE_STATISTICS);
