const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./conversations.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const {createMessages} = require("../promptengine");
const AdmZip = require("adm-zip");
const fs = require("fs");
const mammoth = require("mammoth");
const path = require("path");
const storageDir = path.join(__dirname, "storage");
const FileMixin = require("../../../mixins/file.mixin");
const {OUTPUT_TYPE} = require("../../../constants/constant");
const INIT_INSTRUCTION = {
  role: "system",
  content: "As a good English teacher",
};
const createNewMessages = () => {
  return [INIT_INSTRUCTION];
};

module.exports = {
  name: 'conversations',
  mixins: [DbMongoose(Model), FileMixin, BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "datasetId": 'datasets.get',
      "creatorId": 'users.get',
      "editorId": 'users.get',
      "approvedBy": 'users.get',
    },
    populateOptions: ["datasetId.instructionId", "creatorId", "editorId", "approvedBy"],
  },

  hooks: {
    before: {}
  },

  actions: {
    copy: {
      rest: {
        method: "POST",
        path: "/copy",
      },
      async handler(ctx) {
        const {conversationId} = ctx.params;
        const conversation = await this.adapter.findOne({_id: conversationId});
        return await this.adapter.insert({
          creatorId: ctx.meta.user?._id,
          messages: conversation.messages,
          datasetId: conversation.datasetId
        });
      }
    },
    create: {
      rest: {
        method: "POST",
        path: "/",
      },
      async handler(ctx) {
        const entity = ctx.params;
        entity.creatorId = ctx.meta.user?._id;
        if (entity.approved) {
          entity.approvedBy = ctx.meta.user?._id;
        }
        return await this.adapter.insert(entity);
      }
    },
    approve: {
      rest: {
        method: "PUT",
        path: "/:id/approve",
      },
      async handler(ctx) {
        const entity = ctx.params;
        entity.approvedBy = entity.approved ? ctx.meta.user?._id : null;
        return await this.adapter.updateById(entity.id, entity);
      }
    },

    uploadDataset: {
      auth: "required",
      async handler(ctx) {
        const {filename} = ctx.meta;
        const {folder, datasetId, instructionId} = ctx.meta.$multipart;
        const zipFilePath = this.getFilePath(filename, this.getDirPath(folder, storageDir));
        await this.save(ctx.params, filename, folder);

        const zip = new AdmZip(zipFilePath);
        const zipEntries = zip.getEntries();

        const options = {
          styleMap: [
            "u => u",
            "strike => del",
            "b => strong",
          ]
        };

        const arrayTextPromises = zipEntries
          .filter(entry => entry.entryName.endsWith('.docx'))
          .map(async entry => {
            const [dirName, fileName] = entry.entryName.split("/");
            const docxFilePath = this.getFilePath(fileName, this.getDirPath(dirName, storageDir));
            fs.writeFileSync(docxFilePath, entry.getData());

            // const result = await mammoth.extractRawText({ path: docxFilePath });
            const result = await mammoth.convertToHtml({path: docxFilePath}, options);
            // const markdown = this.convertHTMLToMarkdown(result.value);
            fs.unlinkSync(docxFilePath);
            // return markdown;
            return result.value.replaceAll("\t", "&emsp;").replaceAll("\n", "<br/>");
          });

        const arrayText = await Promise.all(arrayTextPromises);
        fs.unlinkSync(zipFilePath);

        const pairDatasets = arrayText.flatMap((input, i) =>
          arrayText.slice(i + 1).map(output => ({input, output}))
        );

        // Mix dataset to conversation
        await ctx.call("conversations.mixDataset", {datasetId, instructionId, dataset: pairDatasets});

        return pairDatasets;
      }
    },

    mixDataset: {
      rest: {
        method: "POST",
        path: "/mixDataset",
      },
      async handler(ctx) {
        const {datasetId, instructionId, dataset} = ctx.params;
        const instructionData = await ctx.call("instructions.details", {id: instructionId});
        const inputData = {instructionId};

        const insertPromises = dataset.map(async ({input, output}) => {
          const messages = await createMessages({inputData, text: input, instructionData});
          this.appendOutput(messages, {text: output}, instructionData.outputTypeId?.code || instructionData.outputType);

          return this.adapter.insert({
            messages,
            datasetId,
            creatorId: ctx.meta.user?._id
          });
        });

        await Promise.all(insertPromises);

        return dataset;
      }
    }
  },
  methods: {
    appendOutput(messages, output, outputType) {
      if (!output) return;

      const content = (() => {
        switch (outputType) {
          case OUTPUT_TYPE.HTML:
            return output.markdown || output.html || output.text;
          case OUTPUT_TYPE.HTML_QUESTIONS:
            return output.questionsHtml || output.text;
          default:
            return output.text;
        }
      })();

      messages.push({
        role: "assistant",
        content,
      });
    },
    async save(stream, filename, folder) {
      const dirPath = this.getDirPath(folder, storageDir);
      const filePath = this.getFilePath(filename, dirPath);
      return this.saveToLocalStorage(stream, filePath);
    },
  },
  events: {
    async "datasets.deleted"(payload) {
      this.adapter.removeMany({datasetId: payload});
    },
    createConversation: {
      params: {
        response: "object",
        rating: "string",
      },
      async handler(ctx) {
        const {response, rating} = ctx.params;
        const {output, inputId} = response;
        const {inputData} = inputId;

        const [instructionData, defaultDataset] = await Promise.all([
          ctx.call('instructions.details', {
            id: inputData.instructionId,
            populateOpts: ["optionIds.knowledgeIds", "outputTypeId"]
          }),
          ctx.call('datasets.findOne', {
            query: {
              isDefault: true,
              isDeleted: false,
              instructionId: inputData.instructionId
            }
          })
        ]);

        const datasetId = defaultDataset?._id || (await ctx.call('datasets.create', {
          instructionId: inputData.instructionId,
          name: `Default Dataset for ${instructionData.shortName}`,
          isDefault: true
        }))._id;
        const messages = await createMessages({inputData, text: inputData.text, instructionData});
        this.appendOutput(messages, output, instructionData.outputTypeId.code || instructionData.outputType);

        await this.adapter.insert({
          messages,
          rating,
          datasetId,
          creatorId: ctx.meta.user?._id
        });
      }
    },
  },
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {

  },
};
