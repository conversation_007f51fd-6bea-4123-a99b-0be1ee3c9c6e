const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {EMAIL_MARKETING_USERS, EMAIL_GROUPS} = require('../../../constants/dbCollections');

const schema = new Schema({
  email: {
    type: String, 
    required: true,
    validate: {
      validator: function(v) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
      },
      message: 'Invalid email format'
    }
  },
  name: {type: String, required: true},
  phone: {
    type: String, 
    required: true,
    validate: {
      validator: function(v) {
        return /^[\+]?[0-9\s\-\(\)]{10,15}$/.test(v);
      },
      message: 'Invalid phone format'
    }
  },
  mktGroupId: {
    type: Schema.Types.ObjectId, 
    ref: EMAIL_GROUPS, 
    required: true
  },
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Create compound index for email and mktGroupId to prevent duplicates
schema.index({ email: 1, mktGroupId: 1 }, { unique: true });

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(EMAIL_MARKETING_USERS, schema, EMAIL_MARKETING_USERS);
