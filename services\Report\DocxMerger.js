const JSZip = require('jszip');
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, XMLSerializer } = require('@xmldom/xmldom');

class DocxMerger {
  constructor() {
    this._body = [];
    this._pageBreak = true;
    this._style = [];
    this._numbering = [];
    this._files = [];
    this._contentTypes = {};
    this._media = {};
    this._rel = {};
    this._builder = this._body;
    this._fonts = '';
    this._fontsize = '';
    this._nameSpace = [];
  }

  async initialize(options, files) {
    files = files || [];
    this._pageBreak = typeof options.pageBreak !== 'undefined' ? !!options.pageBreak : true;
    this._fonts = options.fonts || 'Arial';
    this._fontsize = options.fontSize || '24';
    for (const file of files) {
      this._files.push(await new JSZip().loadAsync(file));
    }
    if (this._files.length > 0) {
      await this.mergeBody(this._files)
    }
  }

  insertPageBreak = function() {
    const pb = '<w:p> \
					<w:r> \
						<w:br w:type="page"/> \
					</w:r> \
				</w:p>';

    this._builder.push(pb);
  };

  insertRaw = function (xml) {
    this._builder.push(xml);
  };

  async mergeBody(files) {
    this._builder = this._body;

    await mergeContentTypes(files, this._contentTypes);
    await prepareMediaFiles(files, this._media);
    await mergeRelations(files, this._rel);
    await prepareNumbering(files, this._numbering);
    await prepareStyles(files);
    await mergeStyles(files, this._style);
    await mergeNameSpace(files, this._nameSpace);

    for (let index = 0; index < files.length; index++) {
      const zip = files[index];
      let xmlString = await zip.file('word/document.xml').async('string');
      xmlString = xmlString.substring(xmlString.indexOf('<w:body>') + 8);
      xmlString = xmlString.substring(0, xmlString.indexOf('</w:body>'));
      xmlString = xmlString.trim();
      if (xmlString.lastIndexOf('<w:sectPr') === 0) {
        let tag = "</w:sectPr>";
        xmlString = xmlString.substring(xmlString.lastIndexOf(tag) + tag.length);
      } else {
        xmlString = xmlString.substring(0, xmlString.lastIndexOf('<w:sectPr'));
      }

      this.insertRaw(xmlString);
      if (this._pageBreak && index < files.length - 1)
        this.insertPageBreak();
    }
  }

  async save(type) {
    const zip = this._files[0];

    let xmlString = await zip.file('word/document.xml').async('string');

    const startNameSpace = xmlString.indexOf('<w:document ') + 12;
    const endNameSpace = xmlString.indexOf('<w:body>') - 1;
    xmlString = xmlString.replace(xmlString.slice(startNameSpace, endNameSpace), this._nameSpace.join(' '));

    const startIndex = xmlString.indexOf('<w:body>') + 8;
    const endIndex = xmlString.lastIndexOf('<w:sectPr');
    xmlString = xmlString.replace(xmlString.slice(startIndex, endIndex), this._body.join(''));

    await generateContentTypes(zip, this._contentTypes);
    await copyMediaFiles(zip, this._media, this._files);
    await generateRelations(zip, this._rel);
    await generateNumbering(zip, this._numbering);
    await generateStyles(zip, this._style, this._fonts, this._fontsize);

    zip.file('word/document.xml', xmlString);

    return await zip.generateAsync({
      type: type,
      compression: 'DEFLATE',
      compressionOptions: {
        level: 4
      }
    })
  };
}

const mergeNameSpace = async function(files, _nameSpace) {
  const uniqueNamespaces = new Set();

  for (let index = 0; index < files.length; index++) {
    const zip = files[index];
    let xmlString = await zip.file('word/document.xml').async('string');

    const xml = new DOMParser().parseFromString(xmlString, 'text/xml');

    const documentElement = xml.getElementsByTagName('w:document')[0];

    if (documentElement) {
      const attrs = documentElement.attributes;

      for (let i = 0; i < attrs.length; i++) {
        const attr = attrs[i];

        if (attr.name.startsWith('xmlns')) {
          uniqueNamespaces.add(`${attr.name}="${attr.value}"`);
        }
      }
    }
  }

  _nameSpace.push(Array.from(uniqueNamespaces).join(' '))
};

const prepareNumbering = async function(files, _numbering) {
  let count = 0;
  let num = [];
  for (let index = 0; index < files.length; index++) {
    const zip = files[index];

    const xmlBin = zip.file('word/numbering.xml');
    if (!xmlBin) continue;
    let xmlString = await xmlBin.async('string');

    const xml = new DOMParser().parseFromString(xmlString, 'text/xml');

    const nodes = xml.getElementsByTagName('w:abstractNum');
    const numNodes = xml.getElementsByTagName('w:num');

    if (nodes[0] && !nodes[0].getAttribute('w15:restartNumberingAfterBreak')) {
      for (let j = 0; j < nodes.length; j++) {
        const node = nodes[j];
        node.setAttribute('w:abstractNumId', (j + count).toString());

        const numNode = numNodes[j];
        numNode.setAttribute('w:numId', (j + count + 1).toString());

        const absrefID = numNode.getElementsByTagName('w:abstractNumId')[0];
        const Id = parseInt(absrefID.getAttribute('w:val')) + count - 1;
        absrefID.setAttribute('w:val', Id.toString());
      }

      count += nodes.length;

      xmlString = new XMLSerializer().serializeToString(xml.documentElement);
      const abstractNumString = xmlString.substring(xmlString.indexOf("<w:abstractNum "), xmlString.indexOf("<w:num "));
      _numbering.push(abstractNumString);

      const numString = xmlString.substring(xmlString.indexOf("<w:num "), xmlString.indexOf("</w:numbering>"));
      num.push(numString);

      await updateString(zip, count - nodes.length + 1)
    }
  }

  _numbering.push(num.join(""));
};

const generateNumbering = async function(zip, _numbering) {
  const xmlBin = zip.file('word/numbering.xml');
  if (!xmlBin) return
  let xmlString = await xmlBin.async('string');

  const startIndex = xmlString.indexOf("<w:abstractNum ");
  const endIndex = xmlString.indexOf("</w:numbering>");

  if (startIndex !== -1 && endIndex !== -1) {
    xmlString = xmlString.slice(0, startIndex) + _numbering.join("") + xmlString.slice(endIndex);
  } else {
    xmlString += _numbering.join("") + "</w:numbering>";
  }

  zip.file("word/numbering.xml", xmlString);
};

const updateString = async function(zip, index) {
  const xmlBin = zip.file('word/document.xml');
  if (!xmlBin) return;
  let xmlString = await xmlBin.async('string');

  const xml = new DOMParser().parseFromString(xmlString, 'text/xml');

  const wP = xml.getElementsByTagName('w:p');

  for (let i = 0; i < wP.length; i++) {
    const pPr = wP[i].getElementsByTagName('w:pPr');

    for (let j = 0; j < pPr.length; j++) {
      const numPr = pPr[j].getElementsByTagName('w:numPr')[0];
      if(numPr) {
        const numId = numPr.getElementsByTagName('w:numId')[0];
        const Id = parseInt(numId.getAttribute('w:val')) + index - 1;
        numId.setAttribute('w:val', Id.toString());
      }
    }
  }

  xmlString = new XMLSerializer().serializeToString(xml.documentElement);

  zip.file('word/document.xml', xmlString)
};

const prepareMediaFiles = async function(files, media) {
  let gCount = 1;

  for (let index = 0; index < files.length; index++) {
    const zip = files[index];

    const medFiles = zip.folder('word/media').files;
    for (const mfile in medFiles) {
      if (/^word\/media/.test(mfile) && mfile.length > 11) {
        const count = gCount++;
        media[count] = {};
        media[count].oldTarget = mfile;
        media[count].newTarget = mfile.replace(/[0-9]/, '_' + count).replace('word/', "");
        media[count].fileIndex = index;

        await updateMediaRelations(zip, count, media);
        await updateMediaContent(zip, count, media);
      }
    }
  }
};

const updateMediaRelations = async function(zip, count, _media) {
  let xmlString = await zip.file('word/_rels/document.xml.rels').async('string');
  let xml = new DOMParser().parseFromString(xmlString, 'text/xml');

  const childNodes = xml.getElementsByTagName('Relationships')[0].childNodes;
  const serializer = new XMLSerializer();

  for (const node in childNodes) {
    if (/^\d+$/.test(node) && childNodes[node].getAttribute) {
      const target = childNodes[node].getAttribute('Target');
      if ('word/' + target === _media[count].oldTarget) {

        _media[count].oldRelID = childNodes[node].getAttribute('Id');

        childNodes[node].setAttribute('Target', _media[count].newTarget);
        childNodes[node].setAttribute('Id', _media[count].oldRelID + '_' + count);
      }
    }
  }

  const startIndex = xmlString.indexOf('<Relationships');
  xmlString = xmlString.replace(xmlString.slice(startIndex), serializer.serializeToString(xml.documentElement));

  zip.file('word/_rels/document.xml.rels', xmlString);
};

const updateMediaContent = async function(zip, count, _media) {
  let xmlString = await zip.file('word/document.xml').async('string');
  xmlString = xmlString.replace(new RegExp(_media[count].oldRelID + '"', 'g'), _media[count].oldRelID + '_' + count + '"');
  zip.file('word/document.xml', xmlString);
};

const copyMediaFiles = async function(base, _media, _files) {
  for (const media in _media) {
    const content = await _files[_media[media].fileIndex].file(_media[media].oldTarget).async('uint8array');
    base.file('word/' + _media[media].newTarget, content);
  }
};

const mergeContentTypes = async function(files, _contentTypes) {
  for (let i = 0; i < files.length; i++) {
    const zip = files[i];
    const xmlString = await zip.file('[Content_Types].xml').async('string');
    const xml = new DOMParser().parseFromString(xmlString, 'text/xml');

    const childNodes = xml.getElementsByTagName('Types')[0].childNodes;

    for (const node in childNodes) {
      if (/^\d+$/.test(node) && childNodes[node].getAttribute) {
        const contentType = childNodes[node].getAttribute('ContentType');
        if (!_contentTypes[contentType])
          _contentTypes[contentType] = childNodes[node].cloneNode();
      }
    }
  }
};

const mergeRelations = async function(files, _rel) {
  for (let i = 0; i < files.length; i++) {
    const zip = files[i];
    const xmlString = await zip.file('word/_rels/document.xml.rels').async('string');
    const xml = new DOMParser().parseFromString(xmlString, 'text/xml');

    const childNodes = xml.getElementsByTagName('Relationships')[0].childNodes;

    for (const node in childNodes) {
      if (/^\d+$/.test(node) && childNodes[node].getAttribute) {
        const Id = childNodes[node].getAttribute('Id');
        const Target = childNodes[node].getAttribute('Target');
        if (!_rel[Id]) {
          let targetExists = false;

          for (const rel in _rel) {
            if (_rel[rel].getAttribute('Target') === Target) {
              targetExists = true;
              break;
            }
          }

          if (!targetExists) {
            _rel[Id] = childNodes[node].cloneNode();
          }
        }
      }
    }
  }
};

const generateContentTypes = async function(zip, _contentTypes) {
  let xmlString = await zip.file('[Content_Types].xml').async('string');
  const xml = new DOMParser().parseFromString(xmlString, 'text/xml');
  const serializer = new XMLSerializer();

  const types = xml.documentElement.cloneNode();

  for (const node in _contentTypes) {
    types.appendChild(_contentTypes[node]);
  }

  const startIndex = xmlString.indexOf('<Types');
  xmlString = xmlString.replace(xmlString.slice(startIndex), serializer.serializeToString(types));

  zip.file('[Content_Types].xml', xmlString);
};

const generateRelations = async function(zip, _rel) {
  let xmlString = await zip.file('word/_rels/document.xml.rels').async('string');
  const xml = new DOMParser().parseFromString(xmlString, 'text/xml');
  const serializer = new XMLSerializer();

  const types = xml.documentElement.cloneNode();

  for (const node in _rel) {
    types.appendChild(_rel[node]);
  }

  const startIndex = xmlString.indexOf('<Relationships');
  xmlString = xmlString.replace(xmlString.slice(startIndex), serializer.serializeToString(types));

  zip.file('word/_rels/document.xml.rels', xmlString);
};

const prepareStyles = async function (files) {
  const serializer = new XMLSerializer();

  for (let index = 0; index < files.length; index++) {
    const zip = files[index];
    let xmlString = await zip.file('word/styles.xml').async('string');
    let xml = new DOMParser().parseFromString(xmlString, 'text/xml');
    const nodes = xml.getElementsByTagName('w:style');

    for (const node in nodes) {
      if (/^\d+$/.test(node) && nodes[node].getAttribute) {
        const styleId = nodes[node].getAttribute('w:styleId');
        nodes[node].setAttribute('w:styleId', styleId + '_' + index);
        const basedonStyle = nodes[node].getElementsByTagName('w:basedOn')[0];
        if (basedonStyle) {
          const basedonStyleId = basedonStyle.getAttribute('w:val');
          basedonStyle.setAttribute('w:val', basedonStyleId + '_' + index);
        }

        const w_next = nodes[node].getElementsByTagName('w:next')[0];
        if (w_next) {
          const w_next_ID = w_next.getAttribute('w:val');
          w_next.setAttribute('w:val', w_next_ID + '_' + index);
        }

        const w_link = nodes[node].getElementsByTagName('w:link')[0];
        if (w_link) {
          const w_link_ID = w_link.getAttribute('w:val');
          w_link.setAttribute('w:val', w_link_ID + '_' + index);
        }

        const numId = nodes[node].getElementsByTagName('w:numId')[0];
        if (numId) {
          const numId_ID = numId.getAttribute('w:val');
          numId.setAttribute('w:val', numId_ID + index);
        }

        await updateStyleRel_Content(zip, index, styleId);
      }
    }

    const startIndex = xmlString.indexOf('<w:styles ');
    xmlString = xmlString.replace(xmlString.slice(startIndex), serializer.serializeToString(xml.documentElement));

    zip.file('word/styles.xml', xmlString);
  }
};

const mergeStyles = async function(files, _styles) {
  for (let i = 0; i < files.length; i++) {
    const zip = files[i];
    let xmlString = await zip.file('word/styles.xml').async('string');
    xmlString = xmlString.substring(xmlString.indexOf('<w:style '), xmlString.indexOf('</w:styles'));

    _styles.push(xmlString);
  }
};

const updateStyleRel_Content = async function(zip, fileIndex, styleId) {
  let xmlString = await zip.file('word/document.xml').async('string');
  xmlString = xmlString.replace(new RegExp('w:val="' + styleId + '"', 'g'), 'w:val="' + styleId + '_' + fileIndex + '"');
  zip.file('word/document.xml', xmlString);
};

const generateStyles = async function(zip, _style, _fonts, _fontsize) {
  let xmlString = await zip.file('word/styles.xml').async('string');
  const startIndex = xmlString.indexOf('<w:style ');
  const endIndex = xmlString.indexOf('</w:styles>');

  xmlString = xmlString.replace(xmlString.slice(startIndex, endIndex), _style.join(''));

  // Setting default font and font size
  const xml = new DOMParser().parseFromString(xmlString, 'text/xml');
  const nodes = xml.getElementsByTagName('w:docDefaults')[0];
  if (nodes) {
    const rPrDefault = nodes.getElementsByTagName('w:rPrDefault')[0];
    let rPr = rPrDefault.getElementsByTagName('w:rPr')[0];
    if (rPr) {
      const rFonts = rPr.getElementsByTagName('w:rFonts')[0];
      if (rFonts) {
        rFonts.setAttribute('w:ascii', _fonts);
        rFonts.setAttribute('w:hAnsi', _fonts);
        rFonts.setAttribute('w:cs', _fonts);
        rFonts.setAttribute('w:eastAsia', _fonts);
      }
      const cz = rPr.getElementsByTagName('w:sz')[0];
      if (cz) cz.setAttribute('w:val', _fontsize);

      const czCs = rPr.getElementsByTagName('w:szCs')[0];
      if (czCs) czCs.setAttribute('w:val', _fontsize);
    }
  }

  xmlString = new XMLSerializer().serializeToString(xml.documentElement);
  zip.file('word/styles.xml', xmlString);
};

module.exports = { DocxMerger };
