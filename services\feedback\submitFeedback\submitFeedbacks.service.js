const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./submitFeedbacks.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");

module.exports = {
  name: 'submitFeedbacks',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "userId": "users.get",
      "toolId": "tools.get",
      "groupFeedbackId": "groupFeedbacks.get"
    },
    populateOptions: ["userId", "toolId", "groupFeedbackId"]
  },

  hooks: {},

  actions: {},
  methods: {},
  events: {}
};
