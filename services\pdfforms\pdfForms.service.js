const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./pdfForms.model");
const BaseService = require("../../mixins/baseService.mixin");
const AuthRole = require("../../mixins/authRole.mixin");
const { USER_CODES } = require("../../constants/constant");

module.exports = {
  name: "pdfforms",
  mixins: [DbMongoose(Model), BaseService, AuthRole],
  settings: {
    JWT_SECRET: process.env.JWT_SECRET || "jwt-tradar-secret",
  },
  actions: {
    getAllWithoutPagination:{
      role: USER_CODES.NORMAL,
    },
    create: {
      rest: {
        method: "POST",
        path: "/"
      },
      auth: "required",
      params: {},
      async handler(ctx) {
        const entity = ctx.params;
        entity.ownerId = ctx.meta.user?._id?.toString();

        await this.validateEntity(entity);

        return await this.adapter.insert(entity);
      }
    },
  },
  methods: {},

};
