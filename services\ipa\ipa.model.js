const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {IPA, FILE} = require("../../constants/dbCollections");

const schema = new Schema(
  {
    ipa: {type: String},
    word: {type: String},
    audioFileId: {type: Schema.Types.ObjectId, ref: FILE},
    audioUrl: {type: String},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
module.exports = mongoose.model(IPA, schema, IPA);
