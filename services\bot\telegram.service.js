"use strict";

const TelegramBot = require('node-telegram-bot-api');
module.exports = {
  name: "telegram",
  dependencies: ["settings"],

  events: {
    "mail.sent"(ctx) {
      this.logger.info("Nhận được sự kiện 'mail.sent'. <PERSON><PERSON> gửi thông báo Telegram...");
      const {to, subject, status, error, data} = ctx.params;
      const {
        userEmail, satisfaction, time, helpfulness, upgrade, opinion, feature,
        fullName, phone, email, company, userName, packageName, paymentDate, paymentMethod, totalAmount
      } = data;

      const fields = [
        {label: "Người nhận", value: to},
        {label: "Chủ đề", value: subject},
        {label: "Người dùng", value: userEmail},
        {label: "Tính năng", value: feature},
        {label: "Feedback time", value: time},
        {label: "Mức độ hài lòng", value: satisfaction},
        {label: "<PERSON><PERSON><PERSON> độ hữu ích", value: helpfulness},
        {label: "<PERSON><PERSON><PERSON> nâng cấp", value: upgrade},
        {label: "Ý kiến", value: opinion},
        {label: "Tên liên hệ", value: fullName},
        {label: "Số điện thoại", value: phone},
        {label: "Email", value: email},
        {label: "Tên doanh nghiệp", value: company},
        {label: "Tên người thanh toán", value: userName},
        {label: "Gói dịch vụ", value: packageName},
        {label: "Ngày thanh toán", value: paymentDate},
        {label: "Hình thức thanh toán", value: paymentMethod},
        {label: "Tổng số tiền", value: totalAmount},
      ];

      let message = `📧 **Thông báo gửi Email** 📧\n\n`;
      fields.forEach(({label, value}) => {
        if (value) message += `**${label}:** ${value}\n`;
      });

      message += `**Trạng thái:** ${status === 'success' ? 'Thành công ✅' : `Thất bại ❌\n**Lỗi:** ${error || 'Không rõ'}`}`;
      message += `\n\n*Thời gian:* ${new Date().toLocaleString('vi-VN')}`;

      this.sendMessage(message);
    },

    "error-report.sent"(ctx) {
      console.log("Nhận được sự kiện 'error-report.sent'. Đang gửi thông báo Telegram...");
      const {
        userId, imageIds, videoId, description, impactLevel, errorUrl, createdAt
      } = ctx.params;

      // Tạo message với thông tin chi tiết
      let message = `🚨 **THÔNG BÁO BÁO LỖI PHẦN MỀM** 🚨\n\n`;

      if (userId && userId.email) {
        message += `👤 **Người gửi:** ${userId.email}\n`;
      }
      if (userId && userId.fullName) {
        message += `📝 **Tên:** ${userId.fullName}\n`;
      }
      if (description) {
        message += `📋 **Mô tả lỗi:** ${description}\n`;
      }
      if (impactLevel) {
        const impactEmoji = impactLevel === 'high' ? '🔴' : impactLevel === 'medium' ? '🟡' : '🟢';
        const impactLabel = impactLevel === 'high' ? 'Cao' : impactLevel === 'medium' ? 'Trung bình' : 'Thấp';
        message += `${impactEmoji} **Mức độ nghiêm trọng:** ${impactLabel}\n`;
      }
      if (errorUrl) {
        message += `🔗 **URL lỗi:** ${errorUrl}\n`;
      }
      if (imageIds && imageIds.length > 0) {
        message += `📷 **Số ảnh đính kèm:** ${imageIds.length}\n`;
      }
      if (videoId) {
        message += `🎥 **Video đính kèm:** Có\n`;
      }
      if (createdAt) {
        const date = new Date(createdAt);
        message += `⏰ **Thời gian:** ${date.toLocaleString('vi-VN')}\n`;
      }
      console.log("message", message);
      this.sendMessage(message);
    }

  },

  methods: {
    async sendMessage(messageText) {
      const settings = await this.broker.call("settings.findOne");
      const {telegramChatId} = settings
      if (!this.bot || !telegramChatId) {
        this.logger.warn("Telegram Bot chưa được khởi tạo hoặc thiếu Chat ID.");
        return;
      }
      try {
        await this.bot.sendMessage(telegramChatId, messageText, {parse_mode: 'Markdown'}); // Hoặc 'HTML'
        this.logger.info(`Đã gửi thông báo Telegram đến Chat ID: ${telegramChatId}`);
      } catch (error) {
        this.logger.error("Lỗi khi gửi tin nhắn Telegram:", error.message);
      }
    }
  },

  async created() {
  },

  async started() {
    const settings = await this.broker.call("settings.findOne");
    const {telegramBotToken, telegramChatId} = settings
    if (!telegramBotToken) {
      this.logger.warn("Thiếu TELEGRAM_BOT_TOKEN! Service Telegram sẽ không hoạt động.");
      this.bot = null;
      return;
    }
    if (!telegramChatId) {
      this.logger.warn("Thiếu TELEGRAM_CHAT_ID! Service Telegram sẽ không thể gửi tin nhắn.");
    }

    this.bot = new TelegramBot(telegramBotToken);
    this.logger.info("Telegram Bot đã được khởi tạo.");
  },

  async stopped() {
    this.bot = null;
    this.logger.info("Telegram service đã dừng và dọn dẹp tài nguyên.");
  },
};
