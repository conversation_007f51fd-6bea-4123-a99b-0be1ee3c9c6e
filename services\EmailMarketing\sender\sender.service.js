const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const nodemailer = require('nodemailer');
const {getConfig} = require('../../../config/config');

module.exports = {
  name: 'mktsender',
  mixins: [FunctionsCommon],

  settings: {},

  hooks: {
    before: {
      // Kiểm tra permission cho tất cả actions trừ khi được gọi từ scheduler
      '*': function (ctx) {
        // Bỏ qua permission check nếu được gọi từ scheduler
        if (ctx.meta.caller === 'mktscheduler' || ctx.meta.internal === true) {
          return ctx;
        }
      }
    }
  },

  actions: {
    /**
     * Send an email
     */
    send: {
      params: {
        to: {type: 'string'},
        subject: {type: 'string'},
        html: {type: 'string'},
        campaignId: {type: 'string', optional: true},
        userId: {type: 'string', optional: true},
        trackingId: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const {to, subject, html, trackingId, logId} = ctx.params;
        try {
          // Create a log entry if trackingId is not provided
          let finalTrackingId = trackingId;

          // Add tracking pixel and process links
          const processedHtml = this.addTracking(html, finalTrackingId);
          console.log("logId", logId)
          console.log("trackingId", trackingId)
          console.log("processedHtml", processedHtml)
          // Send the email
          const result = await this.sendEmail(to, subject, processedHtml);

          // Update log status if we created a log
          if (logId) {
            await this.broker.call('mktlogs.updateStatus', {
              id: logId,
              status: 'sent'
            });
          }

          return {
            success: true,
            messageId: result.messageId,
            trackingId: finalTrackingId
          };
        } catch (error) {
          // Log the error
          this.logger.error('Email sending failed:', error);
          // Update log status if we have a log ID
          if (logId) {
            await this.broker.call('mktlogs.updateStatus', {
              id: logId,
              status: 'failed',
              errorMessage: error.message
            });
          }

          throw new MoleculerClientError(`Failed to send email: ${error.message}`, 500);
        }
      }
    },

    /**
     * Send a campaign email to a user
     */
    sendCampaignEmail: {
      rest: {
        method: 'POST',
        path: '/campaign'
      },
      params: {
        campaignId: {type: 'string'},
        userId: {type: 'string'},
        email: {type: 'string'},
        data: {type: 'object', optional: true},
      },
      async handler(ctx) {
        const {campaignId, userId, email, data = {}} = ctx.params;

        // Get the campaign
        const campaign = await this.broker.call('mktcampaigns.get', {id: campaignId});
        if (!campaign) {
          throw new MoleculerClientError('Campaign not found', 404);
        }

        // Get the template
        const template = await this.broker.call('mkttemplates.get', {id: campaign.templateId});
        if (!template) {
          throw new MoleculerClientError('Template not found', 404);
        }

        // Get user data if userId is provided
        let userData = {};
        if (userId) {
          const user = await this.broker.call('users.get', {id: userId});
          if (user) {
            userData = {
              name: user.fullName,
              email: user.email,
              userFullname: user.fullName,
              account: user.email,
            };

            // Add subscription data if available
            if (user.subscription) {
              userData.trial_end_date = user.subscription.endDate;
            }
          }
        }

        // Merge data
        const mergedData = {
          ...userData,
          ...data,
          supportEmail: getConfig(process.env.NODE_ENV).supportEmail,
        };

        // Replace variables in subject and content
        const subject = this.replaceVariables(template.subject, mergedData);
        const html = this.replaceVariables(template.content, mergedData);

        // Send the email
        return this.actions.send({
          to: email,
          subject,
          html,
          campaignId,
          userId
        }, {parentCtx: ctx});
      }
    },

    /**
     * Send a test email
     */
    sendTest: {
      rest: {
        method: 'POST',
        path: '/test'
      },
      params: {
        templateId: {type: 'string'},
        to: {type: 'string'},
        data: {type: 'object', optional: true},
      },
      async handler(ctx) {
        const {templateId, to, data = {}} = ctx.params;

        // Get the template
        const template = await this.broker.call('mkttemplates.get', {id: templateId});
        if (!template) {
          throw new MoleculerClientError('Template not found', 404);
        }

        // Generate sample data for variables
        const sampleData = await this.broker.call('mkttemplates.preview', {
          id: templateId,
          data
        });
        // Send the email
        return this.actions.send({
          to,
          subject: sampleData.subject,
          html: sampleData.content
        }, {parentCtx: ctx});
      }
    },
  },

  methods: {
    /**
     * Send an email using nodemailer
     */
    async sendEmail(to, subject, html) {
      const config = getConfig(process.env.NODE_ENV);
      const transporter = nodemailer.createTransport(config.mail);

      const mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`,
        to,
        subject,
        html
      };

      return transporter.sendMail(mailOptions);
    },

    /**
     * Replace variables in a string with their values
     */
    replaceVariables(text, data) {
      return text.replace(/{([a-zA-Z0-9_]+)}/g, (match, variable) => {
        return data[variable] !== undefined ? data[variable] : match;
      });
    },

    /**
     * Add tracking pixel and process links in HTML content
     */
    addTracking(html, trackingId) {
      if (!trackingId) return html;

      const config = getConfig(process.env.NODE_ENV);
      const baseUrl = config.domain || 'https://clickee-dev.thinklabs.com.vn';

      // Add tracking pixel với format yêu cầu
      const trackingPixel = `<img src="${baseUrl}/api/mktcampaigns/track/${trackingId}" width="1" height="1" style="opacity:0;" alt="." />`;

      // Process links to add click tracking
      let processedHtml = html.replace(/<a\s+(?:[^>]*?\s+)?href=["']([^"']*)["']([^>]*)>/gi, (match, url, rest) => {
        // Skip tracking for anchor links and mailto links
        if (url.startsWith('#') || url.startsWith('mailto:')) {
          return match;
        }
        // Encode the URL
        url = url.replace(/{/g, '').replace(/}/g, '');
        const encodedUrl = encodeURIComponent(url);
        const trackingUrl = `${baseUrl}/api/mkttracking/${trackingId}/click?url=${encodedUrl}`;

        return `<a href="${trackingUrl}"${rest}>`;
      });

      // Add tracking pixel at the end of the body, hoặc cuối HTML nếu không có </body>
      if (processedHtml.includes('</body>')) {
        processedHtml = processedHtml.replace('</body>', `${trackingPixel}</body>`);
      } else {
        // Nếu không có thẻ </body>, thêm vào cuối HTML
        processedHtml = processedHtml + trackingPixel;
      }

      return processedHtml;
    }
  },

  events: {
    /**
     * Handle campaign start event
     */
    'email.campaign.started'(payload) {
      this.processCampaign(payload.campaignId);
    }
  },

  /**
   * Process a campaign by sending emails to all users in the target groups
   */
  async processCampaign(campaignId) {
    try {
      // Get the campaign
      const campaign = await this.broker.call('mktcampaigns.get', {id: campaignId});
      if (!campaign || campaign.status !== 'running') {
        return;
      }

      // Get all target groups
      const targetGroups = campaign.targetGroups || [];
      if (targetGroups.length === 0) {
        return;
      }

      // Process each group
      for (const groupId of targetGroups) {
        // Get users in the group
        const users = await this.broker.call('mktgroups.getUsers', {id: groupId});

        // Send email to each user
        for (const user of users.rows) {
          if (user.email) {
            try {
              await this.broker.call('mktsender.sendCampaignEmail', {
                campaignId,
                userId: user._id,
                email: user.email
              });

              // Add a small delay to avoid overwhelming the email server
              await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
              this.logger.error(`Failed to send email to ${user.email}:`, error);
            }
          }
        }
      }

      // Update campaign status to completed
      await this.broker.call('mktcampaigns.updateStatus', {
        id: campaignId,
        status: 'completed'
      });
    } catch (error) {
      this.logger.error(`Failed to process campaign ${campaignId}:`, error);

      // Update campaign status to failed
      await this.broker.call('mktcampaigns.updateStatus', {
        id: campaignId,
        status: 'failed'
      });
    }
  }
};
