const sdk = require("microsoft-cognitiveservices-speech-sdk");
const path = require("path");
const fs = require("fs");
const wav = require("wav");
const {getAccuracyScore} = require("../../microsoft/getAccuracyScoreForShadowing");
const FileMixin = require("../../../mixins/file.mixin");
const FunctionCommonMixin = require("../../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {getConfig} = require("../../../config/config");
const config = getConfig(process.env.NODE_ENV);
const storageDir = path.join(__dirname, "storage");
const {MoleculerClientError} = require("moleculer").Errors;
const ffmpeg = require("fluent-ffmpeg");
const {INPUT_TYPE} = require("../../../constants/constant");
const globalState = new Map();

module.exports = {
  name: "microsoft-shadowing",
  mixins: [FileMixin, FunctionCommonMixin],

  actions: {
    getAudioUrl: {
      rest: 'GET /link/:fileName',
      async handler(ctx) {
        try {
          const {fileName} = ctx.params;
          const filePath = this.getFilePath(fileName, storageDir);
          if (!fs.existsSync(filePath)) return new MoleculerClientError(i18next.t("error_file_not_found"), 404);
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": "audio/wav",
            "Content-Length": stat.size,
            "Content-Disposition": `attachment;filename=${fileName}`
          };
          return fs.createReadStream(filePath, {});
        } catch (e) {
          throw new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },

  },

  methods: {
    async saveAudioFile(state, filePath, socket) {
      try {
        const {audioChunks, userId, inputData} = state;

        // Define file names
        const wavFileName = `shadowing_${userId}_${inputData.segment._id}.wav`;
        const mp3FileName = `shadowing_${userId}_${inputData.segment._id}.mp3`;

        const wavFilePath = this.getFilePath(wavFileName, storageDir);
        const mp3FilePath = this.getFilePath(mp3FileName, storageDir);

        // Create WAV file
        const fileStream = fs.createWriteStream(wavFilePath);
        const wavWriter = new wav.Writer({
          sampleRate: 16000,
          channels: 1,
          bitDepth: 16,
        });

        wavWriter.pipe(fileStream);
        audioChunks.forEach((chunk) => wavWriter.write(chunk));
        wavWriter.end();

        await new Promise((resolve, reject) => {
          fileStream.on("finish", resolve);
          fileStream.on("error", reject);
        });

        console.log(`WAV file saved: ${wavFilePath}`);

        // Convert WAV to MP3
        await new Promise((resolve, reject) => {
          ffmpeg(wavFilePath)
            .toFormat("mp3")
            .audioCodec("libmp3lame")
            .audioBitrate(128)
            .on("end", () => {
              console.log(`MP3 file saved: ${mp3FilePath}`);
              fs.unlinkSync(wavFilePath); // Delete WAV after conversion
              resolve();
            })
            .on("error", (err) => reject(err))
            .save(mp3FilePath);
        });

        // Store MP3 file in database
        const file = await this.broker.call("resources.createFromRecordAudio", {
          audioChunks,
          userId,
          fileName: mp3FileName, // Save MP3 file info in DB
        });
        console.log("file", file)
        // Send MP3 file URL via socket


        const {transcript, standardResults} = await this.broker.call("whisper.streamTranscript", {
          audioPath: mp3FilePath,
          socket
        });

        socket.send({state: "recognized_text", recognizedText: transcript, standardResults});
        inputData.transcript = transcript;
        inputData.studentAudioId = file._id;
        inputData.standardResults = standardResults;
        const audioUrl = `${config.domain}/api/microsoft/link/${mp3FileName}`;
        if (globalState[socket.id]) globalState[socket.id].fileId = file._id;
        socket.send({state: "audio_file_saved", audioUrl, fileId: file._id});
        await this.handleFeedback(inputData, socket, socket.id)
      } catch (e) {
        console.log(e)
      }
    },

    async handleFeedback(inputData, socket, connectionId) {
      try {
        const {
          accuracyScore,
          premiumResults,
          segment,
          exerciseId,
          studentAudioId,
          transcript,
          standardResults
        } = inputData;
        const werValue = this.calculateWER(segment.text, transcript)

        const submission = await this.broker.call("exercisesubmissions.shadowingSubmission", {
          accuracyScore: accuracyScore || 100 - (werValue * 100).toFixed(2) || 0,
          premiumResults,
          studentId: globalState[connectionId].userId,
          segment,
          exerciseId,
          studentAudioId,
          standardResults,
          studentAnswer: transcript
        });
        socket.emit("finish-recognition", {submission});
      } catch (error) {
        console.error("Error handling feedback:", error);
        socket.send({state: "error", message: "An error occurred while processing feedback."});
      }
    },
  },


  events: {
    shadowingConnected: {
      async handler(socket) {
        const connectionId = socket.id;
        this.connectionState = {};

        this.logger.info(`Client connected: ${connectionId}`);
        // const {speechKey, serviceRegion, confidenceThreshold} = await this.broker.call("settings.findOne");
        // const format = sdk.AudioStreamFormat.getWaveFormat(16000, 16, 1, sdk.AudioFormatTag.PCM);
        this.connectionState[connectionId] = {
          audioBufferQueue: [],
          audioChunks: [],
          // audioStream: sdk.AudioInputStream.createPushStream(format),
          inputData: {},
          userId: null,
          startRecognition: false,
          isRecording: true,
          processInterval: null,
          // callbackFunction: (data) => this.handleFeedback(data, socket, connectionId),
        };

        const state = this.connectionState[connectionId];
        globalState[connectionId] = state;
        // state.processInterval = setInterval(() => {
        //   if (state.audioBufferQueue.length > 0) {
        //     while (state.audioBufferQueue.length > 0) {
        //       const audioData = state.audioBufferQueue.shift();
        //       try {
        //         state.audioStream.write(audioData);
        //       } catch (err) {
        //         console.error("Error writing audio data to stream:", err);
        //       }
        //     }
        //
        //     if (!state.startRecognition) {
        //       getAccuracyScore({
        //         socket, speechKey, serviceRegion, confidenceThreshold,
        //         audioStream: state.audioStream,
        //         inputData: state.inputData,
        //         callback: state.callbackFunction
        //       });
        //       state.startRecognition = true;
        //     }
        //   } else if (!state.isRecording) {
        //     clearInterval(state.processInterval);
        //     state.audioStream.close();
        //     console.log(`Stopped processing for client: ${connectionId}`);
        //   }
        // }, 1000);

        socket.on("audio", (data) => {
          state.audioBufferQueue.push(data.buffer);
          state.audioChunks.push(data.buffer);
          state.inputData = data.inputData;
          state.userId = data.userId;
        });

        socket.on("close-recording", async () => {
          console.log(`Closing recording for client: ${connectionId}`);
          state.isRecording = false;
          const outputFilePath = this.getFilePath(`shadowing_${state.userId}_${state.inputData.segment._id}.wav`, storageDir);
          await this.saveAudioFile(state, outputFilePath, socket);
        });

        socket.on("disconnect", () => {
          this.logger.info(`Client disconnected: ${connectionId}`);
          // clearInterval(state.processInterval);
          // state.audioStream.close();
          delete this.connectionState[connectionId];
        });

        // Gửi sự kiện server_ready để thông báo cho client rằng server đã sẵn sàng nhận dữ liệu
        socket.emit("server_ready");
      }
    }
  }
};
