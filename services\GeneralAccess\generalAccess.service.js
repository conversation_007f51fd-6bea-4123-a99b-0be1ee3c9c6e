"use strict";
const GeneralAccessModel = require("./generalAccess.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES, PERMISSION_ACCESS} = require("../../constants/constant");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "generalAccess",
  mixins: [<PERSON>b<PERSON>ongoose(GeneralAccessModel), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      "folderId": 'folders.get',
      "projectId": 'projects.get',
      "sessionId": 'spksessions.get',
      "organizationId": 'organizations.get',
      "userId": 'users.get',
    },
    populateOptions: ["folderId", "projectId", "organizationId", "userId", "sessionId"]
  },
  hooks: {
    before: {
      "restricted|organisational|anyOneWithLink": ["checkObjectExist", "checkPermission"],
    },
    after: {
      "*": "activityLogger",
    }
  },
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    },
    restricted: {
      rest: {
        method: "POST",
        path: "/restricted",
      },
      auth: "required",
      activityLogger: true,
      async handler(ctx) {
        const {folderId, projectId, sessionId} = ctx.params;

        if (folderId) {
          this.handleAccessProjectBelongToFolder(folderId, "RESTRICTED");
        }

        await this.adapter.removeMany({folderId, projectId, sessionId});
        await ctx.call("share.unshareGeneral", {folderId, projectId, sessionId, isGeneral: true});
        const newGenerelAccess = {
          typeAccess: "RESTRICTED",
          folderId, projectId, sessionId
        };
        return this.adapter.insert(newGenerelAccess);
      }
    },
    anyOneWithLink: {
      rest: {
        method: "POST",
        path: "/link",
      },
      auth: "required",
      activityLogger: true,
      async handler(ctx) {
        const {folderId, projectId, sessionId, permission} = ctx.params;
        if (folderId) {
          this.handleAccessProjectBelongToFolder(folderId, "ANYONE_WITH_LINK", permission);
        }

        await this.adapter.removeMany({folderId, projectId});
        const newGenerelAccess = {
          typeAccess: "ANYONE_WITH_LINK",
          folderId, projectId, permission, sessionId,
        };
        return this.adapter.insert(newGenerelAccess);
      }
    },
    organisational: {
      rest: {
        method: "POST",
        path: "/organizational",
      },
      auth: "required",
      activityLogger: true,
      async handler(ctx) {
        const {folderId, projectId, permission, organizationId} = ctx.params;
        if (folderId) {
          this.handleAccessProjectBelongToFolder(folderId, "ORGANIZATIONAL", permission, organizationId);
        }
        await this.adapter.removeMany({folderId, projectId});
        const newGeneral = {
          typeAccess: "ORGANIZATIONAL",
          folderId, projectId, permission, organizationId
        };
        return this.adapter.insertMany(newGeneral);
      }
    }
  },

  /**
   * Events
   */
  events: {
    async "folder.created"(payload, sender, event) {
      this.logger.info("payload", payload, sender, event);
      this.adapter.insert(payload);
    },
    async "project.created"(payload, sender, event) {
      this.logger.info("payload", payload, sender, event);
      this.adapter.insert(payload);
    }

  },

  /**
   * Methods
   */
  methods: {
    async handleAccessProjectBelongToFolder(folderId, typeAccess, permission, organizationId) {

      const projects = await this.broker.call("projects.find", {
        query: {folderId}
      });
      const projectIds = projects.map(({_id}) => _id);
      await this.adapter.removeMany({projectId: {$in: projectIds}});

      const generalAccess = {typeAccess};
      if (permission) {
        generalAccess.permission = permission;
      }
      if (organizationId) {
        generalAccess.organizationId = organizationId;
      }
      const promises = projectIds.map(projectId => {
        this.adapter.insert({...generalAccess, projectId});
      });
      return Promise.all(promises);
    },
    async activityLogger(context, res) {
      const {action, params} = context;
      (action.rawName === "anyOneWithLink") ? action.rawName = "anyoneWithLink" : action.rawName;

      if (action.activityLogger && res && params?.workspaceId) {
        const workspace = await context.call('workspaces.get', {
          id: params?.workspaceId.toString(),
          isDeleted: false
        });

        if (workspace.type === "ORGANIZATIONAL") {
          context.emit('activities.logger', {
            metadata: {
              type: action.rawName,
              permission: params?.permission || res?.permission
            },
            projectId: params?.projectId || res?.projectId,
            folderId: params?.folderId || res?.folderId,
            isDeleted: res?.isDeleted,
            action: "share",
          });
        }
      }
      return res;
    },
    async checkObjectExist(context) {
      const {folderId, projectId} = context.params;

      const folder = folderId ? await context.call("folders.get", {id: folderId}) : {};
      if (folder.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("folder_was_deleted"), 404);
      }

      const project = projectId ? await context.call("projects.get", {id: projectId}) : {};
      if (project.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
      }
    },
    async checkPermission(ctx) {
      const {user} = ctx.meta;
      if (user?.isSystemAdmin) return;

      const {projectId, folderId, sessionId} = ctx.params;
      const id = projectId || folderId || sessionId;
      const entity = projectId ? 'projects' : folderId ? 'folders' : 'spksessions';
      const permission = await ctx.call(`${entity}.permissionAccess`, {id});

      if (![PERMISSION_ACCESS.OWNER, PERMISSION_ACCESS.EDITOR].includes(permission)) {
        const errorKey = folderId ? "dont_have_permission_folder" :  "dont_have_permission_project";
        throw new MoleculerClientError(i18next.t(errorKey), 403);
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
