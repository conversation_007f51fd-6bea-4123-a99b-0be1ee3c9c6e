name: Auto Merge and PR Creation

on:
  push:
    branches:
      - release

jobs:
  merge-and-create-pr:
    runs-on: ubuntu-latest
    steps:
      # Bước 1: Checkout code
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Important for merging

      # Bước 2: <PERSON><PERSON><PERSON> hình git
      - name: Setup Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "hungphandinh"

      # Bước 3: Merge nhánh release vào staging
      - name: Merge release into staging
        run: |
          git fetch origin staging
          git checkout staging
          git merge --no-ff origin/release -m "Merge release into staging [skip ci]"
          git push origin staging

      # # Bước 4: Cài đặt GitHub CLI
      # - name: Install GitHub CLI
      #   run: |
      #     curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
      #     echo "deb [signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
      #     sudo apt update
      #     sudo apt install gh

      # # Bước 5: Tạo Pull Request từ release sang product
      # - name: Create Pull Request from release to product
      #   run: |
      #     gh auth setup-git
      #     gh pr create --base product --head release --title "Update product branch" --body "Merging changes from release to product"
