const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {EXERCISES, FILE, USER} = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    name: {
      type: String,
      required: true,
      maxlength: 100
    },
    difficulty: {
      type: String,
      enum: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
      // required: true
    },
    type: {
      type: String,
      enum: ['dictation', 'shadowing', 'dictation_shadowing'],
      // required: true
    },
    tag: {type: String, required: true},
    timeLimit: {
      type: Number, // Đơn vị: phút
      // required: function () {
      //   return this.type !== 'shadowing';
      // },
      max: 15
    },
    audioId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE,
      required: true
    },
    avatarId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE
    },
    audioDuration: {type: Number, default: 0},
    transcript: {
      type: String,
      // required: function () {
      //   return this.type.includes('dictation');
      // }, // Bắt buộc cho Dictation
      // maxlength: 500 // Giới hạn 500 từ
    },
    segments: [
      {
        start: {type: Number, default: 0},
        end: {type: Number, default: 0},
        text: {type: String, default: ""},
        hiddenWord: {type: String, default: ""}
      },
    ],
    status: {
      type: String,
      enum: ['draft', 'published', 'hidden'],
      default: 'draft'
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
    isDeleted: {type: Boolean, default: false},
    deletedAt: {type: Date},
    versionHistory: [{type: Schema.Types.Mixed}]
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
schema.pre("save", function (next) {
  if (this.versionHistory.length > 3) {
    this.versionHistory = this.versionHistory.slice(-3);
  }
  next();
})
module.exports = mongoose.model(EXERCISES, schema, EXERCISES);
