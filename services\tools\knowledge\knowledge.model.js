const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { KNOWLEDGE } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    name: { type: String },
    content: { type: Schema.Types.Mixed },
    instruction: { type: String },
    isDeleted: { type: Boolean, default: false },
    isOutDated: { type: Boolean, default: false },
    embeddingVector: Array,
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(KNOWLEDGE, schema, KNOWLEDGE);
