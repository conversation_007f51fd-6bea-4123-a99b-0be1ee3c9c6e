const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./docxTemplates.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const path = require("path");
const fs = require("fs");
const storageDir = path.join(__dirname, "storage");
const FileMixin = require("../../mixins/file.mixin");
const carbone = require("carbone");
const i18next = require("i18next");
const mime = require('mime-types');
const { DocxImager } = require("../Report/DocxImager");
const { MoleculerClientError } = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "docxtemplates",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "organizationId": 'organizations.get',
      "baseTemplateId": 'files.get',
      "templateId": 'files.get',
      "templateThumbnailId": 'files.get',
    },
    populateOptions: ["organizationId.avatarId", "baseTemplateId", "templateId", "templateThumbnailId"],
  },

  hooks: {
    before: {
      "publishTemplate|getAllForOrganization|createPreviewFile": "checkPermission",
      "update": "checkPermissionActions"
    }
  },

  actions: {
    update: {
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        const{params, meta} = ctx;
        const{user} = meta;

        const docxTemplate = await this.adapter.findById({_id: params.id.toString()});

        if(!docxTemplate) {
          throw new MoleculerClientError(i18next.t("error_docxtemplate_not_found"), 404);
        }

        if(user?.isSystemAdmin) return ctx

        delete params.isPublic;
        delete params.isDeleted;
        delete params.templateId;
        delete params.templateThumbnailId;
        delete params.organizationId;

        if(user?.role !== USER_CODES.CONTRIBUTOR && user?.role !== USER_CODES.ORG_ADMIN) {
          delete params.name;
          ctx.params = params;
          return ctx;
        }

        if(params?.name && !docxTemplate?.organizationId) {
          throw new MoleculerClientError(i18next.t("error_docxtemplate_not_found"), 404);
        }

        const organization = await ctx.call("organizations.get", {id: docxTemplate?.organizationId.toString()});
        if (!organization) {
          throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
        }

        if (organization?.active === false) {
          throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
        }

        ctx.params = params;
        return ctx;
      }
    },
    remove: {
      permission: true,
      role: USER_CODES.CONTRIBUTOR,
    },
    upload: {
      auth: "required",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const { docxTemplateId, type } = ctx.meta.$multipart;
        ctx.meta.$multipart.folder = "docxtemplates";
        ctx.meta.bypassCapacityAddition = true;
        const file = await ctx.call("files.upload", ctx.params, { meta: ctx.meta });
        const thumbnail = await ctx.call("files.createThumbnailPdf", { id: file._id });

        const updateData = {
          templateId: file._id,
          templateThumbnailId: thumbnail._id
        };
        const docxTemplateUpdated = await this.adapter.updateById(docxTemplateId, updateData);
        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, docxTemplateUpdated);
      }
    },
    uploadOrgTemplate: {
      auth: "required",
      permission: true,
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        const { name, organizationId } = ctx.meta.$multipart;
        const extension = path.extname(ctx.meta.filename);
        ctx.meta.filename = `base_header.${ extension }`;
        ctx.meta.$multipart.folder = "docxtemplates";
        const file = await ctx.call("files.upload", ctx.params, { meta: ctx.meta });
        const thumbnail = await ctx.call("files.createThumbnailPdf", { id: file._id });

        const newDocxTemplate = await this.adapter.insert({
          templateId: file._id,
          templateThumbnailId: thumbnail._id,
          name,
          organizationId
        });

        return await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, newDocxTemplate);
      }
    },

    templateFilePath: {
      rest: "GET /:id/filePath",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const { id } = ctx.params;
          const docxTemplatex = await this.adapter.findById(id);
          // console.log("docxTemplatex", docxTemplatex);
          // await this.adapter.updateById(id, { lastUsed: Date.now() });
          return ctx.call("files.filePath", { id: docxTemplatex?.templateId });
        } catch (e) {
          console.log(e);
        }
      }
    },

    getOne: {
      rest: "GET /getOne",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const query = ctx.params;
          const template = await this.adapter.findOne(query);
          return this.transformDocuments(ctx, { populate: this.settings.populateOptions }, template);
        } catch (e) {
          console.log(e);
        }
      }
    },

    publishTemplate: {
      rest: "POST /publish",
      permission: true,
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        try {
          const { docxTemplateId, organizationId, isOrgPublic } = ctx.params;
          let template = await this.adapter.findById(docxTemplateId);


          const serviceMethod = isOrgPublic ? "publishedtemplate.makePublished" : "publishedtemplate.makeUnpublished";
          await ctx.call(serviceMethod, { docxTemplateId, organizationId });

          const newTemplate = await this.transformDocuments(ctx, { populate: this.settings.populateOptions }, template);
          return { ...newTemplate, isOrgPublic };
        } catch (e) {
          console.log(e);
        }
      }
    },
    getAllForOrganization: {
      rest: "GET /organization/:organizationId/getAll",
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        try {
          const { organizationId } = ctx.params;

          const [clickeeTemplates, orgTemplates, allPublish, orgCustomThumbnails] = await Promise.all([
            ctx.call("docxtemplates.find", {
              query: {
                organizationId: { $exists: false },
                isPublic: true
              }
            }),
            ctx.call("docxtemplates.find", { query: { organizationId } }),
            ctx.call("publishedtemplate.find", { query: { organizationId }, populate: [] }),
            ctx.call("customthumbnail.find", { query: { organizationId }, populate: ["customThumbnailId"] }),
          ]);

          const mapPublish = new Map(allPublish.map(publishedTemplate => [publishedTemplate.docxTemplateId.toString(), publishedTemplate._id]));

          return [...clickeeTemplates, ...orgTemplates].map(item => {
            const itemIdStr = item._id.toString();
            const customThumbnail = orgCustomThumbnails.find(thumbnail => thumbnail.docxTemplateId.toString() === item._id.toString())?.customThumbnailId;

            return {
              ...item,
              isOrgPublic: mapPublish.has(itemIdStr),
              customThumbnail: customThumbnail || {},
            };
          });

        } catch (e) {
          console.error('An error occurred while handling templates:', e);
          throw e;
        }
      }
    },
    getAllForWorkspace: {
      rest: "GET /workspace/:workspaceId/getAll",
      async handler(ctx) {
        const { workspaceId } = ctx.params;
        const { _id: userId } = ctx.meta.user;
        const workspace = await ctx.call("workspaces.get", { id: workspaceId });

        const [clickeeTemplate, publicTemplates, userCustomThumbnails, orgCustomThumbnails] = await Promise.all([
          ctx.call("docxtemplates.find", { query: { isPublic: true } }),
          workspace.type === "ORGANIZATIONAL"
            ? ctx.call("publishedtemplate.find", { query: { organizationId: workspace.organizationId } })
            : Promise.resolve([]),
          ctx.call("customthumbnail.find", { query: { userId: userId }, populate: ["customThumbnailId"] }),
          ctx.call("customthumbnail.find", { query: { organizationId: workspace.organizationId }, populate: ["customThumbnailId"] }),
        ]);

        const docxTemplate =  workspace.type === "ORGANIZATIONAL"
          ? publicTemplates.filter(item => item.docxTemplateId).map(item => item.docxTemplateId)
          : clickeeTemplate;

        return docxTemplate.map(item => {
          const customThumbnail =
            userCustomThumbnails.find(thumbnail => thumbnail.docxTemplateId.toString() === item._id.toString())?.customThumbnailId ||
            orgCustomThumbnails.find(thumbnail => thumbnail.docxTemplateId.toString() === item._id.toString())?.customThumbnailId;

          return {
            ...item,
            customThumbnail: customThumbnail || {}
          }
        });
      }
    },

    getAllForUser: {
      rest: "GET /getAllForUser",
      async handler(ctx) {
        try {
          return await ctx.call("docxtemplates.find", { query: { isPublic: true } });
        } catch (e) {
          console.log(e);
        }
      }
    },

    createPreviewFile: {
      rest: "POST /createPreviewFile",
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        try {
          const { docxTemplateId, organizationId } = ctx.params;

          const templatePath = await this.broker.call("docxtemplates.templateFilePath", { id: docxTemplateId });

          const defaultHeader = await ctx.call('customheader.getDefaultHeader', { docxTemplateId, organizationId });
          const { avatarId, imageId } = defaultHeader?.customHeader || {};

          let fileAfterRender;
          if (imageId) {
            const imagePath = await ctx.call("files.filePath", { id: imageId.toString() });
            fileAfterRender = await this.generateDocumentWithImage(ctx, defaultHeader?.customHeader, templatePath, imagePath, avatarId);
          } else {
            fileAfterRender = await this.generateDocument(ctx, defaultHeader?.customHeader, templatePath);
          }

          const pdfFile = await this.convertDocxToPDF(fileAfterRender);
          return { fileName: path.basename(pdfFile) };
        } catch (e) {
          console.log(e);
        }
      }
    },

    createPreviewTemplate: {
      rest: "POST /createPreviewTemplate",
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        try {
          const { templateId } = ctx.params;
          const templatePath = await this.broker.call("files.filePath", { id: templateId });
          const pdfFile = await this.convertDocxToPDF(templatePath);
          return { fileName: path.basename(pdfFile) };
        } catch (e) {
          console.log(e);
        }
      }
    },
    preview: {
      rest: "GET /:fileName/preview",
      role: USER_CODES.CONTRIBUTOR,
      async handler(ctx) {
        const { fileName, displayName } = ctx.params;
        const filePath = this.getFilePath(fileName, this.getDirPath("preview", storageDir));
        try {
          const stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": mime.lookup(filePath),
            "Content-Length": stat.size,
            "Content-Disposition": 'attachment;filename=' + encodeURI(displayName)
          };

          return fs.createReadStream(filePath, {});
        } catch (err) {
          return new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },
  },
  methods: {
    async addImageToDocx(resultFilePath, imageFilePath, imageId) {
      let docxImager = new DocxImager();
      await docxImager.load(resultFilePath);
      await docxImager.replaceWithLocalImage(imageFilePath, imageId, 'png');
      await docxImager.save(resultFilePath);
    },

    async save(stream, filename, folder) {
      const dirPath = this.getDirPath(folder, storageDir);
      const filePath = this.getFilePath(filename, dirPath);
      return this.saveToLocalStorage(stream, filePath);
    },
    async generateDocumentWithImage(ctx, data, templateFilePath, imagePath, replaceImageId) {
      const fileAfterGen = await this.generateDocument(ctx, data, templateFilePath);
      await this.addImageToDocx(fileAfterGen, imagePath, replaceImageId);
      return fileAfterGen;
    },
    async generateDocument(ctx, data, templateFilePath, convertToPdf = false) {
      const options = {
        renderPrefix: 'report',
        reportName: "Report",
        timezone: 'Asia/Saigon',
        ...(convertToPdf && { convertTo: 'pdf' })
      };
      return new Promise((resolve, reject) => {
        carbone.render(templateFilePath, data, options, (err, result) => {
          if (err) reject(err);
          resolve(result);
        });
      });
    },

    async convertDocxToPDF(inputFilePath) {
      const fileBuffer = fs.readFileSync(inputFilePath);
      const options = {
        convertTo: 'pdf',
        extension: 'docx',
      };
      const outPDFPath = this.getFilePath(`report_${ this.getUniqueID() }.pdf`, this.getDirPath("preview", storageDir));

      return new Promise((resolve, reject) => {
        carbone.convert(fileBuffer, options, function (err, result) {
          if (err) return reject(err);
          fs.writeFileSync(outPDFPath, result);
          resolve(outPDFPath);
        });
      });
    },
    async checkPermission(context) {
      const {params, meta, action} = context;
      const {user} = meta;
      if (user?.isSystemAdmin) return;

      if(action?.permission) {
        let organizationId = params?.organizationId || context.meta.$multipart?.organizationId;

        if(params?.id) {
          const docxTemplate = await this.adapter.findById(params.id);
          if(!docxTemplate) {
            throw new MoleculerClientError(i18next.t("error_docxtemplate_not_found"), 404);
          }

          organizationId = docxTemplate?.organizationId
        }

        if (user?.organizationId?.toString() !== organizationId?.toString()) {
          throw new MoleculerClientError(i18next.t("you_need_to_be_an_admin"), 403, "FORBIDDEN");
        }

        const organization = await context.call("organizations.getOne", {id: organizationId});
        if (!organization) {
          throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
        }

        if (organization?.active === false) {
          throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
        }
      }
    }
  },
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
