const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { ORGANIZATION, FILE } = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({
  name: { type: String, required: true, validate: /\S+/ },
  email: {
    type: String, trim: true, unique: true, index: true, lowercase: true,
    required: "Please fill in an email"
  },
  avatarId:{type: Schema.Types.ObjectId, ref: FILE},
  isDeleted: { type: Boolean, default: false },
  active: { type: Boolean, default: true },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(ORGANIZATION, schema, ORGANIZATION);

