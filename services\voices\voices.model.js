const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {OPENAI_VOICE, FILE} = require("../../constants/dbCollections");

const schema = new Schema(
  {
    name: {type: String},
    code: {type: String, validate: /\S+/},
    isPublic: {type: Boolean, default: false},
    voiceFileId: {type: Schema.Types.ObjectId, ref: FILE},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);
module.exports = mongoose.model(OPENAI_VOICE, schema, OPENAI_VOICE);
