"use strict";
const CronJob = require('moleculer-cron');
const {CronTime} = require('cron');

/** @type {ServiceSchema} */
module.exports = {
  name: "cronjobs",
  mixins: [CronJob],
  dependencies: ['settings'],
  settings: {
    runOnInit: true,
    cronJobs: [
      // {
      //   // Define your cron expression (runs every 5 minutes)
      //   name: "update-status-fine-tuning",
      //   cronTime: '*/2 * * * *',
      //   async onTick() {
      //     try {
      //       console.log('Job update status fine-tuning status');
      //       await this.broker.call('finetuning.updateStatus');
      //     } catch (error) {
      //       console.error('Error:', error.message);
      //     }
      //   },
      // },
      {
        // Define your cron expression (runs at 1h every day)
        name: "clear-storage",
        cronTime: '0 1 * * *',
        async onTick() {
          try {
            console.log('Job clear audio');
            await this.broker.call('audios.clearAudio');
            console.log('Job clear image');
            await this.broker.call('images.clearImage');
            console.log('Job clear file student upload');
            await this.broker.emit('jobClearFileStudentUpload');
          } catch (error) {
            console.error('Error:', error.message);
          }
        },
      },
      {
        // Define your cron expression (runs at 0h every day)
        name: "update-permission-user",
        cronTime: '0 0 * * *',

        async onTick() {
          try {
            const currentDate = new Date();
            const currentDay = currentDate.getDate();

            // Unactive subscription expired today
            await this.broker.call('subscriptions.updateMany', {
              query: {endDate: {$lte: new Date(currentDate.setHours(23, 59, 59, 999))}, status: "ACTIVE"},
              update: {
                status: "INACTIVE"
              }
            });

            console.log('Job update permission user');

            const subscriptions = await this.broker.call('subscriptions.find', {
              query: {
                endDate: {$gt: new Date(currentDate.setHours(0, 0, 0, 0))},
                $expr: {$eq: [{$dayOfMonth: "$startDate"}, currentDay]},
                status: "ACTIVE"
              }
            });

            const userIds = [...new Set(subscriptions.map(row => row.customerId?.userId).filter(Boolean))];

            const users = await this.broker.call('users.find', {query: {_id: {$in: userIds}}});
            const {students, otherUsers} = users.reduce((acc, user) => {
              acc[user.type === 'student' ? 'students' : 'otherUsers'].push(user._id);
              return acc;
            }, {students: [], otherUsers: []});

            const [studentPermissions, otherUsersPermissions] = await Promise.all([
              this.broker.call('permissions.find', {query: {userId: {$in: students}}}),
              this.broker.call('permissions.find', {query: {userId: {$in: otherUsers}}})
            ]);

            const prepareActions = (permissions, limits) => permissions.map(permission => ({
              action: 'permissions.update',
              params: {
                id: permission._id,
                accessLimit: {...permission.accessLimit, ...limits}
              }
            }));

            const studentActions = prepareActions(studentPermissions, {
              speakingUsed: 0,
              writingUsed: 0
            });

            const otherUserActions = prepareActions(otherUsersPermissions, {
              textUsed: 0,
              mediaUsed: 0,
              textAddOnLimit: 0,
              mediaAddOnLimit: 0
            });

            // Execute actions in parallel
            await Promise.all([
              this.broker.mcall(studentActions),
              this.broker.mcall(otherUserActions)
            ]);

          } catch (error) {
            console.error('Error:', error.message);
          }
        }
      },
      {
        // Define your cron expression (runs every 5 minutes)
        name: "report-clear-storage",
        // cronTime: '*/1 * * * *',
        cronTime: '0 2 * * *',
        async onTick() {
          try {
            console.log('Job clear Storage');
            await this.broker.call('reports.clearStorage');
          } catch (error) {
            console.error('Error:', error.message);
          }
        },
      },
      {
        // Define your cron expression (runs every 1 minutes)
        name: "response-check-processing",
        cronTime: '*/1 * * * *',
        async onTick() {
          try {
            this.broker.emit('responseCheckProcessing');
          } catch (error) {
            console.error('Error:', error.message);
          }
        },
      },
      {
        // Define your cron expression (runs at 12h35m every day)
        name: "create-user",
        cronTime: '35 12 * * *',
        async onTick() {
          try {
            console.log('Job create user wait list');
            await this.broker.emit('jobCreateUser');
          } catch (error) {
            console.error('Error:', error.message);
          }
        },
      },
      {
        // Job gửi email campaigns tự động (chạy mỗi giờ)
        name: "email-campaigns-scheduler",
        cronTime: '0 * * * *',
        // cronTime: '*/1 * * * *',
        async onTick() {
          try {
            console.log('Job kiểm tra và gửi email campaigns');
            await this.broker.call('mktscheduler.processCampaigns');
          } catch (error) {
            console.error('Error trong email campaigns scheduler:', error.message);
          }
        },
      },
      {
        // Job kiểm tra và cập nhật transaction VNPay quá hạn (chạy mỗi giờ)
        name: "vnpay-transaction-timeout-checker",
        cronTime: '0 * * * *', // Chạy vào phút 0 của mỗi giờ
        async onTick() {
          try {
            console.log('Job kiểm tra transaction VNPay quá hạn');
            await this.broker.call('transactions.checkAndUpdateExpiredVnpayTransactions');
          } catch (error) {
            console.error('Error trong vnpay transaction timeout checker:', error.message);
          }
        },
      },
    ]
  },

  actions: {},
  methods: {},
  events: {
    settingUpdate: {
      async handler(ctx) {
        const data = ctx.params
        if (data.cronTime) {
          this.getJob('clear-storage').setTime(new CronTime(data.cronTime));
        }
      }
    }
  },
  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    try {
      const cronTime = await this.broker.call('settings.getCronTime', {}) || '0 1 * * *';
      this.getJob('clear-storage').setTime(new CronTime(cronTime));
    } catch (error) {
      console.error('Error:', error);
    }
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
