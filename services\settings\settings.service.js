const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./settings.model");
const BaseService = require("../../mixins/baseService.mixin");
const AuthRole = require("../../mixins/authRole.mixin");
const {SERVICE_NAME} = require("./settings");
const {USER_CODES} = require("../../constants/constant");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");

module.exports = {
  name: SERVICE_NAME,
  mixins: [DbMongoose(Model), BaseService, AuthRole, FunctionsCommon],
  settings: {
    populates: {},
    populateOptions: [],
  },
  actions: {
    getOne: {
      rest: {
        path: "/getOne",
        method: "GET",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const settings = await this.adapter.findOne({});
        const {apiKeyOpenAI, speechKey} = settings;

        if (apiKeyOpenAI) settings.apiKeyOpenAI = this.convertKeyToPrivate(settings, apiKeyOpenAI);
        if (speechKey) settings.speechKey = this.convertKeyToPrivate(settings, speechKey);

        return settings;
      },

    },
    findOne: {
      rest: {
        path: "/findOne",
        method: "GET",
      },
      async handler(ctx) {
        return await this.adapter.findOne({});
      },

    },
    updateSetting: {
      rest: {
        path: "/manager",
        method: "PUT",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const data = ctx.params;
        const setting = await this.adapter.findOne({});

        this.broker.emit("settingUpdate", data);
        return await this.adapter.updateById(setting._id, data);
      },
    },
    getToolStudent: {
      rest: "GET /getToolStudent",
      async handler(ctx) {
        const settings = await this.adapter.findOne({});
        const [tools, listTag, listCategory] = await Promise.all([
          ctx.call("tools.find", {query: {_id: {$in: settings.studentTools}}}),
          ctx.call("tags.find"),
          ctx.call("categories.find"),
        ]);
        const groupTag = this.groupBy(listTag, "toolId");
        const groupCategory = this.groupBy(listCategory, "toolId");
        const toolsWithDetails = tools
          .sort((toolA, toolB) => toolA.createdAt - toolB.createdAt)
          .map(tool => ({
            ...tool,
            listTag: groupTag[tool._id.toString()] || [],
            listCategory: groupCategory[tool._id.toString()] || [],
          }));
        return {
          writing: toolsWithDetails.filter(tool => tool.categories.includes("Writing")),
          speaking: toolsWithDetails.find(tool => tool.categories.includes("Speaking")),
        };
      },
    },
    getCronTime: {
      async handler(ctx) {
        const settings = await this.adapter.findOne({});
        return settings?.cronTime;
      },
    },
    getVideoTutorial: {
      rest: "GET /getVideoTutorial",
      async handler(ctx) {
        const settings = await this.adapter.findOne({});
        return {tutorialUrl: settings?.tutorialUrl};
      },
    },
    getChatbot: {
      rest: "GET /getChatbot",
      async handler(ctx) {
        const settings = await this.adapter.findOne({});
        return {
          showChatbot: settings?.showChatbot,
          chatbotId: settings?.chatbotId
        };
      },
    },
  },
  methods: {
    convertKeyToPrivate(settings, key) {
      const prefixLength = Math.floor(key.length / 3);
      const prefix = key.slice(0, prefixLength);
      const suffix = key.slice(-prefixLength);
      const middle = key.slice(prefixLength, -prefixLength);
      return `${prefix}${middle.replace(/./g, '*')}${suffix}`;
    },


    async seedDB() {
      this.logger.info("Seed setting...");
      const settingObj = {
        apiKeyOpenAI: "***************************************************",
      };
      await this.adapter.insert(settingObj);
      this.logger.info(`Generated system setting!`);
    },
  },

  events: {},

  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },
};
