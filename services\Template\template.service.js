"use strict";

const Model = require("./template.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {IMAGE_SERVICE} = require("../Image");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

/**
 *
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "templates",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {
      "projectId": 'projects.get',
      "organizationId": 'organizations.get',
      "imageId": 'images.get',
    },
    populateOptions: ["projectId", "organizationId.avatarId", "imageId"],
  },
  hooks: {
    before: {
      "*": "checkPermissionActions",
      "create|update|updateNewContent|remove": "checkTemplateExist",
    }
  },
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    },
    upload: {
      // auth: "required",
      async handler(ctx) {
        try {
          const {templateId} = ctx.meta.$multipart;
          ctx.meta.bypassCapacityAddition = true;
          ctx.meta.isTemplate = true;
          const image = await ctx.call(IMAGE_SERVICE.upload, ctx.params, {meta: ctx.meta});
          console.log("image", image);
          const templateUpdated = await this.adapter.updateById(templateId, {imageId: image._id});
          return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, templateUpdated);
        } catch (e) {
          console.log(e);
        }
      }
    },
    create: {
      rest: {
        method: "POST",
        path: "/",
      },
      auth: "required",
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        const {role, isSystemAdmin} = ctx.meta.user;
        const entity = ctx.params;
        entity.projectDetail = await this.getProjectDetail(ctx, entity.projectId);
        const typeTemplate = isSystemAdmin || role === "contributor" ? "SYSTEM" : role === "admin" ? "ORGANIZATION" : "PERSONAL";
        entity.type = entity.type || typeTemplate;
        if (entity.projectDetail.commonOptions && entity.projectDetail.commonOptions.grade) {
          entity.projectDetail.commonOptions.grade = entity.folderCode;
        }

        if(entity.type === "ORGANIZATION") {
          const organization = await ctx.call('organizations.getOne', {id: entity.organizationId});
          if(organization?.active === false) {
            throw new MoleculerClientError(i18next.t("organization_already_unlocked"), 423);
          }
        }

        return await this.adapter.insert(entity);
      }
    },
    getAvailableTemplates: {
      rest: {
        method: "GET",
        path: "/available",
      },
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const {_id: userId, organizationId} = user || {};

        // const [myTemplates, systemTemplates] = await Promise.all([
        //   this.adapter.find({ query: { userId, folderId: { $exists: false }, isDeleted: false } }),
        //   this.adapter.find({ query: { type: "SYSTEM", folderCode: { $exists: false }, isDeleted: false } })
        // ]);
        const [myTemplates, systemTemplates] = await Promise.all([
          this.adapter.find({query: {userId, isDeleted: false}}),
          this.adapter.find({query: {type: "SYSTEM", isDeleted: false}})
        ]);

        let templates = [...myTemplates, ...systemTemplates];

        if (organizationId) {
          templates = [
            ...templates,
            ...(await this.adapter.find({
              query: {
                organizationId,
                // folderCode: { $exists: false },
                isDeleted: false
              }
            }))
          ];
        }

        const templatesTransformed = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, templates);
        return templatesTransformed.sort((a, b) => b.createdAt - a.createdAt);
      }
    },
    update: {
      rest: {
        method: "PUT",
        path: "/:id",
      },
      auth: "required",
      checkPermissionActions: true,
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        return ctx;
      }
    },
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      auth: "required",
      checkPermissionActions: true,
      role: USER_CODES.NORMAL,
      async handler(ctx) {
        const {id} = ctx.params;
        return await this.adapter.updateById(id, {isDeleted: true});
      }
    },
    updateNewContent: {
      rest: {
        method: "PUT",
        path: "/:id/newContent",
      },
      auth: "required",
      async handler(ctx) {

        const {id} = ctx.params;
        const template = await this.adapter.findOne({_id: id, isDeleted: false});
        if (!template) {
          throw new MoleculerClientError(i18next.t("template_was_deleted"), 404, "TEMPLATE_NOT_FOUND");
        }
        if(template.type === "ORGANIZATION") {
          const organization = await ctx.call('organizations.getOne', {id: template.organizationId});
          if(organization?.active === false) {
            throw new MoleculerClientError(i18next.t("organization_already_unlocked"), 423);
          }
        }

        const projectDetail = await this.getProjectDetail(ctx, template.projectId?.toString());

        return await this.adapter.updateById(id, {projectDetail}, {new: true});

      }
    },

    welcome: {
      rest: {
        method: "GET",
        path: "/welcome",
      },
      auth: "required",
      async handler(ctx) {
        const {user} = ctx.meta;
        const persona = await ctx.call("persona.find", {
          query: {code: {$in: user.persona}},
          fields: ["templateId"]
        });
        const templates = [...new Set(persona.map(i => i.templateId).flat())];
        return templates.slice(0, 4);
      }
    },

    getExamTemplate: {
      rest: {
        method: "GET",
        path: "/getExamTemplate",
      },
      auth: "required",
      async handler(ctx) {
        const {folderId} = ctx.params;
        console.log("folderId", folderId);
        const {organizationId} = ctx.meta.user;

        const [myTemplates, folder] = await Promise.all([
          this.adapter.find({query: {folderId: folderId.toString(), isDeleted: false}}),
          ctx.call("folders.get", {id: folderId.toString()})
        ]);

        const clickeeTemplatesPromise = this.adapter.find({
          query: {
            folderCode: folder.code,
            type: "SYSTEM",
            isDeleted: false
          }
        });

        let orgTemplatesPromise = Promise.resolve([]);

        if (organizationId) {
          orgTemplatesPromise = this.adapter.find({
            query: {
              organizationId,
              folderCode: folder.code,
              isDeleted: false
            }
          });
        }

        const [clickeeTemplates, orgTemplates] = await Promise.all([clickeeTemplatesPromise, orgTemplatesPromise]);
        const allTemplates = [...myTemplates, ...clickeeTemplates, ...orgTemplates];
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, allTemplates);
      }
    },
    getAvailableExamTemplate: {
      rest: {
        method: "GET",
        path: "/getAvailableExamTemplate",
      },
      auth: "required",
      async handler(ctx) {
        const {_id: userId, organizationId} = ctx.meta.user;
        const myExamFolder = await ctx.call("folders.find", {
          query: {
            type: {$in: ["EXAM_SCHOOL", "EXAM_IELTS"]},
            ownerId: userId,
          }
        });
        const myTemplates = await this.adapter.find({
          query: {
            folderId: {$in: myExamFolder.map(i => i._id)},
            isDeleted: false
          }
        });

        const clickeeTemplatesPromise = this.adapter.find({
          query: {
            folderCode: {$exists: true},
            type: "SYSTEM",
            isDeleted: false
          }
        });

        let orgTemplatesPromise = Promise.resolve([]);

        if (organizationId) {
          orgTemplatesPromise = this.adapter.find({
            query: {
              organizationId,
              folderCode: {$exists: true},
              isDeleted: false
            }
          });
        }

        const [clickeeTemplates, orgTemplates] = await Promise.all([clickeeTemplatesPromise, orgTemplatesPromise]);
        const allTemplates = [...myTemplates, ...clickeeTemplates, ...orgTemplates];
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, allTemplates);
      }
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {

    async getProjectDetail(ctx, projectId) {

      const projectDetail = await ctx.call("projects.get", {
        id: projectId,
        fields: ["projectName", "activeExam", "type", "commonOptions"],
      });
      const contents = await ctx.call("contents.find", {
        query: {projectId, isDeleted: false},
        sort: 'contentIndex',
        fields: ["isHidden", "toolId", "contentIndex", "title", "lastInput", "_id"],
        populate: [],
      });
      const queryResponse = ["EXAM_SCHOOL", "EXAM_IELTS"].includes(projectDetail.type) ? {examOrder: projectDetail.activeExam} : {isActivate: true};
      const responses = await ctx.call('responses.find', {
        query: {
          contentId: this.extractIdFromList(contents),
          isDeleted: false,
          ...queryResponse
        },
        fields: [
          "contentId", "headline", "outputType", "state", "isActivate", "plaintext", "output",
          "inputId.inputType", "inputId.inputData", "inputId.toolId", "examOrder", "toolId"
        ],
        populate: ['inputId']
      });
      responses.forEach(response => {
        response.examOrder = 1;
      });
      const groupResponses = this.groupBy(responses, "contentId");
      contents.forEach(content => {
        content.inputs = this.deleteFields([groupResponses[content._id]?.[0]?.inputId]);
        content.responses = this.deleteFields(groupResponses[content._id], ["_id", "contentId", "inputId"]);
      });
      const cleanedContents = this.deleteFields(contents, ["_id"]);
      return {...projectDetail, activeExam: 1, numberOfExams: 1, contents: cleanedContents};
    },

    deleteFields(listData, fields = []) {
      if (fields.length === 0) {
        return listData;
      }
      return listData?.map(item => {
        const newItem = {...item};
        fields.forEach(field => delete newItem[field]);
        return newItem;
      });
    },

    async checkPermissionActions(context) {
      const {action, params, meta} = context;
      const {id} = params;
      if (action?.checkPermissionActions) {
        const {user} = meta;
        const {_id: userId, role, isSystemAdmin, organizationId} = user;
        if (isSystemAdmin || role === "contributor") {
          return;
        }
        const template = await this.adapter.findById(id);

        if(template?.organizationId) {
          const organization = await context.call('organizations.getOne', {id: template.organizationId});
          if(organization?.active === false) {
            throw new MoleculerClientError(i18next.t("organization_already_unlocked"), 423);
          }
        }
        if (["EXAM_SCHOOL", "EXAM_IELTS"].includes(template.type)) {
          return;
        }

        const isPerTemplate = template.type === "PERSONAL" && template.userId?.toString() === userId?.toString();
        const isOrgTemplate = template.type === "ORGANIZATION" && template.organizationId?.toString() === organizationId?.toString();

        if (role === "normal" && !isPerTemplate) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_template"), 403, "FORBIDDEN");
        }
        if (role === "admin" && !(isPerTemplate || isOrgTemplate)) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_template"), 403, "FORBIDDEN");
        }
      }
    },
    async checkTemplateExist(context) {
      const {params, action} = context;
      let {id, projectId} = params;
      const template = id ? await this.adapter.findById({_id: id}) : {};
      if (template.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("template_was_deleted"), 404);
      }
      if (action.rawName === "updateNewContent") {
        projectId = template.projectId;
      }

      const project = projectId ? await context.call('projects.get', {id: projectId.toString()}) : {};
      if (project.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
      }
    }
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
