const sdk = require("microsoft-cognitiveservices-speech-sdk");
const path = require("path");
const fs = require("fs");
const wav = require("wav");
const {pronunciationAssessment} = require("./pronunciationAssessment");
const FileMixin = require("../../mixins/file.mixin");
const FunctionCommonMixin = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {getConfig} = require("../../config/config");
const {INPUT_TYPE} = require("../../constants/constant");
const config = getConfig(process.env.NODE_ENV);
const storageDir = path.join(__dirname, "storage");
const {MoleculerClientError} = require("moleculer").Errors;
const ffmpeg = require("fluent-ffmpeg");
const {getAudioDurationInSeconds} = require("get-audio-duration");
const globalState = new Map();
module.exports = {
  name: "speaking",
  mixins: [FileMixin, FunctionCommonMixin],

  actions: {
    getAudioUrl: {
      rest: 'GET /link/:fileName',
      async handler(ctx) {
        try {
          const {fileName} = ctx.params;
          let filePath = this.getFilePath(fileName, storageDir);
          if (!fs.existsSync(filePath)) {
            filePath = await this.broker.call("files.pathFromFileName", {fileName});
          }
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": "audio/wav",
            "Content-Length": stat.size,
            "Content-Disposition": `attachment;filename=${fileName}`
          };
          return fs.createReadStream(filePath, {});
        } catch (e) {
          throw new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },

  },

  methods: {
    async saveAudioFile(state, socket) {
      try {
        const {audioChunks, userId, inputData} = state;
        console.log("inputData", inputData);
        const {sessionId, questionIndex, part} = inputData;
        // Define file names
        const wavFileName = `record_audio_${sessionId}_${part}_${questionIndex}.wav`;
        const mp3FileName = `record_audio_${sessionId}_${part}_${questionIndex}.mp3`;

        const wavFilePath = this.getFilePath(wavFileName, storageDir);
        const mp3FilePath = this.getFilePath(mp3FileName, storageDir);

        // Create WAV file
        const fileStream = fs.createWriteStream(wavFilePath);
        const wavWriter = new wav.Writer({
          sampleRate: 16000,
          channels: 1,
          bitDepth: 16,
        });

        wavWriter.pipe(fileStream);
        audioChunks.forEach((chunk) => wavWriter.write(chunk));
        wavWriter.end();

        await new Promise((resolve, reject) => {
          fileStream.on("finish", resolve);
          fileStream.on("error", reject);
        });

        console.log(`WAV file saved: ${wavFilePath}`);

        // Convert WAV to MP3
        await new Promise((resolve, reject) => {
          ffmpeg(wavFilePath)
            .toFormat("mp3")
            .audioCodec("libmp3lame")
            .audioBitrate(128)
            .on("end", () => {
              console.log(`MP3 file saved: ${mp3FilePath}`);
              // Chỉ xóa file WAV khi chuyển đổi thành công
              try {
                fs.unlinkSync(wavFilePath);
              } catch (error) {
                console.error("Error deleting WAV file:", error);
                // Tiếp tục xử lý, không dừng ở đây nếu lỗi xóa file
              }
              resolve();
            })
            .on("error", (err) => reject(err))
            .save(mp3FilePath);
        });
        const [duration, file] = await Promise.all([
          getAudioDurationInSeconds(mp3FilePath),
          this.broker.call("resources.createFromRecordAudio", {
            audioChunks,
            userId,
            fileName: mp3FileName, // Save MP3 file info in DB
          }),
        ])
        console.log('duration', duration);
        // Send MP3 file URL via socket
        const audioUrl = `${config.domain}/api/speaking/link/${mp3FileName}`;

        if (globalState[socket.id]) {
          globalState[socket.id].fileId = file._id;
          globalState[socket.id].audioDuration = duration;
        }

        socket.send({state: "audio_file_saved", audioUrl, fileId: file._id});
      } catch (e) {
        console.error("Error in saveAudioFile:", e);
        socket.send({state: "error", message: "An error occurred while saving the audio file."});
      }
    },

    async handleFeedback(inputData, socket, connectionId) {
      try {
        await this.saveSessionData(inputData, connectionId);
        socket.emit("finish-recognition");
      } catch (error) {
        console.error("Error handling feedback:", error);
        socket.send({state: "error", message: "An error occurred while processing feedback."});
      }
    },
    async getContentScore(data) {
      try {
        console.log("data", data)
        const {topic, recognizedText} = data
        const explainInstructions = await this.broker.call("explain.getExplainByType", {
          explainType: "answers_comment",
          taskType: "speaking_room"
        })
        if (!topic || !recognizedText) {
          this.logger.error("Missing topic or recognizedText in getContentScore");
          return {
            vocabularyScore: 0,
            grammarScore: 0,
            topicScore: 0,
            comments: "Missing topic or recognizedText"
          };
        }

        const {instruction, temperature, maxTokens, responseFormat, schemaInstruction} = explainInstructions

        const prompt = ` **Input:**

          Topic: ${topic}
          Voice transcript: ${recognizedText}`

        const messages = [
          {
            "role": "system",
            "content": `${instruction}`
          },
          {
            "role": "user",
            "content": prompt
          }
        ]
        try {
          return await this.broker.call("tools.submitFastLLM", {
            messages,
            temperature: temperature,
            max_tokens: maxTokens,
            responseFormat,
            schema: schemaInstruction,
            responseId: data.sessionId
          });
        } catch (gptError) {
          this.logger.error("GPT API error:", gptError);
          return {
            vocabularyScore: 0,
            grammarScore: 0,
            topicScore: 0,
            comments: "An error occurred while processing the transcript."
          };
        }

      } catch (error) {
        this.logger.error("Error in getContentScore:", error);
        return {
          vocabularyScore: 0,
          grammarScore: 0,
          topicScore: 0
        };
      }
    },

    async saveSessionData(inputData, connectionId) {
      try {
        const words = inputData.results
          .flatMap(item => item.NBest.flatMap(nBestItem => nBestItem.Words))
          .filter(item => item?.Word);
        const wordsAudio = []
        const wordsData = []
        words.forEach(item => {
          wordsAudio.push(
            this.broker.call("files.wordAudio", {word: item.Word})
          )
          wordsData.push(
            this.broker.call("ipa.getWordData", {word: item.Word})
          )
        })
        await Promise.allSettled(wordsAudio);
        const results = await Promise.allSettled(wordsData);

        const mapWords = results.reduce((map, item) => {
          if (item) {
            map[item.word] = item;
          }
          return map;
        }, {});

        inputData.results.forEach(item => {
          item.NBest.forEach(nBestItem => {
            nBestItem.Words?.forEach(word => {
              const wordData = mapWords[word.Word] || {};
              word.ipa = wordData.ipa;
              word.audioUrl = wordData.audioUrl;
            });
          });
        });

        const {part, questionId, sessionId, questionIndex} = inputData
        const audioUrl = `${config.domain}/api/speaking/link/record_audio_${sessionId}_${part}_${questionIndex}.mp3`
        // let question = await this.broker.call("spkquestions.get", {id: questionId});

        const {vocabularyScore, grammarScore, pronScore, fluencyScore} = inputData
        console.log('inputData', inputData)
        const feedback = this.convertAzureScoreToIELTS(vocabularyScore, grammarScore, pronScore, fluencyScore);
        const newAnswer = {
          questionId,
          sessionId,
          duration: globalState[connectionId]?.audioDuration || 0,
          userId: globalState[connectionId]?.userId,
          transcript: inputData?.recognizedText,
          results: inputData?.results,
          feedback: {
            ...feedback,
            comments: inputData?.comments
          },
          audioUrl,
          audioFileId: globalState[connectionId]?.fileId,
          part,
        };

        let answer;
        try {
          answer = await this.broker.call("spkanswers.createByQuestionId", {...newAnswer});
        } catch (responseError) {
          this.logger.error("Error inserting answer:", responseError);
          throw new Error("Failed to save answer data");
        }

        // Xóa dữ liệu từ globalState sau khi đã xử lý xong
        delete globalState[connectionId];

      } catch (error) {
        this.logger.error("Error in saveSessionData:", error);
        throw error; // Re-throw để hàm gọi có thể xử lý
      }
    },

  },

  async started() {

  },

  events: {
    speakingConnected: {
      async handler(socket) {
        const connectionId = socket.id;
        // Khởi tạo nếu chưa tồn tại
        if (!this.connectionState) {
          this.connectionState = {};
        }

        this.logger.info(`Client connected: ${connectionId}`);
        const {speechKey, serviceRegion, confidenceThreshold} = await this.broker.call("settings.findOne");
        const format = sdk.AudioStreamFormat.getWaveFormat(16000, 16, 1, sdk.AudioFormatTag.PCM);
        this.connectionState[connectionId] = {
          audioBufferQueue: [],
          audioChunks: [],
          audioStream: sdk.AudioInputStream.createPushStream(format),
          inputData: {},
          userId: null,
          startRecognition: false,
          isRecording: true,
          audioDuration: 0,
          processInterval: null,
          callbackFunction: (data) => this.handleFeedback(data, socket, connectionId),
          contentFunction: (data) => this.getContentScore(data),
        };

        const state = this.connectionState[connectionId];
        globalState[connectionId] = state;
        state.processInterval = setInterval(() => {
          if (state.audioBufferQueue.length > 0) {
            while (state.audioBufferQueue.length > 0) {
              const audioData = state.audioBufferQueue.shift();
              try {
                state.audioStream.write(audioData);
              } catch (err) {
                console.error("Error writing audio data to stream:", err);
              }
            }

            if (!state.startRecognition) {
              pronunciationAssessment({
                socket, speechKey, serviceRegion, confidenceThreshold,
                audioStream: state.audioStream,
                inputData: state.inputData,
                callback: state.callbackFunction,
                contentFunction: state.contentFunction,

              });
              state.startRecognition = true;
            }
          } else if (!state.isRecording) {
            clearInterval(state.processInterval);
            state.audioStream.close();
            console.log(`Stopped processing for client: ${connectionId}`);
          }
        }, 500);

        socket.on("audio", (data) => {
          state.audioBufferQueue.push(data.buffer);
          state.audioChunks.push(data.buffer);
          state.inputData = data.inputData;
          state.userId = data.userId;
        });

        socket.on("close-recording", async () => {
          console.log(`Closing recording for client: ${connectionId}`);
          state.isRecording = false;
          await this.saveAudioFile(state, socket);
        });

        socket.on("disconnect", () => {
          this.logger.info(`Client disconnected: ${connectionId}`);
          clearInterval(state.processInterval);
          state.audioStream.close();
          delete this.connectionState[connectionId];
          delete globalState[connectionId];
        });

        // Gửi sự kiện server_ready để thông báo cho client rằng server đã sẵn sàng nhận dữ liệu
        socket.emit("server_ready");
      }
    }
  },

};
