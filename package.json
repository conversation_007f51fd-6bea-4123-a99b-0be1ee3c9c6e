{"name": "tradar-g-be-microservices", "version": "1.0.0", "description": "My Moleculer-based microservices project", "scripts": {"dev": "moleculer-runner --hot services", "start": "moleculer-runner --instances 2 services", "cli": "moleculer connect NATS", "ci": "jest --watch", "test": "jest --coverage", "lint": "eslint services", "dc:up": "docker-compose up --build -d", "dc:logs": "docker-compose logs -f", "dc:down": "docker-compose down"}, "keywords": ["microservices", "moleculer"], "author": "", "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@eslint/js": "^9.17.0", "dotenv": "^16.4.5", "eslint": "^9.17.0", "globals": "^15.14.0", "jest": "^27.5.1", "jest-cli": "^27.5.1", "moleculer-repl": "^0.7.4", "prettier": "^3.0.3"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.12.0", "@anthropic-ai/sdk": "^0.33.1", "@azure/identity": "^4.6.0", "@distube/ytdl-core": "4.14.4", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/local-auth": "^3.0.1", "@google/genai": "^1.0.1", "@googleapis/forms": "^2.0.5", "@gradio/client": "git+https://github.com/hungphandinh92it/gradio_js_client.git", "@langchain/community": "^0.2.22", "@langchain/core": "^0.2.16", "@langchain/google-genai": "^0.0.22", "@langchain/openai": "^0.2.5", "@lucasretamoso/docx-merger": "^0.0.18", "@turbodocx/html-to-docx": "^1.9.4", "@xmldom/xmldom": "^0.8.10", "activedirectory": "^0.7.2", "adm-zip": "^0.5.14", "ajv": "^8.12.0", "any-text": "^1.2.0", "axios": "^1.6.7", "base-64": "^1.0.0", "bcryptjs": "^2.4.3", "carbone": "3.2.3", "cookie": "^0.5.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "difflib": "^0.2.4", "docx": "^9.0.2", "docx-merger": "^1.2.2", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "form-data": "2.3.3", "get-audio-duration": "^4.0.1", "gm": "^1.23.1", "google-auth-library": "^9.6.3", "googleapis": "^133.0.0", "gpt-tokenizer": "^2.6.2", "groq-sdk": "^0.5.0", "html-pdf": "^3.0.1", "html-to-docx": "^1.8.0", "html-to-md": "^0.8.5", "html-to-text": "^9.0.5", "i18n": "^0.15.1", "i18next": "^22.4.11", "i18next-fs-backend": "^2.1.1", "i18next-http-backend": "^2.1.1", "i18next-http-middleware": "^3.3.0", "i18next-node-fs-backend": "^2.1.3", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "langchain": "^0.2.9", "loconvert": "^1.0.2", "lodash": "4.17.21", "mammoth": "^1.8.0", "marked": "^13.0.1", "microsoft-cognitiveservices-speech-sdk": "^1.41.0", "mime-types": "^3.0.1", "ml-distance": "^4.0.1", "moleculer": "^0.14.26", "moleculer-cron": "^0.0.7", "moleculer-db": "^0.8.20", "moleculer-db-adapter-mongo": "^0.4.15", "moleculer-db-adapter-mongoose": "^0.8.13", "moleculer-web": "^0.10.4", "moment": "^2.29.4", "mongoose": "^5.11.15", "mongoose-paginate-v2": "^1.7.1", "nats": "^2.7.1", "node-cron": "^3.0.3", "node-fetch": "2", "node-poppler": "^7.2.0", "node-telegram-bot-api": "^0.66.0", "node-tesseract-ocr": "^2.2.1", "nodemailer": "^6.4.17", "openai": "^4.58.2", "pdf-parse": "^1.1.1", "pdf.js-extract": "^0.2.1", "puppeteer": "^24.10.1", "qrcode": "^1.5.3", "qs": "^6.11.2", "readline": "^1.3.0", "readline-sync": "^1.4.10", "regex-username": "^2.0.0", "sharp": "^0.33.5", "sherpa-onnx-node": "^1.12.0", "showdown": "^2.1.0", "socket.io": "^4.8.1", "superagent": "^8.1.2", "thumbsupply": "^0.4.0", "video-thumb": "^0.0.3", "wav": "^1.0.2", "xml2js": "^0.6.2", "xxhash-wasm": "^1.1.0", "youtube-transcript": "^1.0.6", "youtubei.js": "^10.3.0", "zod": "^3.23.8"}, "engines": {"node": ">= 16.x.x"}, "jest": {"coverageDirectory": "../coverage", "testEnvironment": "node", "rootDir": "./services", "roots": ["../test"]}}