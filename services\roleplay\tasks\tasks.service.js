"use strict";

const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const Model = require("./tasks.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const i18next = require("i18next");
const { MoleculerClientError } = require("moleculer").Errors;
const z = require("zod");

module.exports = {
  name: "tasks",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, AuthRole],

  settings: {
    entityValidator: {
      name: { type: "string", min: 2, max: 255 },
      description: { type: "string", max: 5000 },
      courseId: { type: "string" },
      evaluationGuidelines: { type: "string", optional: true, max: 5000 },
      weight: { type: "number", optional: true, min: 0 },
      exampleVideoUrl: { type: "string", optional: true },
      helpfulLinks: { type: "array", optional: true, items: "string" },
      isMakeOrBreak: { type: "boolean", optional: true },
      orderInCourse: { type: "number", optional: true, min: 0 }
    },
    populates: {
      "courseId": "courses.get",
      "organizationId": "organizations.get",
      "createdBy": "users.get",
      "updatedBy": "users.get",
    },
    populateOptions: ["courseId", "organizationId", "createdBy", "updatedBy"],
    fields: [
      "_id",
      "name",
      "description",
      "courseId",
      "evaluationGuidelines",
      "weight",
      "exampleVideoUrl",
      "helpfulLinks",
      "isMakeOrBreak",
      "orderInCourse",
      "organizationId",
      "createdBy",
      "updatedBy",
      "createdAt",
      "updatedAt",
      "isDeleted"
    ],
    defaultSort: "orderInCourse"
  },

  hooks: {
    after: {
      create: async (ctx, task) => {
        console.log("#####################task", task);
        ctx.emit("tasks.created", { task });
        return task;
      },
      remove: async (ctx, task) => {
        ctx.emit("tasks.deleted", { task });
        return task;
      }
    }
  },

  actions: {
    // Tạo nhiệm vụ mới
    create: {
      rest: "POST /",
      params: {
        name: { type: "string", min: 2, max: 255 },
        description: { type: "string", max: 5000 },
        courseId: { type: "string" },
        evaluationGuidelines: { type: "string", optional: true, max: 5000 },
        weight: { type: "number", optional: true, min: 0 },
        exampleVideoUrl: { type: "string", optional: true },
        helpfulLinks: { type: "array", optional: true, items: "string" },
        isMakeOrBreak: { type: "boolean", optional: true, default: false },
        orderInCourse: { type: "number", optional: true }
      },
      async handler(ctx) {
        const {
          name, description, courseId, evaluationGuidelines,
          weight, exampleVideoUrl, helpfulLinks, isMakeOrBreak,
          orderInCourse, organizationId
        } = ctx.params;
        const user = ctx.meta.user;

        // Tìm orderInCourse cao nhất hiện tại nếu không được cung cấp
        let taskOrder = orderInCourse;
        if (taskOrder === undefined) {
          const lastTask = await this.adapter.findOne({
            courseId,
            isDeleted: { $ne: true }
          }, { sort: { orderInCourse: -1 } });

          taskOrder = lastTask ? lastTask.orderInCourse + 1 : 0;
        }

        const taskData = {
          name,
          description,
          courseId,
          evaluationGuidelines: evaluationGuidelines || "",
          weight: weight || 0,
          exampleVideoUrl: exampleVideoUrl || "",
          helpfulLinks: helpfulLinks || [],
          isMakeOrBreak: isMakeOrBreak || false,
          orderInCourse: taskOrder,
          organizationId, // Đã được gán từ hook before
          createdBy: user._id,
          updatedBy: user._id,
        };

        const task = await this.adapter.insert(taskData);
        return this.transformDocuments(ctx, {}, task);
      },
    },
    // Xóa nhiệm vụ (xóa mềm)
    remove: {
      rest: "DELETE /:id",
      params: {
        id: { type: "string" }
      },
      async handler(ctx) {
        const { id } = ctx.params;
        const user = ctx.meta.user;
        const task = ctx.locals.task; // Task đã được lấy từ hook before

        const updated = await this.adapter.updateById(id, {
          $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedBy: user._id,
          }
        });

        return this.transformDocuments(ctx, {}, updated);
      },
    },

    // Tạo tasks từ prompt sử dụng AI
    createTasksFromPrompt: {
      rest: "POST /createFromPrompt",
      params: {
        courseId: { type: "string"}, // ID của khóa học (tùy chọn)
        prompt: { type: "string"}, // Prompt để gửi cho AI
        aiPersonaId: { type: "string", optional: true }, // AI Persona ID để AI hiểu rõ hơn ngữ cảnh
      },
      async handler(ctx) {
        const { courseId, prompt, aiPersonaId } = ctx.params;
        const user = ctx.meta.user;

        let course = null;
        let courseReferences = [];

        // Nếu có courseId, lấy thông tin khóa học và references
        if (courseId) {
          try {
            course = await ctx.call("courses.get", { id: courseId, populate: ["references"] });
            if (!course) {
              throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
            }
            // Lấy references từ course nếu có
            if (course.references && Array.isArray(course.references)) {
              courseReferences = course.references;
            }

            // Kiểm tra quyền (ví dụ: chỉ admin hoặc người tạo course)
            // Add actual permission check here, e.g., based on course.createdBy or organizationId
            // For now, let's assume if the user can get the course, they can update it via AI.
          } catch (error) {
            this.logger.error("Failed to get course:", error);
            throw new MoleculerClientError(i18next.t("error.course_not_found", "Không tìm thấy khóa học"), 404);
          }
        }

        // Xử lý helpfulLinks và exampleVideoUrl từ course.references nếu có
        let extractedHelpfulLinks = [];
        let extractedExampleVideoUrl = "";

        if (courseReferences.length > 0) {
          // Lọc ra các reference có type là 'url' hoặc 'youtube' cho helpfulLinks
          extractedHelpfulLinks = courseReferences
            .filter(ref => ref.type === 'url' || ref.type === 'youtube')
            .map(ref => ref.url)
            .filter(url => url); // Lọc bỏ các giá trị null/undefined

          // Tìm reference đầu tiên có type là 'youtube' hoặc 'video' cho exampleVideoUrl
          const videoRef = courseReferences.find(ref => ref.type === 'youtube' || ref.type === 'video');
          if (videoRef && videoRef.url) {
            extractedExampleVideoUrl = videoRef.url;
          }
        }

        // Sử dụng giá trị từ tham số đầu vào nếu có, nếu không thì sử dụng giá trị trích xuất từ references
        const finalHelpfulLinks = extractedHelpfulLinks;
        const finalExampleVideoUrl = extractedExampleVideoUrl;

        const systemPrompt = `
Bạn là một hệ thống tạo danh sách nhiệm vụ (tasks) cho một cuộc trò chuyện với AI Persona.
Đọc thông tin khóa học và AI Persona (nếu có) được cung cấp trong prompt sau đây.
Bạn hãy đưa danh sách các nhiệm vụ trò chuyện cho người dùng với AI Persona phù hợp với tài liệu liên quan của khoá học.

Mỗi nhiệm vụ cần có các thông tin sau:
- name: string (Tên nhiệm vụ, ngắn gọn, ví dụ: "Chào hỏi và giới thiệu sản phẩm")
- description: string (Mô tả chi tiết nhiệm vụ, mục tiêu cần đạt được, ví dụ: "Học viên thực hành kỹ năng chào hỏi khách hàng và giới thiệu về sản phẩm X một cách tự nhiên và thu hút.")
- evaluationGuidelines: string (Hướng dẫn đánh giá sơ bộ, ví dụ: "Đánh giá dựa trên sự tự tin, thông tin sản phẩm chính xác, khả năng tạo thiện cảm.")
- weight: number (Trọng số của nhiệm vụ, từ 0-100, ví dụ: 20)
- isMakeOrBreak: boolean (Đánh dấu nhiệm vụ có tính chất "sống còn", ví dụ: true/false)
- helpfulLinks: array (Danh sách các URL tham khảo hữu ích, bỏ trống nếu không có)
- exampleVideoUrl: string (URL đến video mẫu minh họa cách thực hiện nhiệm vụ, bỏ trống nếu không có)

Trả về một mảng JSON chứa các object nhiệm vụ. Ví dụ:
[
  {
    "name": "Task 1 Name",
    "description": "Detailed description for Task 1.",
    "evaluationGuidelines": "Guideline for evaluating task 1.",
    "weight": 30,
    "isMakeOrBreak": true,
    "helpfulLinks": ["https://example.com/guide1"],
    "exampleVideoUrl": "https://example.com/video1.mp4"
  },
  {
    "name": "Task 2 Name",
    "description": "Detailed description for task 2.",
    "evaluationGuidelines": "Guideline for evaluating task 2.",
    "weight": 20,
    "isMakeOrBreak": false,
    "helpfulLinks": ["https://example.com/guide2", "https://example.com/guide3"],
    "exampleVideoUrl": "https://example.com/video2.mp4"
  }
]
Chỉ trả về mảng JSON hợp lệ, không giải thích thêm.
        `.trim();

        const messages = [
          { role: "system", content: systemPrompt },
          { role: "user", content: `Dưới đây là thông tin chi tiết để bạn tạo danh sách nhiệm vụ (tasks) cho cuộc trò chuyện:\n${prompt}
          ${course ? `\nCourse liên quan: Tên khoá học: ${course.name}, thể loại trò chuyện: ${course.simulationType}, mô tả ${course.description}` : ''}
          ${courseReferences.length > 0 ? `\nTài liệu tham khảo của khoá học:\n${courseReferences.map(ref => `- ${ref.name} - ${ref.url} : ${(ref.content ? ref.content: 'Không có nội dung')}`).join('\n')}` : ''}
          // ${aiPersonaId ? `\nAI Persona ID liên quan: ${aiPersonaId}` : ''}` }
        ];

        // Tạo Zod schema cho task
        const taskZodSchema = z.object(
          {
            tasks: z.array(
              z.object({
                name: z.string(),
                description: z.string(),
                evaluationGuidelines: z.string().optional(),
                weight: z.number().optional(),
                isMakeOrBreak: z.boolean().optional(),
                helpfulLinks: z.array(z.string()).optional(),
                exampleVideoUrl: z.string().optional()
              })
            )
          }
        );

        let aiResult;
        try {
          this.logger.info("Calling OpenAI for task generation...");
          aiResult = await ctx.call("roleplay.openai.sendToOpenAI", {
            messages,
            jsonSchema: taskZodSchema,
            schemaName: "tasks",
            maxTokens: 1000,
          });
          this.logger.info("OpenAI response received for task generation.");
        } catch (error) {
          this.logger.error("OpenAI task generation failed:", error);
          throw new MoleculerClientError("Không thể tạo tasks từ AI", 500, "AI_TASK_GENERATION_FAILED");
        }

        // Không cần phải parse JSON vì aiResult đã là một mảng JSON
        const suggestedTasks = aiResult.tasks;

        // Chuẩn bị danh sách tasks để trả về cho client
        const suggestedTasksData = [];
        let orderInCourse = 0;

        // Nếu có courseId, lấy orderInCourse tiếp theo
        if (courseId) {
          const lastTask = await this.adapter.findOne(
            { courseId, isDeleted: { $ne: true } },
            { sort: { orderInCourse: -1 } }
          );
          orderInCourse = lastTask ? lastTask.orderInCourse + 1 : 0;
        }

        for (const taskSuggestion of suggestedTasks) {
          if (!taskSuggestion.name || !taskSuggestion.description) {
            this.logger.warn("Skipping task suggestion due to missing name or description:", taskSuggestion);
            continue;
          }

          const taskData = {
            name: taskSuggestion.name,
            description: taskSuggestion.description,
            evaluationGuidelines: taskSuggestion.evaluationGuidelines || "",
            weight: taskSuggestion.weight || 0,
            isMakeOrBreak: taskSuggestion.isMakeOrBreak || false,
            helpfulLinks: taskSuggestion.helpfulLinks || finalHelpfulLinks || [],
            exampleVideoUrl: taskSuggestion.exampleVideoUrl || finalExampleVideoUrl || "",
            orderInCourse: orderInCourse++
          };

          // Chỉ thêm courseId nếu có
          if (courseId) {
            taskData.courseId = courseId;
          }

          suggestedTasksData.push(taskData);
        }

        return { tasks: suggestedTasksData, count: suggestedTasksData.length };
      }
    }
  },

  methods: {
    /**
     * Kiểm tra quyền của user đối với nhiệm vụ
     *
     * @param {Object} user - Thông tin user
     * @param {Object} task - Thông tin nhiệm vụ
     * @param {String} permission - Loại quyền ('read', 'write', 'delete')
     * @returns {Boolean} - Có quyền hay không
     */
    hasPermission(user, task, permission = 'read') {
      if (!user) return false;

      // System admin có mọi quyền
      if (user.isSystemAdmin) return true;

      // Organization admin có quyền với nhiệm vụ trong tổ chức của mình
      if (user.isOrgAdmin && task.organizationId &&
          user.organizationId && user.organizationId.toString() === task.organizationId.toString()) {
        return true;
      }

      // Người dùng thường chỉ có quyền đọc
      if (permission === 'read') {
        // Nhiệm vụ thuộc tổ chức của user
        if (task.organizationId && user.organizationId &&
            user.organizationId.toString() === task.organizationId.toString()) {
          return true;
        }
      }

      return false;
    }
  },

  async started() {
    this.logger.warn("#####################################################Tasks service started");
  },

  async stopped() {
    this.logger.warn("#####################################################Tasks service stopped");
  },
};
