'use strict';

const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const FileMixin = require('../../mixins/file.mixin');
const BaseService = require('../../mixins/baseService.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {Configuration, OpenAIApi} = require('openai');
const i18next = require('i18next');
const {OpenAI} = require('openai');
const {zodResponseFormat} = require('openai/helpers/zod');
const z = require('zod');

// C<PERSON>c thành phần prompt được tách biệt thành const
const SYSTEM_INSTRUCTION = `Bạn là một AI Agent nhập vai nhân vật(PERSONA) với tính cách, vai trò và giọng nói cụ thể
trong một cuộc gọi. Bạn phải hành động, giao tiếp và phản ứng chính xác như nhân vật được mô tả.
Bạn phải nhập vai persona đúng cả về tính cách, tâm trạng và phong cách giọng nói được mô tả.`;

const PERSONA_INSTRUCTION = `
# THÔNG TIN
- Tên: {{name}}
- Vai trò trong cuộc gọi : {{role}}
- Tổ chức của bạn: {{organization}}
- Tâm trạng hiện tại của bạn: {{mood}}
- Nền tảng: {{background}}
- Mối quan tâm: {{concern}}
- Small Talk Likely: {{smallTalkLikely}}
- Từ ngữ cần lọc/tránh: {{filterWords}}
- Phong cách giọng nói: {{voiceStyle}}
`;

const COURSE_INSTRUCTION = `
// # THÔNG TIN KHÓA HỌC CHỈ CÓ Ý NGHĨA NẾU PERSONA LÀ GIẢNG VIÊN HOẶC LÀ TƯ VẤN, NẾU PERSONA LÀ KHÁCH HÀNG SẼ KHÔNG BIẾT THÔNG TIN NÀY
// - Tên khóa học: {{courseName}}
// - Mô tả khóa học: {{courseDescription}}
- Tài liệu tham khảo: {{courseReferences}}
`;

const TASK_INSTRUCTION = `
# THÔNG TIN NHIỆM VỤ DÀNH CHO HỌC VIÊN, PERSONA KHÔNG BIẾT NHIỆM VỤ NÀY, AI CẦN KHÉO LÉO GIAO TIẾP  ĐỂ HỌC VIÊN ĐI QUA CÁC NHIỆM VỤ NÀY
- Mục tiêu nhiệm vụ: {{taskObjective}}
`;

const CONVERSATION_STYLE = `
# PHONG CÁCH GIAO TIẾP CỦA PERSONA
- Phong cách nói chuyện của bạn phải phù hợp với tâm trạng "{{mood}}".
- Bạn phải tuân theo phong cách giọng nói được mô tả: "{{voiceStyle}}".
- {{#if smallTalkLikely}}Bạn có xu hướng nói chuyện phiếm và thân thiện khi thích hợp.{{else}}Bạn tập trung vào vấn đề chính và ít nói chuyện phiếm.{{/if}}
- Hãy nhớ rằng đây là một cuộc trò chuyện qua điện thoại, vì vậy hãy phản ứng như trong một cuộc gọi thực tế.
- Tránh sử dụng các từ ngữ sau: {{filterWords}}
- Hãy phản ứng tự nhiên với những gì người học nói đúng tính cách, tâm trạng và phong cách giọng nói của Persona.
`;

const RESPONSE_FORMAT = `
# ĐỊNH DẠNG PHẢN HỒI
- Phản hồi của bạn nên ngắn gọn và tự nhiên như cuộc trò chuyện thực.
- Đừng mô tả hành động nào bằng dấu *asterisk* hoặc các thẻ đánh dấu.
- Chỉ trả lời như nhân vật, không bao gồm metadata hay nhãn như "AI:" hoặc "Persona:".
- Đáp lại một cách tự nhiên theo ngữ cảnh của cuộc trò chuyện và câu hỏi/phát biểu của người dùng.
`;

module.exports = {
  name: 'roleplay.openai',
  mixins: [FunctionsCommon, FileMixin, BaseService],

  hooks: {
    before: {
      '*': 'getAPIKey',
    },
  },

  settings: {
    // Các cài đặt mặc định
    defaultModel: 'gpt-4o-mini',
    maxTokens: 500,
    temperature: 0.7,
    // Có thể thêm các cài đặt khác nếu cần
  },

  actions: {
    /**
     * Tạo prompt cho AI Persona
     *
     * @param {Object} ctx - Context
     * @returns {String} - Prompt đã tạo
     */
    createPersonaPrompt: {
      params: {
        persona: 'object',
        course: 'object',
        conversation: {type: 'array', optional: true},
      },
      async handler(ctx) {
        const {persona, course, conversation = []} = ctx.params;

        try {
          // Tạo prompt hệ thống
          const systemPrompt = await this.buildSystemPrompt(persona, course);

          // Tạo danh sách messages theo định dạng của OpenAI
          const messages = [{role: 'system', content: systemPrompt}];

          // Thêm lịch sử cuộc trò chuyện
          if (conversation && conversation.length > 0) {
            conversation.forEach(message => {
              if (message.role && message.content) {
                messages.push({
                  role: message.role, // "user" hoặc "assistant"
                  content: message.content,
                });
              }
            });
          }

          return messages;
        } catch (error) {
          this.logger.error('Error creating persona prompt:', error);
          throw new MoleculerClientError('Không thể tạo prompt cho AI Persona', 500);
        }
      },
    },

    /**
     * Gửi prompt đến OpenAI và nhận phản hồi
     *
     * @param {Object} ctx - Context
     * @returns {String|Object} - Phản hồi từ OpenAI (string hoặc object JSON tùy thuộc vào jsonSchema)
     */
    sendToOpenAI: {
      params: {
        messages: 'array',
        model: {type: 'string', optional: true},
        temperature: {type: 'number', optional: true},
        maxTokens: {type: 'number', optional: true},
        jsonSchema: {type: 'object', optional: true},
        schemaName: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const {
          messages,
          model = this.settings.defaultModel,
          temperature = this.settings.temperature,
          maxTokens = this.settings.maxTokens,
          jsonSchema,
          schemaName = 'data',
        } = ctx.params;

        const {apiKey} = ctx.meta; // Lấy API key từ meta đã được hook nạp
        // console.log("#########Message", messages)

        if (!apiKey) {
          this.logger.error('OpenAI API key is missing.');
          throw new MoleculerClientError('OpenAI API key is missing.', 500, 'API_KEY_MISSING');
        }

        const openai = new OpenAI({apiKey});

        try {
          this.logger.info(`Calling OpenAI chat.completions.create with model: ${model}`);

          // Kiểm tra xem có yêu cầu phản hồi dạng JSON không
          if (jsonSchema) {
            // Gọi API OpenAI với định dạng phản hồi JSON
            const completion = await openai.beta.chat.completions.parse({
              model,
              messages,
              temperature,
              max_tokens: maxTokens,
              response_format: zodResponseFormat(jsonSchema, schemaName),
            });

            // Trả về dữ liệu đã được phân tích
            if (
              completion &&
              completion.choices &&
              completion.choices.length > 0 &&
              completion.choices[0].message &&
              completion.choices[0].message.parsed
            ) {
              return completion.choices[0].message.parsed;
            } else {
              throw new Error('Không nhận được phản hồi JSON hợp lệ từ OpenAI');
            }
          } else {
            // Gọi API OpenAI trực tiếp theo cách thông thường
            const response = await openai.chat.completions.create({
              model,
              messages,
              temperature,
              max_tokens: maxTokens,
            });

            // Trích xuất và trả về nội dung phản hồi
            if (
              response &&
              response.choices &&
              response.choices.length > 0 &&
              response.choices[0].message &&
              response.choices[0].message.content
            ) {
              return response.choices[0].message.content.trim();
            } else {
              throw new Error('Không nhận được phản hồi hợp lệ từ OpenAI');
            }
          }
        } catch (error) {
          this.logger.error('Error calling OpenAI API:', error.message);
          if (error.response) {
            this.logger.error('OpenAI Error Data:', error.response.data);
            this.logger.error('OpenAI Error Status:', error.response.status);
            this.logger.error('OpenAI Error Headers:', error.response.headers);
          }
          throw new MoleculerClientError(`Lỗi khi gọi OpenAI API: ${error.message}`, error.status || 500, error.code);
        }
      },
    },

    /**
     * Xử lý tin nhắn từ học viên và trả về phản hồi từ AI Persona
     *
     * @param {Object} ctx - Context
     * @returns {Object} - Phản hồi từ AI Persona và thông tin phiên
     */
    processStudentMessage: {
      params: {
        sessionId: 'string',
        message: 'string',
        personaId: 'string',
        courseId: 'string',
        taskId: 'string',
        jsonSchema: {type: 'object', optional: true},
        schemaName: {type: 'string', optional: true},
        model: {type: 'string', optional: true},
        temperature: {type: 'number', optional: true},
        maxTokens: {type: 'number', optional: true},
      },
      async handler(ctx) {
        const {sessionId, message, personaId, courseId, taskId, jsonSchema, schemaName, model, temperature, maxTokens} =
          ctx.params;

        try {
          // Lấy thông tin AI Persona
          const persona = await ctx.call('roleplay.aipersonas.get', {id: personaId});
          if (!persona) {
            throw new MoleculerClientError('Không tìm thấy AI Persona', 404);
          }

          // Lấy thông tin khóa học
          const course = await ctx.call('roleplay.courses.get', {id: courseId});
          if (!course) {
            throw new MoleculerClientError('Không tìm thấy khóa học', 404);
          }

          // Lấy thông tin nhiệm vụ
          const task = await ctx.call('roleplay.tasks.get', {id: taskId});
          if (!task) {
            throw new MoleculerClientError('Không tìm thấy nhiệm vụ', 404);
          }

          // Lấy lịch sử cuộc trò chuyện
          const conversationHistory = await ctx.call('roleplay.roleplaysessions.getConversationHistory', {
            sessionId,
            limit: 10, // Giới hạn chỉ lấy 10 tin nhắn gần nhất để tránh quá dài
          });

          // Tạo prompt
          const messages = await this.actions.createPersonaPrompt({
            persona,
            course,
            task,
            conversation: conversationHistory,
          });

          // Thêm tin nhắn hiện tại của học viên
          messages.push({
            role: 'user',
            content: message,
          });

          // Chuẩn bị tham số cho sendToOpenAI
          const sendToOpenAIParams = {
            messages,
            ...(model && {model}),
            ...(temperature && {temperature}),
            ...(maxTokens && {maxTokens}),
          };

          // Thêm tham số jsonSchema và schemaName nếu có
          if (jsonSchema) {
            sendToOpenAIParams.jsonSchema = jsonSchema;
            if (schemaName) {
              sendToOpenAIParams.schemaName = schemaName;
            }
          }

          // Gửi đến OpenAI và nhận phản hồi
          const aiResponse = await this.actions.sendToOpenAI(sendToOpenAIParams);

          // Xử lý phản hồi để lưu vào lịch sử
          let responseContent;
          if (jsonSchema) {
            // Nếu là JSON, chuyển đổi thành chuỗi để lưu vào lịch sử
            responseContent = JSON.stringify(aiResponse);
          } else {
            responseContent = aiResponse;
          }

          // Lưu tin nhắn của học viên và phản hồi của AI vào lịch sử
          await ctx.call('roleplay.roleplaysessions.saveConversation', {
            sessionId,
            messages: [
              {role: 'user', content: message},
              {role: 'assistant', content: responseContent},
            ],
          });

          return {
            sessionId,
            aiResponse,
            timestamp: new Date(),
          };
        } catch (error) {
          this.logger.error('Error processing student message:', error);
          throw new MoleculerClientError('Lỗi khi xử lý tin nhắn', 500);
        }
      },
    },

    // ACTION MỚI CHO STREAMING
    chatCompletionStream: {
      params: {
        messages: 'array',
        model: {type: 'string', optional: true},
        temperature: {type: 'number', optional: true},
        max_tokens: {type: 'number', optional: true}, // Sử dụng max_tokens theo SDK
      },
      async handler(ctx) {
        const {
          messages,
          model = this.settings.defaultModel,
          temperature = this.settings.temperature,
          // Chuyển đổi maxTokens từ settings nếu dùng, hoặc lấy từ params
          max_tokens = ctx.params.max_tokens || this.settings.maxTokens,
        } = ctx.params;

        const {apiKey} = ctx.meta; // Lấy API key từ meta đã được hook nạp

        if (!apiKey) {
          this.logger.error('OpenAI API key is missing.');
          throw new MoleculerClientError('OpenAI API key is missing.', 500, 'API_KEY_MISSING');
        }

        const openai = new OpenAI({apiKey});

        try {
          console.log(
            `#####################Calling OpenAI chat.completions.create (stream) with model: ${model}`,
            messages,
          );
          const stream = await openai.chat.completions.create({
            model,
            messages,
            temperature,
            max_tokens,
            stream: true,
          });
          return stream;
        } catch (error) {
          this.logger.error('Error calling OpenAI chat.completions.create (stream):', error.message);
          if (error.response) {
            this.logger.error('OpenAI Error Data:', error.response.data);
            this.logger.error('OpenAI Error Status:', error.response.status);
            this.logger.error('OpenAI Error Headers:', error.response.headers);
          }
          throw new MoleculerClientError(
            `Lỗi khi gọi OpenAI API (stream): ${error.message}`,
            error.status || 500,
            error.code,
          );
        }
      },
    },
  },

  methods: {
    async getAPIKey(ctx) {
      // Ưu tiên API key từ gptmodelprice nếu có ngữ cảnh model cụ thể
      // Tuy nhiên, chatCompletionStream có thể không luôn có model trong ctx.params trực tiếp cho getOneByGptModel
      // nên sẽ dùng key chung trước, hoặc service gọi nó phải pass apiKey nếu muốn logic phức tạp hơn.
      const setting = await ctx.call('settings.findOne');
      const apiKeyFromSettings = setting?.apiKeyOpenAI || process.env.OPENAI_API_KEY;

      if (!apiKeyFromSettings) {
        this.logger.warn('OpenAI API Key not found in settings or environment variables.');
      }
      ctx.meta.apiKey = apiKeyFromSettings;
      return ctx; // Hook before nên trả về ctx hoặc Promise<ctx>
    },

    /**
     * Chuyển đổi từ JSON schema sang Zod schema
     *
     * @param {Object} jsonSchema - JSON schema object
     * @returns {z.ZodType} - Zod schema
     */
    createZodSchemaFromObject(jsonSchema) {
      // Xử lý các kiểu dữ liệu cơ bản
      if (typeof jsonSchema !== 'object' || jsonSchema === null) {
        throw new Error('JSON Schema phải là một object');
      }

      // Xử lý các trường hợp đặc biệt
      if (jsonSchema.type === 'string') {
        return z.string();
      } else if (jsonSchema.type === 'number') {
        return z.number();
      } else if (jsonSchema.type === 'boolean') {
        return z.boolean();
      } else if (jsonSchema.type === 'array') {
        if (jsonSchema.items) {
          return z.array(this.createZodSchemaFromObject(jsonSchema.items));
        }
        return z.array(z.any());
      } else if (jsonSchema.type === 'object' || (!jsonSchema.type && jsonSchema.properties)) {
        // Xử lý object
        const shape = {};

        if (jsonSchema.properties) {
          for (const [key, value] of Object.entries(jsonSchema.properties)) {
            shape[key] = this.createZodSchemaFromObject(value);
          }
        }

        let schema = z.object(shape);

        // Xử lý required fields
        if (jsonSchema.required && Array.isArray(jsonSchema.required)) {
          for (const field of jsonSchema.required) {
            if (shape[field]) {
              shape[field] = shape[field].optional ? shape[field].unwrap() : shape[field];
            }
          }
          schema = z.object(shape);
        }

        return schema;
      }

      // Mặc định trả về any
      return z.any();
    },

    /**
     * Xây dựng prompt cho hệ thống dựa trên các thành phần
     *
     * @param {Object} persona - Thông tin AI Persona
     * @param {Object} course - Thông tin khóa học (bao gồm mảng tasks)
     * @returns {String} - Prompt hệ thống hoàn chỉnh
     */
    async buildSystemPrompt(persona, course) {
      // Thay thế các placeholder trong PERSONA_INSTRUCTION
      const personaInstruction = PERSONA_INSTRUCTION.replace('{{name}}', persona.name || '')
        .replace('{{role}}', persona.role || '')
        .replace('{{organization}}', persona.organization || '')
        .replace('{{mood}}', persona.mood || 'Bình thường')
        .replace('{{background}}', persona.background || '')
        .replace('{{concern}}', persona.concern || '')
        .replace('{{smallTalkLikely}}', persona.smallTalkLikely ? 'Cao' : 'Thấp')
        .replace('{{filterWords}}', persona.filterWords || '')
        .replace('{{voiceStyle}}', persona.voiceStyle || 'Bình thường');

      // Xử lý nhiều task từ course
      let tasksObjectives = '';
      let evaluationCriteria = '';
      course.tasks = course.taskIds;

      if (course.tasks && Array.isArray(course.tasks) && course.tasks.length > 0) {
        // Tổng hợp mục tiêu từ tất cả các task
        tasksObjectives = course.tasks
          .map((task, index) => `Mục tiêu ${index + 1}: ${task.description || 'Không có mục tiêu cụ thể'}`)
          .join('\n');

        // Tổng hợp tiêu chí đánh giá từ tất cả các task
        evaluationCriteria = course.tasks
          .map(
            (task, index) =>
              `Tiêu chí ${index + 1}: ${task.evaluationGuidelines || 'Không có tiêu chí đánh giá cụ thể'}`,
          )
          .join('\n');
      } else if (course.taskIds && Array.isArray(course.taskIds) && course.taskIds.length > 0) {
        // Tổng hợp mục tiêu từ tất cả các taskIds
        tasksObjectives = course.taskIds
          .map((task, index) => `Mục tiêu ${index + 1}: ${task.objective || 'Không có mục tiêu cụ thể'}`)
          .join('\n');

        // Tổng hợp tiêu chí đánh giá từ tất cả các taskIds
        evaluationCriteria = course.taskIds
          .map(
            (task, index) => `Tiêu chí ${index + 1}: ${task.evaluationCriteria || 'Không có tiêu chí đánh giá cụ thể'}`,
          )
          .join('\n');
      }

      const courseReferencesContent = course.references
        ? course.references
            .map(
              (ref, index) =>
                `Thông tin: ${index + 1}: ${ref.name || 'Không có tiêu đề'} - ${ref.content || ''}`,
            )
            .join('\n')
        : '';
      // Thay thế các placeholder trong COURSE_INSTRUCTION
      const courseInstruction = COURSE_INSTRUCTION.replace('{{courseName}}', course.name || '')
        .replace('{{courseDescription}}', course.description || '')
        .replace('{{courseReferences}}', courseReferencesContent || '');

      // Thay thế các placeholder trong TASK_INSTRUCTION
      const taskInstruction = TASK_INSTRUCTION.replace(
        '{{taskObjective}}',
        tasksObjectives || 'Không có mục tiêu cụ thể',
      ).replace('{{evaluationCriteria}}', evaluationCriteria || 'Không có tiêu chí đánh giá cụ thể');

      // Thay thế các placeholder trong CONVERSATION_STYLE
      const conversationStyle = CONVERSATION_STYLE.replace('{{mood}}', persona.mood || 'Bình thường')
        .replace('{{voiceStyle}}', persona.voiceStyle || 'Bình thường')
        .replace(/{{#if smallTalkLikely}}(.*?){{else}}(.*?){{\/if}}/s, persona.smallTalkLikely ? '$1' : '$2')
        .replace('{{filterWords}}', persona.filterWords || '');

      if(course?.roleplayInstructionId?.conversationInstruction){
        let prompt = course.roleplayInstructionId?.conversationInstruction.replace('{{personaInstruction}}', personaInstruction)
          .replace('{personaInfo}', personaInstruction)
          .replace('{referenceInfo}', courseReferencesContent)
        console.log("roleplayInstruction", prompt)
        return `${prompt}\n\n${RESPONSE_FORMAT}`;
      }

      // Ghép tất cả các phần lại với nhau
      return `${SYSTEM_INSTRUCTION}\n\n${personaInstruction}\n\n${courseInstruction}\n\n${conversationStyle}\n\n${RESPONSE_FORMAT}`;
    },

    /**
     * Lọc từ ngữ không phù hợp trong phản hồi của AI
     *
     * @param {String} response - Phản hồi từ AI
     * @param {Array} filterWords - Danh sách từ ngữ cần lọc
     * @returns {String} - Phản hồi đã được lọc
     */
    filterResponse(response, filterWords) {
      if (!response || !filterWords || !Array.isArray(filterWords) || filterWords.length === 0) {
        return response;
      }

      let filteredResponse = response;

      // Lọc từng từ trong danh sách
      filterWords.forEach(word => {
        if (word && word.trim()) {
          // Tạo regex có tính đến ranh giới từ và không phân biệt hoa thường
          const regex = new RegExp(`\\b${word.trim()}\\b`, 'gi');
          // Thay thế bằng dấu sao tương ứng với độ dài của từ
          filteredResponse = filteredResponse.replace(regex, '*'.repeat(word.length));
        }
      });

      return filteredResponse;
    },
  },

  created() {
    // Khởi tạo khi service được tạo
  },

  async started() {
    console.log('started openai service');
  },

  async stopped() {
    console.log('stopped openai service');
  },
};
