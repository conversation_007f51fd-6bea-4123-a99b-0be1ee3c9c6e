exports.USER_CODES = {
  SYSTEM_ADMIN: 'SYSTEM_ADMIN',
  ORG_ADMIN: 'admin',
  CONTRIBUTOR: 'contributor',
  NORMAL: 'normal',
  TEACHER: 'teacher',
  STUDENT: 'student',
};

exports.PDF_WIDTH = 595.273;
exports.PDF_HEIGHT = 841.886;
exports.PERMISSION_ACCESS = {
  NO_PERMISSION: 'NO_PERMISSION',
  OWNER: 'OWNER',
  EDITOR: 'EDITOR',
  VIEWER: 'VIEWER',
};

exports.ACCESS_CODE = {
  SUBMIT_TEXT: 'SUBMIT_TEXT',
  SUBMIT_MEDIA: 'SUBMIT_MEDIA',
  INPUT_LIMIT_ON_MEDIA: 'INPUT_LIMIT_ON_MEDIA',
  MEDIA_CAPACITY: 'MEDIA_CAPACITY',
  EXPORT_FILE: 'EXPORT_FILE',
  EXTRACT_TEXT_FROM_IMAGE: 'EXTRACT_TEXT_FROM_IMAGE',
  EXTRACT_TEXT_FROM_PDF: 'EXTRACT_TEXT_FROM_PDF',
  EXPORT_TO_GG_FORM: 'EXPORT_TO_GG_FORM',
  MULTIPLE_SEATS: 'MULTIPLE_SEATS',
  PERSONAL_SUPPORT: 'PERSONAL_SUPPORT',
  SUBMIT_WRITING: 'SUBMIT_WRITING',
  SUBMIT_SPEAKING: 'SUBMIT_SPEAKING',
  DOWNLOAD_REPORT: 'DOWNLOAD_REPORT',
  SUBMIT_TEXT_ADD_ON: 'SUBMIT_TEXT_ADD_ON',
  SUBMIT_MEDIA_ADD_ON: 'SUBMIT_MEDIA_ADD_ON',
  MEDIA_CAPACITY_ADD_ON: 'MEDIA_CAPACITY_ADD_ON',
  SUBMIT_WRITING_ADD_ON: 'SUBMIT_WRITING_ADD_ON',
  SUBMIT_SPEAKING_ADD_ON: 'SUBMIT_SPEAKING_ADD_ON',
  DICTATION: 'DICTATION',
  SHADOWING: 'SHADOWING',
  SPEAKING_ROOM: 'SPEAKING_ROOM',
};

exports.PERSONA = {
  TEACHER_FOR_GRADES_1_2_3: 'TEACHER_FOR_GRADES_1_2_3',
  LECTURER_AT_UNIVERSITY: 'LECTURER_AT_UNIVERSITY',
  LANGUAGE_CENTER: 'LANGUAGE_CENTER',
  IELTS_CENTER: 'IELTS_CENTER',
  INDEPENDENT_TEACHER: 'INDEPENDENT_TEACHER',
  ENGLISH_CENTER_MANAGER: 'ENGLISH_CENTER_MANAGER',
  OTHER: 'OTHER',
};

exports.EXAM_CODE = {
  grade_3: 'grade 3',
  grade_4: 'grade 4',
  grade_5: 'grade 5',
  grade_6: 'grade 6',
  grade_7: 'grade 7',
  grade_8: 'grade 8',
  grade_9: 'grade 9',
  grade_10: 'grade 10',
  grade_11: 'grade 11',
  grade_12: 'grade 12',
};

exports.CONSTANT = {
  TEXT: "text",
  HTML: "html",
  HTML_TRIM_NBSP: "html_trim_nbsp",
  VIDEO: "video",
  OFFLINE_VIDEO: "offline_video",
  TOPIC: "topic",
  AUDIO: "audio",
  IMAGE: "image",
  FILE: "file",
  MARK_TEST: "mark_test",
  MARK_TEST_IMAGE: "mark_test_image",
  NONE: "none",
}

exports.INPUT_TYPE = {
  TEXT: "text",
  HTML: "html",
  HTML_TRIM_NBSP: "html_trim_nbsp",
  VIDEO: "video",
  OFFLINE_VIDEO: "offline_video",
  TOPIC: "topic",
  AUDIO: "audio",
  AUDIO_STREAM: "audio_stream",
  IMAGE: "image",
  FILE: "file",
  MARK_TEST: "mark_test",
  MARK_TEST_TASK_1: "mark_test_task_1",
  MARK_TEST_IMAGE: "mark_test_image",
  MARK_TEST_TASK_2: "mark_test_task_2",
  TTS: "text_to_speech",
  NONE: "none",
  STUDENT_TASK_1: "student_task_1",
  STUDENT_TASK_2: "student_task_2",
  STUDENT_SPEAKING: "student_speaking",
}

exports.OUTPUT_TYPE = {
  MARK_TEST_WRITING: "mark_test_writing",
  MARK_TEST_IELTS_WRITING: "mark_test_ielts_writing",
  TEXT: "text",
  ESSAY_TOPICS: "essay_topics",
  ADVANTAGES_AND_DISADVANTAGES: "advantages_and_disadvantages",
  WRITING_TASK: "writing_task",
  SCRAMBLE_WORDS: "scramble_words",
  WORDS: "words",
  MATCHING_WORDS: "matching_words",
  DIALOGUES: "dialogues",
  FILL_GAPS: "fill_gaps",
  MULTI_CHOICE: "multi_choice",
  OPEN_QUESTION: "open_question",
  TF_QUESTION: "tf_question",
  HTML: "html",
  OPTIONS: "options",
  HTML_QUESTIONS: "html_questions",
  AUDIO: "audio",
  PRON_FEEDBACK: "pron_feedback",
}

exports.USER_STATE = {
  ACTIVE: "active",
  WAITLIST: "waitlist",
}
exports.MEDIA_INPUT_TYPE = [
  "video",
  "offline_video",
  "audio",
  "image",
  "mark_test",
  "mark_test_image",
  "mark_test_image",
  "text_to_speech",
  "mark_test_task_1",
  "mark_test_task_2",
  "student_task_1",
  "student_task_2",
]


exports.DEFAULT_PROJECT_NAME = {
  NEW_PROJECT: "New project",
  UNTITLED_ESSAY: "Untitled essay",
  UNTITLED_SPEECH: "Untitled speech",
}

exports.DOCUMENT_TYPE = {
  MY_FILE: "my_file",
  SHARED_WITH_ME: "shared_with_me",
}

exports.FEEDBACK_TYPE = {
  ICON: "icon",
  NUMBER: "number",
  FREE: "free",
  PAID: "paid",
  COMMENT: "comment",
}

// Cấu hình cho audio processing
exports.AUDIO_PROCESSING_DEFAULTS = {
  silenceThreshold: 2000, // Time in ms to consider silence as end of speech
};
