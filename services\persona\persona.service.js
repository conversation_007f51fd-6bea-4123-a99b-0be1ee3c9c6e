const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./persona.model");
const BaseService = require("../../mixins/baseService.mixin");
const FileMixin = require("../../mixins/file.mixin");
const AuthRole = require("../../mixins/authRole.mixin");
const {SERVICE_NAME} = require("./permissions");

module.exports = {
  name: SERVICE_NAME,
  mixins: [DbMongoose(Model), BaseService, FileMixin, AuthRole],
  settings: {
    populates: {
      "templateId": "templates.get",
      "toolId": "tools.get",
    },
    populateOptions: ["templateId.imageId", "toolId"]
  },
  actions: {},

  events: {},

  methods: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {

  },
};
