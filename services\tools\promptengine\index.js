const {EXAM_CODE, INPUT_TYPE} = require("../../../constants/constant");
const INIT_INSTRUCTION = {
  role: "system",
  content: "## You are a good English teacher and have a lot of experience in teaching English in VietNam." +
    " You have been teaching in school and in English teaching center. You have IELTS certificated and have been teaching IELTS student for 10 years. ",
};

const appendInput = (messages, inputMessage, input) => {
  if (!input) return;
  messages.push(
    {
      role: "user",
      content: `## ${inputMessage ? inputMessage : "Given output following this input"}:\n ${input}`,
    },
  );
};
// append chatgpt prompt to messages for chat type is text
const appendToolInstruction = (messages, instruction) => {
  messages.push({
    role: "user",
    content: `## ${instruction}`,
  });
};


const appendToolOptions = (messages, instructionsOptions) => {
  instructionsOptions.forEach(instruction => messages.push({role: 'user', content: instruction}));
};
const appendRequirements = (messages, inputData) => {
  const {additionalRequest} = inputData;
  if (!additionalRequest) return;
  messages.push(
    {
      role: "user",
      content: `## ${additionalRequest}`,
    },
  );
};
const appendKnowledges = (messages, knowledges) => {
  if (knowledges.length === 0) return;

  const messageContent = knowledges.reduce((content, knowledge) => {
    return `${content}${knowledge.content}\n------------------------------------------------------------\n`;
  }, "## Based on the following knowledge:\n");

  messages.push({
    role: "system",
    content: messageContent,
  });
};

// append chatgpt prompt to messages for chat type is image
const appendMultiModalInstruction = (messages, instruction) => {
  messages.push({
    role: "user",
    content: [
      {type: INPUT_TYPE.TEXT, text: instruction},
    ],
  });
};

const appendMultiModalOptions = (messages, inputData, options) => {
  for (const [key, value] of Object.entries(inputData)) {
    const option = options?.find(option => option.code === key);
    if (option && value) {
      const {instruction} = option;
      messages[0].content.push({type: 'text', text: `${instruction?.replaceAll(`{${key}}`, value)}.`});
    }
  }
};

function isValidURL(str) {
  const pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
  return !!pattern.test(str);
}

function isValidMultiURL(urls) {
  const checkUrl = (url) => {
    return isValidURL(url) && isNotLocalhost(url);
  }
  return urls.every(checkUrl);
}

function isNotLocalhost(url) {
  return !url.startsWith("http://localhost");
}

function imageUrlMessage(imageUrl) {
  return {
    type: "image_url",
    image_url: {
      url: `${imageUrl}`,
      detail: "high"
    },
  };
}

function imageBase64Message(imageBase64) {
  return {
    type: "image_url",
    image_url: {
      url: `data:image/jpeg;base64,${imageBase64}`,
      detail: "high"
    },
  };
}

function markTestImageTopicInstruction() {
  return {
    type: INPUT_TYPE.TEXT, text: "Contains the task description and visual (e.g., chart, graph, table, or map): "
  };
}


const appendMultiModalInput = (messages, inputMessage, inputData, text) => {
  const {imageUrl, imageUrls, input, imageBase64, imageBase64s, topicImageUrl, topicImageBase64} = inputData;
  if (!imageUrl && !input && !imageBase64 && !imageUrls && !imageBase64s && !topicImageUrl && !topicImageBase64 && !text) return;
  if (topicImageUrl && isValidURL(topicImageUrl) && isNotLocalhost(topicImageUrl)) {
    messages[0].content.push(
      markTestImageTopicInstruction(),
      imageUrlMessage(topicImageUrl)
    );
  } else if (topicImageBase64) {
    messages[0].content.push(
      markTestImageTopicInstruction(),
      imageBase64Message(topicImageBase64)
    );
  }
  messages[0].content.push({
    type: INPUT_TYPE.TEXT,
    text: `## ${inputMessage || "Given output following this input"}:`
  });

  if (text) {
    messages[0].content.push({
      type: INPUT_TYPE.TEXT,
      text: `${text}`,
    })
  } else if ((imageUrl && isValidURL(imageUrl) && isNotLocalhost(imageUrl)) || (imageUrls?.length > 0 && isValidMultiURL(imageUrls))) {
    messages[0].content.push(
      {type: INPUT_TYPE.TEXT, text: inputMessage},
      imageUrlMessage(imageUrl),
    );
  } else if (input) {
    messages[0].content.push(
      {
        type: INPUT_TYPE.TEXT,
        text: `${input}`,
      },
    );
  } else if (imageBase64) {
    messages[0].content.push(imageBase64Message(imageBase64));
  } else if (imageBase64s) {
    const appendImageBase64s = (imageBase64s) => {
      imageBase64s.forEach(imageBase64 => {
        messages[0].content.push(imageBase64Message(imageBase64));
      });
    }
    appendImageBase64s(imageBase64s);
  }
};

const createNewMessages = (systemMessage) => {
  return systemMessage ? [{role: "system", content: `## ${systemMessage}.`}] : [INIT_INSTRUCTION]
};

const completionOptionCreator = (instructionData, findRelatedKnowledges) => {
  const {gptModel, chatType, outputTypeId, schemaInstruction} = instructionData;

  return async (text, inputData, inputType) => {
    const isMultiModel = chatType === "image"
      && ((!(inputData.markTestType && ["text", "file"].includes(inputData.markTestType))
        || inputType === INPUT_TYPE.MARK_TEST_IMAGE
        || inputType === INPUT_TYPE.MARK_TEST_TASK_1
        || inputType === INPUT_TYPE.STUDENT_TASK_1));
    const schema = outputTypeId.schemaInstruction || schemaInstruction;
    const createMessagesFn = isMultiModel ? createMultiModalMessages : createMessages;
    const knowledgeAugmentor = await createKnowledgeAugmentor(findRelatedKnowledges);
    const messages = await createMessagesFn({inputData, text, instructionData}, knowledgeAugmentor);

    return {
      messages,
      schema,
      model: gptModel,
    };
  };
};

async function buildKnowledge(instructionData, instructionsOptionsPairs = [], knowledgeAugmentor) {
  const {options, knowledges} = instructionData;
  const optionsKnowledges = options?.flatMap(option => option.knowledgeIds);
  const augmentedKnowledges = knowledgeAugmentor ? await knowledgeAugmentor(instructionsOptionsPairs) : optionsKnowledges; // filter by similar instructions
  const allKnowledges = [...knowledges, ...augmentedKnowledges];
  return Array.from(new Map(allKnowledges.map(item => [item._id, item])).values());
}

function buildOptionsInstructionsPair(instructionData, inputData, mockData) {
  const {options} = instructionData;
  const instructionPairs = [];
  const {examOptions, commonOptions} = inputData;
  if (!mockData) {
    for (const [key, value] of Object.entries(inputData)) {
      const option = options?.find(option => option.code === key);
      const optionValue = value || option?.defaultValue;
      if (option && optionValue) {
        const {instruction} = option;
        instructionPairs.push({
          optionsKnowledges: option.knowledgeIds,
          instruction: `## ${instruction?.replaceAll(`{${key}}`, value)}.`
        });
      }
      if (key === "examCode") {
        instructionPairs.push({
          optionsKnowledges: option?.knowledgeIds || [],
          instruction: `## Tests for ${EXAM_CODE[value]} students.`
        });
      }
      if (examOptions && commonOptions) {
        for (const [key, value] of Object.entries(commonOptions)) {
          const option = examOptions.find(option => option.code === key);
          if (option && value) {
            const {instruction} = option;
            instructionPairs.push({
              optionsKnowledges: option?.knowledgeIds || [],
              instruction: `## ${instruction.replaceAll(`{${key}}`, value)}.`
            });
          }
        }
      }
    }
  } else {
    for (const {code, defaultValue, instruction} of options) {
      const optionValue = defaultValue || mockData;
      instructionPairs.push(`## ${instruction.replace(`{${code}}`, optionValue)}.`);
      instructionPairs.push({
        optionsKnowledges: [],
        instruction: `## ${instruction.replace(`{${code}}`, optionValue)}.`
      });
    }
  }

  return instructionPairs;
}

function buildOptionsInstructions(instructionData, inputData, mockData) {
  const {options} = instructionData;
  const {examOptions, commonOptions} = inputData;
  const instructions = [];

  const buildInstruction = (key, value, option) => {
    if (option && value) {
      const {instruction} = option;
      instructions.push(`## ${instruction?.replaceAll(`{${key}}`, value)}.`);
    }
  };

  if (!mockData) {
    for (const [key, value] of Object.entries(inputData)) {
      const option = options?.find(option => option.code === key);
      buildInstruction(key, value, option);
      if (key === "examCode") {
        instructions.push(`## Tests for ${EXAM_CODE[value]} students.`);
      }
    }
    if (examOptions && commonOptions) {
      for (const [key, value] of Object.entries(commonOptions)) {
        const option = examOptions.find(option => option.code === key);
        buildInstruction(key, value, option);
      }
    }
  } else {
    for (const {code, defaultValue, instruction} of options) {
      const optionValue = defaultValue || mockData;
      instructions.push(`## ${instruction.replaceAll(`{${code}}`, optionValue)}.`);
    }
  }

  return instructions;
}

function createKnowledgeAugmentor(findRelatedKnowlegdes) {
  return async (instructionsOptions) => {
    const knowledgesFindingTasks = instructionsOptions.map(pair => findRelatedKnowlegdes(pair.instruction, pair.optionsKnowledges));
    const augmentedKnowledges = (await Promise.all(knowledgesFindingTasks)).flat();
    return augmentedKnowledges;
  };

}

async function createMessages({inputData, text, instructionData, mockData}, knowledgeAugmentor) {
  const {instruction, inputMessage, systemMessage} = instructionData;
  const messages = createNewMessages(systemMessage);
  const instructionsOptions = buildOptionsInstructions(instructionData, inputData, mockData);
  const instructionsOptionsPairs = buildOptionsInstructionsPair(instructionData, inputData, mockData);
  // const knowledges = await buildKnowledge(instructionData, instructionsOptionsPairs, knowledgeAugmentor);
  // appendKnowledges(messages, knowledges);
  appendToolInstruction(messages, instruction);
  appendToolOptions(messages, instructionsOptions);
  appendInput(messages, inputMessage, text);
  appendRequirements(messages, inputData);
  return messages;
}

async function createMultiModalMessages({inputData, text, instructionData, mockData}, knowledgeAugmentor) {
  const {instruction, options, inputMessage, systemMessage} = instructionData;
  const messages = createNewMessages(systemMessage);
  const instructionsOptionsPairs = buildOptionsInstructionsPair(instructionData, inputData, mockData);
  // const knowledges = await buildKnowledge(instructionData, instructionsOptionsPairs, knowledgeAugmentor);
  // appendKnowledges(messages, knowledges);
  const multiModalMessages = [];
  appendMultiModalInstruction(multiModalMessages, instruction);
  appendMultiModalOptions(multiModalMessages, inputData, options);
  appendMultiModalInput(multiModalMessages, inputMessage, inputData, text);
  return [...messages, ...multiModalMessages];
}

module.exports = {
  createMessages,
  createMultiModalMessages,
  completionOptionCreator
};
