const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./activities.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const AuthRole = require("../../mixins/authRole.mixin");
const {USER_CODES} = require("../../constants/constant");

module.exports = {
  name: "activities",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "userId": 'users.get',
      "organizationId": 'organizations.get',
      "folderId": 'folders.get',
      "projectId": 'projects.get',
      "metadata.shareUserId": 'users.get',
      "metadata.oldFolderId": 'folders.get',
      "metadata.copyProjectId": 'projects.get',
      "metadata.copyFolderId": 'folders.get',
    },
    populateOptions: [
      "userId", "organizationId", "folderId", "projectId",
      "metadata.shareUserId", "metadata.oldFolderId", "metadata.copyProjectId", "metadata.copyFolderId",
    ],
  },

  hooks: {},

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.CONTRIBUTOR,
    }
  },
  methods: {},
  events: {
    "activities.logger": {
      async handler(ctx) {
        const payload = {
          userId: ctx.meta.user?._id,
          organizationId: ctx.meta.user?.organizationId,
          action: ctx.params.action,
          folderId: ctx.params.folderId,
          projectId: ctx.params.projectId,
          isDeleted: ctx.params.isDeleted,
          metadata: ctx.params.metadata,
        };
        return this.adapter.insert(payload);
      }
    },
  },
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
