const DbMongoose = require("../../../mixins/dbMongo.mixin");
const ContentModel = require("./content.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const StudentContents = require("./content.students");
const {MoleculerClientError} = require("moleculer").Errors;
const i18next = require("i18next");
const {ObjectId} = require("mongoose").Types;

const {
  PERMISSION_ACCESS,
  INPUT_TYPE,
  MEDIA_INPUT_TYPE,
  OUTPUT_TYPE,
  DEFAULT_PROJECT_NAME,
} = require("../../../constants/constant");

module.exports = {
  name: 'contents',
  mixins: [DbMongoose(ContentModel), BaseService, FunctionsCommon, StudentContents],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "toolId": "tools.get",
    },
    populateOptions: ["toolId", "toolId.instructionIds"],
  },

  hooks: {
    before: {
      "*": ["checkProjectPermission", "checkToolPermission"],
      "remove": "checkProjectExist",
      "submitInputData|submitMarkTestInputData": "checkSubmitPermission",
      "submitSpeaking|submitIeltsWriting": "checkStudentSubmitPermission",
    }
  },

  actions: {
    list : {
      visibility : 'public'
    },
    details: {
      rest: {
        method: "GET",
        path: "/project/:projectId",
      },
      auth: "required",
      async handler(ctx) {
        const {projectId} = ctx.params;
        const contents = await this.adapter.find({query: {projectId, isDeleted: false}, sort: 'contentIndex'});
        const contentTrans = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, contents);
        const queryIO = {contentId: this.extractIdFromList(contents), isDeleted: false};
        const actions = [
          {action: 'inputs.find', params: {query: queryIO}},
          {
            action: 'responses.find', params: {
              query: queryIO,
              fields: ["_id", "contentId", "inputId", "toolId", "isActivate", "state", "output", "examOrder",
                "outputType", "plaintext", "rating", "headline", "isDeleted"],
            }
          },
        ];
        const [inputs, responses] = await ctx.mcall(actions);
        const groupInputs = this.groupBy(inputs, "contentId");
        const groupResponses = this.groupBy(responses, "contentId");
        contentTrans.forEach(content => {
          content.inputs = groupInputs[content._id];
          content.responses = groupResponses[content._id];
        });
        return contentTrans;
      }
    },
    moveUp: {
      rest: {
        method: "PUT",
        path: "/:id/moveUp",
      },
      permission: 'EDITOR',
      auth: "required",
      /** @param {Context} ctx */
      async handler(ctx) {
        const {id} = ctx.params;

        const contentToMoveUp = await this.adapter.findById(id);
        const {projectId, contentIndex} = contentToMoveUp;

        const contentToMoveDown = await this.adapter.findOne({
          projectId,
          contentIndex: contentIndex - 1,
          isDeleted: false
        });

        if (!contentToMoveDown) {
          throw new MoleculerClientError(i18next.t('cannot_move_up_content'));
        }

        await this.swapContentIndex(contentToMoveUp, contentToMoveDown);
        ctx.emit('lastModifiedProject', {id: projectId});
        this.broker.emit('recentProject', {projectId, userId: ctx.meta?.user?._id});
        return this.adapter.updateById(id, {updatedAt: new Date()});
      }
    },
    moveDown: {
      rest: {
        method: "PUT",
        path: "/:id/moveDown",
      },
      permission: 'EDITOR',
      auth: "required",
      async handler(ctx) {
        const {id} = ctx.params;
        const contentToMoveDown = await this.adapter.findById(id);
        const {projectId, contentIndex} = contentToMoveDown;
        const contentToMoveUp = await this.adapter.findOne({
          projectId,
          contentIndex: contentIndex + 1,
          isDeleted: false
        });

        if (!contentToMoveUp) {
          throw new MoleculerClientError(i18next.t('cannot_move_down_content'));
        }

        await this.swapContentIndex(contentToMoveUp, contentToMoveDown);
        ctx.emit('lastModifiedProject', {id: projectId});
        this.broker.emit('recentProject', {projectId, userId: ctx.meta?.user?._id});
        return this.adapter.updateById(id, {updatedAt: new Date()});
      }
    },
    remove: {
      rest: "DELETE /:id",
      params: {
        id: "string",
      },
      permission: 'EDITOR',
      auth: "required",
      async handler(ctx) {
        const {id} = ctx.params;
        const content = await this.adapter.findById(id);
        await this.broker.emit('contents.deteted', content);
        await this.updateContentIndex(content);
        ctx.emit('lastModifiedProject', {id: content?.projectId});
        this.broker.emit('recentProject', {projectId: content?.projectId, userId: ctx.meta?.user?._id});
        return await this.adapter.updateById(id, {isDeleted: true}, {new: true});
      }
    },
    submitInputData: {
      rest: "PUT /:id/submitInputData",
      params: {
        inputData: {type: "object", required: true},
        inputType: {type: "string"},
      },
      auth: "required",
      toolPermission: true,
      async handler(ctx) {
        const {inputData, inputType, id, workspaceId, examOrder, projectType} = ctx.params;
        const content = await this.adapter.findById(id);
        const {user} = ctx.meta;
        const permissionAccess = await ctx.call('projects.permissionAccess', {id: content.projectId});
        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }

        const inputEntity = {
          workspaceId: workspaceId,
          inputData,
          inputType,
          userId: user?._id,
          toolId: content.toolId,
          contentId: content._id,
          plaintext: await this.getInputPlaintext(inputType, inputData)
        };
        const savedInput = await ctx.call('inputs.insert', {entity: inputEntity});
        const processingResponse = {
          inputId: savedInput._id,
          contentId: content._id,
          toolId: content.toolId,
          output: {
            text: "Hold on! We are processing"
          },
          state: "processing",
          isActivate: true, examOrder
        };
        if ([INPUT_TYPE.OFFLINE_VIDEO, INPUT_TYPE.AUDIO, INPUT_TYPE.VIDEO].includes(inputType)) {
          processingResponse.gptModel = "whisper-1";
        }
        if (inputType === INPUT_TYPE.IMAGE) {
          processingResponse.gptModel = "gpt-4o-mini";
        }
        const savedResponse = await ctx.call('responses.insert', {entity: processingResponse});

        return this.responseSubmit(ctx, savedResponse, content, savedInput, projectType, workspaceId);
      }
    },

    submitMarkTestInputData: {
      rest: "PUT /:id/submitMarkTestInputData",
      params: {
        input: {type: "object"},
      },
      auth: "required",
      toolPermission: true,
      async handler(ctx) {
        const {input, id, workspaceId} = ctx.params;
        const {_id: inputId, inputData, inputType, numberSubmit} = input;
        const content = await this.adapter.findById(id);
        const permissionAccess = await ctx.call('projects.permissionAccess', {id: content.projectId});
        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }

        await ctx.call('inputs.update', {
          id: inputId.toString(),
          plaintext: await this.getInputPlaintext(inputType, inputData),
          isHasResponse: true,
          workspaceId,
          numberSubmit: numberSubmit + 1
        });

        const processingResponse = {
          inputId,
          contentId: content._id,
          toolId: content.toolId,
          output: {
            text: "Hold on! We are processing"
          },
          state: "processing",
          isActivate: true
        };
        let savedResponse;
        if (input.isEditing && input.isHasResponse) {
          const response = await ctx.call('responses.getOneByInputId', {inputId: input._id.toString()});
          processingResponse.previousState = response?.state;
          processingResponse.previousOutput = response?.output;
          processingResponse.canceledSubmit = false
          savedResponse = await ctx.call('responses.updateStreamResponse', {id: response?._id.toString(), ...processingResponse});
        } else {
          savedResponse = await ctx.call('responses.insert', {entity: processingResponse});
        }

        // return this.responseSubmit(ctx, savedResponse, content, input, "EXAM", workspaceId);
        return this.responseSubmit(ctx, savedResponse, content, input, "MARK_TEST", workspaceId);
      }
    },
    submitInput: {
      rest: "POST /submitInput",
      params: {
        input: "object",
        processingResponse: "object",
      },
      auth: "required",
      // toolPermission: true,
      async handler(context) {
        let {input, processingResponse, projectId} = context.params;
        try {
          await this.adapter.updateById(input.contentId, {
            lastInput: {
              inputType: input.inputType,
              inputData: input.inputData
            }
          });
          const serviceActions = [
            INPUT_TYPE.MARK_TEST_TASK_1, INPUT_TYPE.MARK_TEST_TASK_2, INPUT_TYPE.AUDIO_STREAM,
            INPUT_TYPE.STUDENT_TASK_1, INPUT_TYPE.STUDENT_TASK_2, INPUT_TYPE.STUDENT_SPEAKING
          ].includes(input.inputType) ? "submitMultiSections" : "submit"
          const response = await context.call(`tools.${serviceActions}`, {
            input,
            response: processingResponse
          });
          if (response?.success) {
            processingResponse = {
              ...processingResponse,
              ...response,
              state: "done",
            };
            delete processingResponse.isDeleted
            await this.broker.call("responses.updateStreamResponse", {
              ...processingResponse,
              id: processingResponse._id,
              projectId
            });
          } else {
            processingResponse = {
              ...processingResponse,
              output: {error: response, message: "System busy please try again!"},
              state: "error",
            };
            await this.broker.call("responses.updateStreamResponse", {
              ...processingResponse,
              id: processingResponse._id,
            });
          }
          await this.broker.emit(`sse.${processingResponse._id}`, processingResponse);
        } catch (error) {
          console.log(error)
          processingResponse = {
            ...processingResponse,
            output: {error, message: "System busy please try again!"},
            state: "error",
          };
          await this.broker.call("responses.updateStreamResponse", {
            ...processingResponse,
            id: processingResponse._id,
          });
          await this.broker.emit(`sse.${processingResponse._id}`, processingResponse);
        }

        return processingResponse;
      }
    },
    lastUpdatedContent: {
      params: {
        id: "string",
      },
      auth: "required",
      async handler(ctx) {
        const {id} = ctx.params;
        const content = await this.adapter.findById(id);
        ctx.emit('lastModifiedProject', {id: content.projectId});
        this.broker.emit('recentProject', {projectId: content.projectId, userId: ctx.meta?.user?._id});
        return this.adapter.updateById(id, {updatedAt: new Date()}, {new: true});
      }
    },
    moveContent: {
      rest: {
        method: "PUT",
        path: "/:id/moveContent",
      },
      permission: 'EDITOR',
      auth: "required",
      async handler(ctx) {
        const {id, newContentIndex} = ctx.params;
        const content = await this.adapter.findById(id);
        const {projectId, contentIndex} = content;

        const isMovingUp = contentIndex > newContentIndex;
        const query = {
          projectId, isDeleted: false,
          contentIndex: isMovingUp
            ? {$gte: newContentIndex, $lt: contentIndex}
            : {$gt: contentIndex, $lte: newContentIndex},
        };
        const update = {$inc: {contentIndex: isMovingUp ? 1 : -1}};
        await this.adapter.updateMany(query, update);
        return await this.adapter.updateById(id, {contentIndex: newContentIndex});
      }
    },
    createFromTemplate: {
      rest: {
        method: "POST",
        path: "/createFromTemplate",
      },
      auth: "required",
      async handler(ctx) {
        const data = ctx.params;
        const newContent = await this.adapter.insert(data);

        if (data.inputs && !!data.inputs[0]) {
          const input = data.inputs[0];
          const response = data.responses ? data.responses[0] : [];
          const inputEntity = {
            inputData: input.inputData,
            inputType: input.inputType,
            contentId: newContent._id,
          };
          ctx.emit('lastModifiedProject', {id: newContent.projectId});
          const newInput = await this.broker.call('inputs.insert', {entity: inputEntity});
          const newResponse = await this.broker.call('responses.insert', {
            entity: {
              ...response,
              inputId: newInput._id,
              contentId: newContent._id
            }
          });
        }


        return data;
      }
    },

    submitExam: {
      rest: {
        method: "POST",
        path: "/submitExam1",
      },
      async handler(ctx) {
        const {
          workspaceId,
          data,
          numberOfExam = 1,
          examCode,
          projectId,
          commonOptions
        } = ctx.params;
        const project = await ctx.call('projects.get', {id: projectId});
        const examOptions = await ctx.call("options.find", {
            query: {isExamOption: true},
            fields: ["instructionId", "code", "name", "type", "rule", "placeholder", "defaultValue", "selectOptions", "instruction", "localization", "isExamOption",]
          },
        );
        const availableExam = project.numberOfExams || 0;
        const promises = [];
        for (let i = 0; i < numberOfExam; i++) {
          data?.forEach(content => {
            promises.push(ctx.call('contents.submitInputData', {
              id: content.contentId,
              inputData: {...content.inputData, examCode, commonOptions, examOptions},
              inputType: content.inputType,
              workspaceId,
              isExam: true,
              projectType: project.type,
              examOrder: i + 1 + availableExam
            }));
          });
        }
        const allResponses = await Promise.all(promises);
        await ctx.call("projects.update", {
          id: projectId,
          commonOptions,
          numberOfExams: availableExam + numberOfExam,
          activeExam: availableExam + numberOfExam
        });
        return allResponses;
      }
    },
    submitOneExam: {
      rest: {
        method: "POST",
        path: "/submitExam",
      },
      async handler(ctx) {
        const {
          workspaceId, data, examCode, projectId, commonOptions
        } = ctx.params;
        const [project, examOptions] = await Promise.all([
          ctx.call('projects.get', {id: projectId}),
          ctx.call('options.find', {
            query: {isExamOption: true},
            fields: [
              "instructionId", "code", "name", "type", "rule",
              "placeholder", "defaultValue", "selectOptions",
              "instruction", "localization", "isExamOption"
            ]
          }),
        ]);


        const availableExam = project.numberOfExams || 0;
        const promises = [];
        const responses = await ctx.call("responses.find", {
          query: {
            contentId: {$in: data.map(content => content.contentId)},
            isDeleted: false,
            examOrder: project.activeExam
          },
          fields: ["_id", "contentId", "examOrder"]
        })
        const contentIdsHaveResponse = new Set(responses.map(response => response.contentId.toString()));
        const newContentIds = data
          .map(content => content.contentId)
          .filter(contentId => !contentIdsHaveResponse.has(contentId.toString()));
        const isAddNewContent = newContentIds.length > 0
        const dataSubmit = isAddNewContent ? data.filter(content => newContentIds.includes(content.contentId)) : data

        dataSubmit.forEach(content => {
          promises.push(ctx.call('contents.submitInputData', {
            id: content.contentId,
            inputData: {...content.inputData, examCode, commonOptions, examOptions},
            inputType: content.inputType,
            workspaceId,
            isExam: true,
            projectType: project.type,
            examOrder: (isAddNewContent && availableExam !== 0) ? project.activeExam : 1 + availableExam
          }));
        })
        const allResponses = await Promise.all(promises);
        if (!isAddNewContent || availableExam === 0) {
          await ctx.call("projects.update", {
            id: projectId,
            commonOptions,
            numberOfExams: availableExam + 1,
            activeExam: availableExam + 1
          });
        }
        return allResponses
      }
    },

    submitMultiMarkTests: {
      rest: {
        method: "POST",
        path: "/submitMarkTests",
      },
      toolPermission: true,
      async handler(ctx) {
        const {workspaceId, contentId, inputIds, projectId, commonOptions} = ctx.params;
        const [inputs, projects, responses] = await Promise.all([
          this.broker.call('inputs.find', {query: {_id: {$in: inputIds}}}),
          ctx.call("projects.get", {id: projectId}),
          this.broker.call('responses.find', {query: {inputId: {$in: inputIds}, isDeleted: false}}),
        ])
        const mapState = responses.reduce((map, item) => {
          map[item.inputId._id] = item.state;
          return map;
        }, {});

        const inputSubmit = inputs.filter(input => input.isEditing || !input.isHasResponse || mapState[input._id] === "error");
        if (inputSubmit.length === 0) {
          return {
            success: true,
            message: i18next.t("all_essay_has_been_submitted")
          };
        }
        const promises = inputSubmit.map(input => {
          const isEditing = mapState[input._id] === "error" ? true : input.isEditing;
          return ctx.call("contents.submitMarkTestInputData", {
            id: contentId,
            workspaceId,
            input: {...input, inputData: {...projects.commonOptions, ...input.inputData}, isEditing},
          })
        });
        const allResponses = await Promise.all(promises);

        await ctx.call("inputs.updateMany", {
          query: {_id: {$in: inputIds}},
          update: {isEditing: false}
        })
        ctx.emit("listenProject", {projectId, responses: allResponses});
        return allResponses;
      }
    },

    splitInputData: {
      rest: {
        method: "POST",
        path: "/splitInput",
      },
      params: {
        inputData: {type: "object", required: true},
        inputType: {type: "string"},
      },
      auth: "required",
      async handler(ctx) {
        const {inputData, inputType, projectId, contentId, workspaceId} = ctx.params;
        const content = await this.adapter.findById(contentId);
        const {user} = ctx.meta;
        const permissionAccess = await ctx.call('projects.permissionAccess', {id: projectId});
        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }
        if ([INPUT_TYPE.OFFLINE_VIDEO, INPUT_TYPE.AUDIO, INPUT_TYPE.VIDEO].includes(inputType)) {
          throw new MoleculerClientError(i18next.t("input_data_not_supported"), 400);
        }

        const inputEntity = {
          workspaceId: workspaceId,
          inputData,
          inputType,
          userId: user?._id,
          toolId: content.toolId,
          contentId: content._id,
        };
        const response = await this.broker.call("tools.splitInput", {
          input: inputEntity,
        });
        return response
      }
    },


    studentDetails: {
      auth: "required",
      async handler(ctx) {
        const {projectIds, inputType} = ctx.params;

        return ContentModel.aggregate([
          {$match: {projectId: {$in: projectIds}, isDeleted: false}},
          {
            $lookup: {
              from: "Response",
              let: {contentId: "$_id"},
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$contentId", "$$contentId"]
                    },
                    isActivate: true,
                  },
                },
                {
                  $lookup: {
                    from: "Input",
                    let: {inputId: "$inputId"},
                    pipeline: [
                      {
                        $match: {
                          $expr: {$eq: ["$_id", "$$inputId"]},
                          ...(inputType && {inputType: {$in: inputType.split(",").map(type => type.trim())}}),
                        },
                      },
                      {
                        $project: {
                          inputType: 1,
                          "inputData.topic": 1,
                        }
                      }
                    ],
                    as: "inputs"
                  }
                },
                {
                  $match: {inputs: {$ne: []}}
                },
                {
                  $project: {
                    inputId: 1,
                    input: {$arrayElemAt: ["$inputs", 0]},
                    isActivate: 1,
                    isDeleted: 1,
                    "output.overallBandScore": "$output.overallBandScore",
                    "output.score": "$output.score",
                    "output.topic": "$output.topic",
                  }
                },
              ],
              as: "responses"
            }
          },
          {$match: {responses: {$ne: []}}},
          {
            $project: {
              _id: 1,
              projectId: 1,
              response: {$arrayElemAt: ["$responses", 0]},
              isDeleted: 1,
            }
          }
        ]);
      }
    },

    permissionAccess: {
      async handler(ctx) {
        const {id} = ctx.params;
        const content = await this.adapter.findById(id);
        if (!content) {
          throw new MoleculerClientError(i18next.t("error_content_not_found"), 404);
        }
        const permission = await ctx.call('projects.permissionAccess', {id: content?.projectId});
        return this.editAccess(permission);
      }
    },
  },
  methods: {
    async responseSubmit(ctx, savedResponse, content, input, projectType, workspaceId, projectName) {
      const {inputData, inputType} = input;

      ctx.call('responses.deactivate', {
        contentId: savedResponse.contentId,
        id: savedResponse._id
      });
      const projectEntity = projectName ? {id: content.projectId, projectName, isDraft: false} : {
        id: content.projectId,
        isDraft: false
      };
      await ctx.call("projects.update", projectEntity);
      ctx.emit('lastModifiedProject', {id: content.projectId});

      if (["EXAM_SCHOOL", "EXAM_IELTS"].includes(projectType)) {
        await ctx.call("contents.submitInput", {
          input,
          processingResponse: savedResponse,
          projectId: content.projectId
        });
      } else {
        ctx.call("contents.submitInput", {input, processingResponse: savedResponse, projectId: content.projectId});
      }

      ctx.emit('recentProject', {projectId: content.projectId, userId: ctx.meta.user?._id});
      ctx.emit("resourceUpdate", {inputData, inputType, projectId: content.projectId});
      ctx.emit("imageUpdateSubmit", {inputData});
      await ctx.emit("userSubmited", {inputType, workspaceId});
      return ctx.call('responses.get', {id: savedResponse._id, populate: ['inputId']});
    },

    async copyInputs(mapContent, oldContentId) {
      const inputs = await this.broker.call('inputs.find', {query: {contentId: oldContentId, isDeleted: false}});

      const newInputs = inputs.map(input => ({
        action: 'inputs.insert',
        params: {
          entity: {
            toolId: input.toolId,
            inputType: input.inputType,
            inputData: input.inputData,
            contentId: mapContent[input.contentId]
          }
        }
      }));

      const mcallInputs = await this.broker.mcall(newInputs);

      const mapInput = {};
      mcallInputs.forEach((input, index) => {
        mapInput[inputs[index]._id] = {
          inputId: input._id,
          contentId: input.contentId
        };
      });

      return mapInput;
    },
    async copyResponses(mapInput, oldContentId) {

      const responses = await this.broker.call('responses.find', {
        query: {contentId: oldContentId, isDeleted: false},
        populate: [],
      });

      const newResponses = responses.map(response => {
        const {toolId, state, output, isActivate, outputType, examOrder} = response;
        const {inputId, contentId} = mapInput[response.inputId];
        const entity = {toolId, state, output, inputId, contentId, isActivate, outputType, examOrder};
        return {action: 'responses.insert', params: {entity}};
      });

      await this.broker.mcall(newResponses);
    },
    async updateContentIndex(content) {
      const {_id: id, projectId} = content;
      const foundContents = await this.adapter.find({
        query: {projectId, isDeleted: false, _id: {$ne: id}},
        sort: 'contentIndex'
      });
      const mcallContents = foundContents.map(({_id}, index) => ({
        action: 'contents.update',
        params: {contentIndex: index + 1, id: _id}
      }));

      await this.broker.mcall(mcallContents);
    },
    async swapContentIndex(contentMoveUp, contentMoveDown) {
      const paramMoveUp = {contentIndex: contentMoveUp.contentIndex - 1, id: contentMoveUp._id};
      const paramMoveDown = {contentIndex: contentMoveDown.contentIndex + 1, id: contentMoveDown._id};
      const mcallSwap = [
        {action: 'contents.update', params: paramMoveUp},
        {action: 'contents.update', params: paramMoveDown}
      ];
      return this.broker.mcall(mcallSwap);
    },
    async getInputPlaintext(inputType, inputData) {
      switch (inputType) {
        case INPUT_TYPE.TEXT:
        case INPUT_TYPE.TTS:
          return `Text: ${inputData.text}`;
        case INPUT_TYPE.VIDEO:
        case INPUT_TYPE.AUDIO:
          return this.getVideoInput(inputData);
        case INPUT_TYPE.TOPIC:
          return `Topic: ${inputData.topic}`;
        default:
          return "";
      }
    },
    async getVideoInput(inputData) {
      const cutStartMs = this.secondsToMS(inputData.cutStart);
      const cutEndMs = this.secondsToMS(inputData.cutEnd);
      const timeRange = `Time start: ${cutStartMs} - time end: ${cutEndMs}\n`;
      if (inputData.url) {
        return timeRange + `Link youtube: ${inputData.url}`;
      }
      if (inputData.offlineVideoId) {
        const file = await this.broker.call("offlinevideos.get", {id: inputData.offlineVideoId.toString()});
        return timeRange + `Video: ${file.name}`;
      }
      if (inputData.audioId) {
        const file = await this.broker.call("files.get", {id: inputData.audioId.toString()});
        return timeRange + `Audio: ${file.displayName}`;
      }

    },
    async checkProjectPermission(context) {
      const {action, params} = context;
      const permission = action?.permission;

      if (permission === PERMISSION_ACCESS.EDITOR) {
        const {id} = params;
        const content = await this.adapter.findById(id);
        const projectId = content?.projectId;
        const permissionAccess = await context.call('projects.permissionAccess', {id: projectId});

        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t("dont_have_permission_project"), 403, "FORBIDDEN");
        }
      }
    },
    async checkSubmitPermission(ctx) {
      const {workspaceId, input} = ctx.params;
      const inputType = ctx.params.inputType || input.inputType;
      let organizationId;
      let userId;
      if (workspaceId) {
        const workspace = await ctx.call("workspaces.get", {id: workspaceId});
        ({organizationId, userId} = workspace);
      } else {
        userId = ctx.meta.user._id;
      }

      const permission = await ctx.call("permissions.getOne", {userId, organizationId});
      // const subscription = await ctx.call('subscriptions.getActive', {userId: ctx.meta.user?._id});
      // if (subscription.endDate < new Date()) {
      //   throw new MoleculerClientError(i18next.t("subscription_expired"), 400, "SUBSCRIPTION_EXPIRED");
      // }
      const isMedia = MEDIA_INPUT_TYPE.includes(inputType);
      if (!isMedia && permission?.accessLimit.textLimit !== "Unlimited" && permission?.accessLimit.textUsed >= +permission?.accessLimit.textLimit) {
        throw new MoleculerClientError(i18next.t("submit_text_tool_limited"), 400, "LIMIT_TEXT");
      }
      if (isMedia && permission?.accessLimit.mediaUsed >= +permission?.accessLimit.mediaLimit) {
        throw new MoleculerClientError(i18next.t("submit_media_tool_limited"), 400, "LIMIT_MEDIA");
      }
    },


    async checkProjectExist(context) {
      const {id} = context.params;
      const content = await this.adapter.findById(id);
      const project = await context.call('projects.get', {id: content.projectId.toString()});
      if (project.isDeleted === true) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
      }
    },
    async checkToolPermission(ctx) {
      const {action, params, meta} = ctx;
      const {contentId, id} = params;
      if (meta.user?.isSystemAdmin) return;

      if (action?.toolPermission) {
        const content = await this.adapter.findById({_id: contentId || id});
        if (!content) {
          throw new MoleculerClientError(i18next.t("error_content_not_found"), 404);
        }
        await ctx.call("tools.checkToolPermission", {id: content?.toolId});
      }
    }
  },
  events: {

    async "project.createdFromTool"(payload, sender, event, ctx) {
      this.logger.info("payload", payload, sender, event);
      const {lang} = ctx.meta
      const {contentTitle, localization} = await this.broker.call('tools.get', {id: payload.toolId});
      const title = localization?.contentTitle?.[lang] || contentTitle || i18next.t("response_of_content")
      await this.adapter.insert({...payload, title});
    },

    async "project.copied"(payload, sender, event, ctx) {
      this.logger.info("payload", payload, sender, event);
      const {oldProject, newProject} = payload;
      const contents = await this.adapter.find({query: {projectId: oldProject._id.toString(), isDeleted: false}});

      const newContents = contents.map(content => {
        const {toolId, title, description, contentIndex, lastInput} = content;
        const entity = {toolId, projectId: newProject._id, title, description, contentIndex, lastInput};
        return {action: 'contents.insert', params: {entity}};
      });
      const mcallContents = await ctx.mcall(newContents);

      const mapContent = {};
      mcallContents.forEach((content, index) => {
        mapContent[contents[index]._id] = content._id;
      });

      const oldContentId = this.extractIdFromList(contents);
      const mapInput = await this.copyInputs(mapContent, oldContentId);

      await this.copyResponses(mapInput, oldContentId);
    },
    async "project.deleted"(payload, sender, event) {
      this.logger.info("payload", payload, sender, event);
      const contents = await this.adapter.find({query: {projectId: payload, isDeleted: false}});
      await this.adapter.updateMany({projectId: payload}, {isDeleted: true});
      this.broker.emit('contents.deleteMany', this.extractIdFromList(contents));
    },

  },

};
