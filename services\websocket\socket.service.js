const {Server} = require("socket.io");

module.exports = {
  name: "socket",
  settings: {
    port: 3001,
  },
  dependencies: [
    "settings"
  ],



  async started() {

    // Khởi tạo Socket.IO server
    this.io = new Server(this.settings.port, {
      transports: ["websocket"],
      path: "/socket",
    });

    // Phân biệt kết nối dựa trên namespace/path
    this.io.of("student").on("connection", (socket) => {
      this.broker.emit("studentConnected", socket);
    });

    this.io.of("teacher").on("connection", (socket) => {
      this.broker.emit("teacherConnected", socket);

    });

    // Thêm namespace cho shadowing
    this.io.of("shadowing").on("connection", (socket) => {
      this.broker.emit("shadowingConnected", socket);
    });
    // Thêm namespace cho shadowing
    this.io.of("shadowing-premium").on("connection", (socket) => {
      this.broker.emit("shadowingPremiumConnected", socket);
    });

    // Thêm namespace cho explain service
    this.io.of("explain").on("connection", (socket) => {
      this.broker.emit("explainConnected", socket);
    });

    // Thêm namespace cho speaking service
    this.io.of("speaking-advanced").on("connection", (socket) => {
      this.broker.emit("speakingConnected", socket);
    });

    // ---- Role Play Namespace ----
    this.io.of("/roleplay").on("connection", async (socket) => {
      this.logger.info(`RolePlay client connected: ${socket.id}`);

      // Chuyển việc xử lý socket sang roleplaysessions.service.js
      this.broker.emit("roleplay.client.connected", socket);
    });
    // ---- End Role Play Namespace ----

    this.logger.info("Socket.IO server is running...");

  },

  stopped() {
    if (this.io) {
      console.log("Shutting down WebSocket server...");
      this.io.close();
    }
  },
};
