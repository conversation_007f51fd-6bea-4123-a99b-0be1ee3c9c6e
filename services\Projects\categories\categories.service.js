const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./categories.model");
const BaseService = require("../../../mixins/baseService.mixin");
const authRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: 'categories',
  mixins: [DbMongoose(Model), BaseService, authRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: []
  },

  hooks: {
    before: {}
  },

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    }
  },
  methods: {},
  events: {},
};
