const mongoose = require('mongoose');
const { Schema } = require('mongoose');
const { CUSTOM_THUMBNAIL, DOCX_TEMPLATES, USER, ORGANIZATION, FILE } = require('../../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema({

  docxTemplateId: { type: Schema.Types.ObjectId, ref: DOCX_TEMPLATES },
  userId: { type: Schema.Types.ObjectId, ref: USER },
  organizationId: { type: Schema.Types.ObjectId, ref: ORGANIZATION },
  customThumbnailId: { type: Schema.Types.ObjectId, ref: FILE },
  isDeleted: { type: Boolean, default: false },

}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(CUSTOM_THUMBNAIL, schema, CUSTOM_THUMBNAIL);

