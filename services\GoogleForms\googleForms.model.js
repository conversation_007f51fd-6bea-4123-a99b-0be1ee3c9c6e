const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { GOOGLE_FORM, USER } = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  idToken: { type: String },
  accessToken: { type: String },
  refreshToken: { type: String },
  email: { type: String },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: USER },
  isDeleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(GOOGLE_FORM, schema, GOOGLE_FORM);
