const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {ROLEPLAY_INSTRUCTION} = require("../../../constants/dbCollections");

const RoleplayInstructionSchema = new Schema({
    simulationType: {
      // Loại giả lập
      type: String,
      enum: ['Sale', 'Service', 'HR', 'Education', 'Other'],
      trim: true,
      required: true,
    },
    personaInstruction: {type: String},
    topicInstruction: {type: String},
    conversationInstruction: {type: String},
    analyzeInstruction: {type: String},
    name: {type: String, required: true},
    isDeleted : {type: Boolean, default: false},
}, {
    timestamps: true,
    collection: ROLEPLAY_INSTRUCTION,
});

const RoleplayInstructionModel = mongoose.model(ROLEPLAY_INSTRUCTION, RoleplayInstructionSchema);
module.exports = RoleplayInstructionModel;
