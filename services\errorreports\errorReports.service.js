const DbMongoose = require("../../mixins/dbMongo.mixin");
const Model = require("./errorReports.model");
const BaseService = require("../../mixins/baseService.mixin");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const {USER_CODES} = require("../../constants/constant");
const {MoleculerClientError} = require("moleculer").Errors;

module.exports = {
  name: "errorreports",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "userId": 'users.get',
      "imageIds": 'files.get',
      "videoId": 'files.get',
    },
    populateOptions: ["userId", "imageIds", "videoId"],
  },

  hooks: {
    after: {
      'create': 'afterCreate',
    },
  },

  actions: {
    create: {
      rest: "POST /",
      auth: "required",
      params: {},
      async handler(ctx) {
        // Lấy userId từ authenticated user
        const userId = ctx.meta.user?._id || ctx.meta.userID;
        if (!userId) {
          throw new MoleculerClientError("User not authenticated", 401, "UNAUTHORIZED");
        }

        ctx.params.userId = userId;

        return await this.adapter.insert(ctx.params);
      }
    },
    getAllErrorReports: {
      rest: "GET /allErrorReports",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {query: queryString = '{}', sort} = ctx.params;
          const query = {...JSON.parse(queryString), isDeleted: {$ne: true}};
          const queryTime = query.time ? this.extractQueryTime(query) : {};
          const params = this.constructParams(ctx.params, {...query, ...queryTime}, sort);
          return ctx.call('errorreports.list', params);
        } catch (error) {
          this.logger.error('Error in getAll handler:', error);
          throw error;
        }
      }
    }
  },
  methods: {
    constructParams(params, query, sort) {
      delete query.time;
      delete query.fromDate;
      delete query.toDate;
      return {
        ...this.extractParamsList(params),
        query: JSON.stringify(query),
        sort,
        populate: params.populate || [],
      };
    },
    async afterCreate(ctx, errorReport) {
      // Populate user information để có thể gửi email trong telegram notification
      const populatedErrorReport = await this.transformDocuments(ctx, {
        populate: this.settings.populateOptions
      }, errorReport);

      // Emit event để telegram service có thể lắng nghe và gửi notification
      ctx.emit("error-report.sent", {
        userId: populatedErrorReport.userId,
        imageIds: populatedErrorReport.imageIds,
        videoId: populatedErrorReport.videoId,
        description: populatedErrorReport.description,
        impactLevel: populatedErrorReport.impactLevel,
        errorUrl: populatedErrorReport.errorUrl,
        createdAt: populatedErrorReport.createdAt
      });

      return errorReport;
    },
  },
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
