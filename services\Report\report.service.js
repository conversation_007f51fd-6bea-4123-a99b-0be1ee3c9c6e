const FunctionCommonMixin = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const carbone = require('carbone');
const fs = require("fs");
const path = require("path");
const templatesDir = path.join(__dirname, "templates");
const storageDir = path.join(__dirname, "storage");
const {DocxImager} = require('./DocxImager');
const {DocxMerger} = require('./DocxMerger');
const {MoleculerClientError} = require("moleculer").Errors;
const mime = require('mime-types');
const {CONTENT_TYPE} = require("./report.constants");
const {getConfig} = require("../../config/config");
const i18next = require("i18next");
const config = getConfig(process.env.NODE_ENV);
const htmlToDocx = require('@turbodocx/html-to-docx');
const {INPUT_TYPE, OUTPUT_TYPE} = require("../../constants/constant");
const zip = require("adm-zip");
const {USER_CODES} = require("../../constants/constant");

const {
  Document,
  Packer,
  Paragraph,
  TextRun,
  Comment,
  CommentRangeStart,
  CommentRangeEnd,
  CommentReference,
  HeadingLevel,
  ImageRun,
  AlignmentType
} = require('docx');

const titleSection = {
  evaluations: {English: "Criteria Assessment", Vietnamese: "Đánh giá tiêu chí"},
  criteria: {English: "Criteria Assessment", Vietnamese: "Đánh giá tiêu chí"},
  ideas: {English: "Ideas suggestion", Vietnamese: "Gợi ý ý tưởng cho chủ đề này"},
  essayAssessment: {English: "Essay Assessment", Vietnamese: "Lập luận"},
  args: {English: "Essay Assessment", Vietnamese: "Lập luận"},
  improvedEssay: {English: "Improved Essay", Vietnamese: "Bài luận cải thiện"},
  sampleEssay: {English: "Sample Essay", Vietnamese: "Bài luận mẫu"},
  vocabularies: {English: "Vocabularies", Vietnamese: "Từ vựng"},
  grammarStructureSuggestion: {English: "Grammar Structure Suggestion", Vietnamese: "Gợi ý cấu trúc ngữ pháp"},
  overallBandScore: {English: "Overall band score", Vietnamese: "Tổng điểm"},
}


module.exports = {
  name: 'reports',
  mixins: [FileMixin, FunctionCommonMixin],
  settings: {},
  hooks: {
    before: {
      "createProjectFile": "checkProjectExist"
    },
    after: {
      "*": "activityLogger",
    }
  },
  actions: {

    createProjectFile: {
      rest: "POST /",
      path: "/createProjectFile",
      async handler(ctx) {
        try {
          const {projectId, contentIds, type, isPDF, docxTemplateId} = ctx.params;
          const [project, contents, templateFilePath] = await Promise.all([
            ctx.call('projects.get', {id: projectId}),
            ctx.call('contents.find', {
              query: {_id: {$in: contentIds}, isDeleted: false, isHidden: false},
              sort: 'contentIndex'
            }),
            this.broker.call("docxtemplates.templateFilePath", {id: docxTemplateId})
          ]);
          const responsePromises = contentIds.map(contentId => ctx.call('responses.find', {
            query: {isActivate: true, contentId},
            populate: []
          }));
          const responses = (await Promise.all(responsePromises)).flat();
          const inputs = await ctx.call('inputs.find', {query: {_id: this.extractKeyFromList(responses, "inputId")}});

          project.contents = this.handleContents(contents, responses, inputs);

          return this.createFileReport(ctx, project, templateFilePath, docxTemplateId, type, isPDF);
        } catch (e) {
          console.log(e);
        }
      }
    },

    createOneExam: {
      rest: "POST /",
      path: "/createOneExam",
      async handler(ctx) {
        try {
          const {projectId, responseIds, type, isPDF, docxTemplateId} = ctx.params;
          console.log("projectId, responseIds, type, isPDF, docxTemplateId", projectId, responseIds, type, isPDF, docxTemplateId);

          const [project, responses, templateFilePath] = await Promise.all([
            ctx.call('projects.get', {id: projectId}),
            ctx.call('responses.find', {query: {_id: {$in: responseIds}}, populate: []}),
            this.broker.call("docxtemplates.templateFilePath", {id: docxTemplateId})
          ]);

          const [contents, inputs] = await Promise.all([
            ctx.call('contents.find', {
              query: {_id: {$in: responses.map(response => response.contentId)}, isDeleted: false, isHidden: false},
              sort: 'contentIndex'
            }),
            ctx.call('inputs.find', {query: {_id: this.extractKeyFromList(responses, "inputId")}})
          ]);

          project.contents = this.handleContents(contents, responses, inputs);
          const dirPath = this.getDirPath(storageDir);
          const outFileName = this.getFilePath(`${this.getUniqueID()}_${i18next.t('exam_number')} ${responses[0]?.examOrder}.${isPDF ? 'pdf' : 'docx'}`, dirPath);
          return this.createFileReport(ctx, project, templateFilePath, docxTemplateId, type, isPDF, outFileName);
        } catch (e) {
          console.log(e);
        }
      }
    },

    createMultiExam: {
      rest: "POST /",
      path: "/createMultiExam",
      async handler(ctx) {
        try {
          const {projectId, data, type, isPDF, docxTemplateId} = ctx.params;
          const promises = data.map((item) => ctx.call('reports.createOneExam', {
            ...item,
            projectId,
            type,
            isPDF,
            docxTemplateId,
          }));

          const files = await Promise.all(promises);
          return this.createZipFile(files, this.getUniqueID() + '_report_exam.zip');
        } catch (e) {
          console.log(e);
        }
      }
    },

    createOneMarkTest: {
      rest: "POST /",
      path: "/createOneMarkTest",
      async handler(ctx) {
        try {
          const {projectId, responseId, type, isPDF, docxTemplateId} = ctx.params;

          const [project, responses, templateFilePath] = await Promise.all([
            ctx.call('projects.get', {id: projectId}),
            ctx.call('responses.get', {id: responseId}),
            this.broker.call("docxtemplates.templateFilePath", {id: docxTemplateId})
          ]);
          const [contents, inputs] = await Promise.all([
            ctx.call('contents.get', {id: responses.contentId.toString()}),
            ctx.call('inputs.get', {id: responses.inputId.toString()})
          ]);
          project.contents = this.handleContents([contents], [responses], [inputs]);

          const dirPath = this.getDirPath(storageDir);
          const outFileName = this.getFilePath(`${this.getUniqueID()}_${inputs?.inputData?.studentName || "evaluation essay"}.${isPDF ? 'pdf' : 'docx'}`, dirPath);
          const essayTopic = inputs.inputData.topic || contents.lastInput.inputData.topic
          const studentOutput = contents.input.inputData.text || responses.output.inputText
          const docxPath = await this.actions.createDocxWithComments({
            output: responses.output,
            studentOutput,
            essayTopic,
            inputType: contents.input.inputType,
            language: contents.input.inputData.language || "en",
          })
          await this.timeout(500)
          const finalFile = isPDF ? await this.convertDocxToPDF(docxPath, outFileName) : docxPath;
          return {fileName: path.basename(finalFile)};
        } catch (e) {
          console.log(e);
        }
      }
    },

    createMultiMarkTests: {
      rest: "POST /",
      path: "/createMultiMarkTests",
      async handler(ctx) {
        try {
          const {projectId, responseIds, type, isPDF, docxTemplateId} = ctx.params;
          const promises = responseIds.map((item) => ctx.call('reports.createOneMarkTest', {
            responseId: item,
            projectId,
            type,
            isPDF,
            docxTemplateId,
          }));

          const files = await Promise.all(promises);
          return this.createZipFile(files, this.getUniqueID() + '_report_mark_test.zip');

        } catch (e) {
          console.log(e);
        }
      }
    },
    createAcademicReport: {
      rest: "POST /",
      path: "/createAcademicReport",
      async handler(ctx) {
        try {
          const {projectId} = ctx.params;
          const [project, content] = await Promise.all([
            ctx.call("projects.get", {id: projectId}),
            ctx.call('contents.details', {projectId})
          ]);

          const responses = content[0]?.responses || [];
          const mapInput = responses.reduce((acc, {inputId, output}) => {
            if (inputId?._id) acc[inputId._id] = output.score;
            return acc;
          }, {});

          const results = content[0]?.inputs?.map((input, index) => ({
            stt: index + 1,
            studentName: input.inputData.studentName,
            score: mapInput[input._id] || null
          })) || [];

          const data = {topic: project?.commonOptions?.topic, results};
          const templateFilePath = this.getFilePath('academic_transcript.xlsx', templatesDir);
          const fileAfterGen = await this.generateDocument(ctx, data, templateFilePath);
          const buffer = fs.readFileSync(fileAfterGen);
          const localPath = this.getFilePath(`${this.getUniqueID()}_academic_transcript.xlsx`, this.getDirPath(storageDir));

          await this.saveFileToLocal(buffer, localPath);

          return {fileName: path.basename(localPath)};
        } catch (e) {
          console.error(e);
        }
      }
    },
    downloadProject: {
      rest: "GET /:fileName/download",
      activityLogger: true,
      async handler(ctx) {
        let {fileName, displayName} = ctx.params;
        const dirPath = this.getDirPath(storageDir);
        const filePath = this.getFilePath(fileName, dirPath);
        console.log("encodeURI(displayName)", encodeURI(displayName))
        try {
          const stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": mime.lookup(filePath),
            "Content-Length": stat.size,
            "Content-Disposition": 'attachment;filename=' + encodeURI(displayName)
          };

          return fs.createReadStream(filePath, {});
        } catch (err) {
          console.log("err", err);
          return new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },

    clearStorage: {
      rest: "DELETE /clearStorage",
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const dirPath = this.getDirPath(storageDir);
        fs.readdir(dirPath, (err, files) => {
          if (err) throw err;
          for (const file of files) {
            fs.unlink(path.join(dirPath, file), (err) => {
              if (err) throw err;
            });
          }
        });
      }
    },

    createDocxWithComments: {
      rest: "POST /createDocxWithComment",
      path: "/createDocxWithComment",
      async handler(ctx) {
        const {output, studentOutput, essayTopic, inputType, language} = ctx.params;
        return [INPUT_TYPE.MARK_TEST_TASK_1, INPUT_TYPE.STUDENT_TASK_1].includes(inputType)
          ? this.getFileDownloadTask1(output, essayTopic, studentOutput, language)
          : this.getFileDownloadTask2(output, essayTopic, studentOutput, language)

      }
    },

    createSpeakingReport: {
      rest: "POST /",
      path: "/createSpeakingReport",
      async handler(ctx) {
        try {
          const {responseId, isPDF} = ctx.params;

          const [response] = await Promise.all([
            ctx.call('responses.get', {id: responseId})
          ]);
          const {html} = response.output;

          const templateFilePath = this.getFilePath('speaking.docx', templatesDir);
          const qrImagePath = this.getFilePath(`qrcode_${response.output.fileId}.png`, this.getDirPath(storageDir));
          const urlQrCode = `${config.domain}/api/files/content/${response.output.fileId}`;
          await this.getQrCodeImage(urlQrCode, qrImagePath);
          response.output.accScore = Math.floor(response.output.overallPronunciationScore.accuracyScore) || 0;
          response.output.fluencyScore = Math.floor(response.output.overallPronunciationScore.fluencyScore) || 0;
          response.output.prosodyScore = Math.floor(response.output.overallPronunciationScore.prosodyScore) || 0;

          const speakPath = await this.generateDocumentWithImage(ctx, response.output, templateFilePath, qrImagePath, 2);
          const docxPath = await this.convertHtmlStringToDocx(html);

          const listFilesBinary = [speakPath, docxPath].map(file => fs.readFileSync(file, 'binary'));
          const mergeredDocxPath = await this.mergerDocumentDocx(listFilesBinary, false);
          const finalFile = isPDF ? await this.convertDocxToPDF(mergeredDocxPath) : mergeredDocxPath;
          return {fileName: path.basename(finalFile)};
        } catch (e) {
          console.log(e);
        }
      }
    },
  },
  events: {},
  methods: {
    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },

    async createZipFile(files, fileName) {

      const dirPath = this.getDirPath(storageDir);
      const zipPath = this.getFilePath(fileName, dirPath);
      const zipFile = new zip();
      files.forEach((file) => {
        const filePath = this.getFilePath(file.fileName, dirPath);
        if (fs.existsSync(filePath)) {
          const fileBuffer = fs.readFileSync(filePath);
          const [, newFileName] = file.fileName.split('_');
          zipFile.addFile(newFileName, fileBuffer);
        }
      });

      const fileStream = zipFile.toBuffer();
      await this.saveFileToLocal(fileStream, zipPath);
      return {fileName: path.basename(zipPath)};
    },

    async createFileReport(ctx, project, templateFilePath, docxTemplateId, type, isPDF, outFileName) {
      const generatePromises = [];

      if (fs.existsSync(templateFilePath)) {
        const {_id: userId} = ctx.meta.user;
        const customDetail = await ctx.call('customheader.getHeaderUserTemplate', {docxTemplateId, userId});

        let customHeader = customDetail?.customHeader;
        if (!customHeader) {
          const defaultHeader = await ctx.call('customheader.getDefaultHeader', {docxTemplateId});
          customHeader = defaultHeader?.customHeader;
          if (customHeader) {
            await ctx.call('customheader.makeHeaderUser', {docxTemplateId, customHeader});
          }
        }

        const {avatarId, imageId} = customHeader || {};
        if (imageId) {
          const imagePath = await ctx.call("files.filePath", {id: imageId.toString()});
          generatePromises.push(this.generateDocumentWithImage(ctx, customHeader, templateFilePath, imagePath, avatarId));
        } else {
          generatePromises.push(customHeader ? this.generateDocument(ctx, customHeader, templateFilePath) : templateFilePath);
        }
      }

      // Handle the content inputs and outputs
      for (const content of project.contents) {
        await this.handleContentInput(ctx, generatePromises, content);
        await this.handleContentOutput(ctx, generatePromises, content, type);
      }

      if (type === CONTENT_TYPE.ANSWERS_IN_BOTTOM) {
        const templateFilePath = this.getFilePath('correct_answers_page.docx', this.getDirPath(templatesDir));
        const contentWithAnswers = project.contents.filter(content => !!content.answers);
        generatePromises.push(this.generateDocument(ctx, contentWithAnswers, templateFilePath));
      }
      // Merge the generated files
      const listFilesAfterGenerate = await Promise.all(generatePromises);
      if (listFilesAfterGenerate.length === 0) {
        return {fileName: ''};
      }
      const listFilesBinary = listFilesAfterGenerate.map(file => fs.readFileSync(file, 'binary'));
      const fileAfterMerge = await this.mergerDocumentDocx(listFilesBinary, false, outFileName);


      const finalFile = isPDF ? await this.convertDocxToPDF(fileAfterMerge, outFileName) : fileAfterMerge;
      return {fileName: path.basename(finalFile)};
    },

    async activityLogger(context, response) {
      const {action, params} = context;
      const {workspaceId, projectId} = params || {};

      if (action.activityLogger && workspaceId) {
        const workspace = await context.call('workspaces.get', {id: workspaceId.toString(), isDeleted: false});

        if (workspace.type === "ORGANIZATIONAL") {
          context.emit("activities.logger", {action: "download", projectId,});
        }
      }


      return response;
    },

    async handleContentInput(ctx, generatePromises, content) {
      try {
        const {outputType} = content.response || {};
        if (outputType === "html_questions") {
          content.input.plaintext = "";
        }
        let imageLocalPath = this.getFilePath(`image_${content.input._id}.png`, this.getDirPath(storageDir));
        let qrImagePath = this.getFilePath(`qrcode_${content.input._id}.png`, this.getDirPath(storageDir));
        const dirPath = this.getDirPath('input', templatesDir);
        switch (content.input.inputType) {
          case INPUT_TYPE.NONE:
          case INPUT_TYPE.TEXT:
          case INPUT_TYPE.TTS:
            const templateFilePath = this.getFilePath('text.docx', dirPath);
            generatePromises.push(this.generateDocument(ctx, content, templateFilePath));
            break;
          case INPUT_TYPE.IMAGE:
            await this.handleImageCase(ctx, content, generatePromises, dirPath);
            break;
          case INPUT_TYPE.AUDIO:
            await this.handleAudioCase(ctx, content, generatePromises, dirPath, imageLocalPath);
            break;
          case INPUT_TYPE.VIDEO:
          case INPUT_TYPE.OFFLINE_VIDEO:
            await this.handleVideoCase(ctx, content, generatePromises, dirPath, imageLocalPath, qrImagePath);
            break;
          case INPUT_TYPE.MARK_TEST:
          case INPUT_TYPE.MARK_TEST_TASK_2:
          case INPUT_TYPE.STUDENT_TASK_2:
            console.log("content.input.inputData", content.input.inputData)
            if (content.input.inputData.imageId || content.input.inputData.topicImageId) {
              await this.handleImageCase(ctx, content, generatePromises, dirPath);
              break;
            }
            generatePromises.push(this.generateDocument(ctx, content, this.getFilePath('text.docx', dirPath)));
            break
          case INPUT_TYPE.MARK_TEST_IMAGE:
          case INPUT_TYPE.MARK_TEST_TASK_1:
          case INPUT_TYPE.STUDENT_TASK_1:
            console.log("content.input.inputData", content.input.inputData)
            if (content.input.inputData.imageId || content.input.inputData.topicImageId) {
              await this.handleImageCase(ctx, content, generatePromises, dirPath);
              break;
            }
            generatePromises.push(this.generateDocument(ctx, content, this.getFilePath('text.docx', dirPath)));
            break
          default:
            generatePromises.push(this.generateDocument(ctx, content, this.getFilePath('text.docx', dirPath)));
            break;
        }
        return generatePromises;
      } catch (e) {
        console.error(e);
      }
    },

    async handleImageCase(ctx, content, generatePromises, dirPath) {
      try {
        const templateFilePath = this.getFilePath('image.docx', dirPath);
        const {imageId, topicImageId} = content.input.inputData;
        const images = await ctx.call("images.get", {id: imageId || topicImageId});
        const imagePath = await ctx.call("files.filePath", {id: images.imageFileId});
        const replaceImageId = images.height > images.width ? 1 : 2;
        generatePromises.push(this.generateDocumentWithImage(ctx, content, templateFilePath, imagePath, replaceImageId));
      } catch (e) {
        console.log(e);
      }
    },

    async handleAudioCase(ctx, content, generatePromises, dirPath) {
      const templateFilePath = this.getFilePath('media.docx', dirPath);
      const qrImagePath = this.getFilePath(`qrcode_${content.input?._id}.png`, this.getDirPath(storageDir));
      // const urlQrCode = `${config.domain}/api/files/${content.input.inputData.audioId}/stream-media`;
      const urlQrCode = `${config.domain}/api/files/content/${content.input.inputData.audioId}`;
      await this.getQrCodeImage(urlQrCode, qrImagePath);
      generatePromises.push(this.generateDocumentWithImage(ctx, content, templateFilePath, qrImagePath, 2));
    },

    async handleVideoCase(ctx, content, generatePromises, dirPath, imageLocalPath, qrImagePath) {
      const templateFilePath = this.getFilePath('media.docx', dirPath);

      const {url, offlineVideoId} = content.input.inputData;
      if (url) {
        const videoDetails = await ctx.call("videos.videoDetail", {url});
        let thumbnailUrl = videoDetails.thumbnails.pop().url;
        const extension = path.extname(thumbnailUrl);
        if (![".jpg", ".jpeg", ".png"].includes(extension)) {
          thumbnailUrl = videoDetails.thumbnails[0].url.split('?')[0];
        }
        await this.saveImageUrlToLocal(thumbnailUrl, imageLocalPath);
        await this.getQrCodeImage(url, qrImagePath);
      } else {
        const offlineVideos = await ctx.call("offlinevideos.get", {id: offlineVideoId});
        imageLocalPath = await ctx.call("files.filePath", {id: offlineVideos.thumbnailFileId});
        // const urlQrCode = `${config.domain}/api/files/${offlineVideos.videoFileId}/stream-media`;
        const urlQrCode = `${config.domain}/api/files/content/${offlineVideos.videoFileId}`;
        await this.getQrCodeImage(urlQrCode, qrImagePath);
      }
      generatePromises.push(this.generateDocumentWithInputMedia(ctx, content, templateFilePath, imageLocalPath, qrImagePath, 1));
    },
    async handleContentOutput(ctx, generatePromises, content, type) {
      try {
        const showAll = type === CONTENT_TYPE.ALL;
        const dirPath = this.getDirPath('response', templatesDir);
        let templateFileName;
        switch (content.response.outputType) {
          case OUTPUT_TYPE.HTML:
            return generatePromises.push(this.convertHtmlStringToDocx(content.response.output.html));
          case OUTPUT_TYPE.HTML_QUESTIONS:
            const {questionsHtml, answersHtml} = content.response.output;
            const html = showAll && answersHtml ?
              `${questionsHtml} <p>${i18next.t("correct_answers")}</p> ${answersHtml}` :
              questionsHtml;
            return generatePromises.push(await this.convertHtmlStringToDocx(html));
          case OUTPUT_TYPE.MARK_TEST_WRITING:
          case OUTPUT_TYPE.MARK_TEST_IELTS_WRITING:
            const {evaluation, score} = content.response.output;
            const evaluationHtml = `${evaluation} <h2>Score: ${score}</h2> `;
            return generatePromises.push(await this.convertHtmlStringToDocx(evaluationHtml));
          case OUTPUT_TYPE.TEXT:
          case OUTPUT_TYPE.IMAGE:
            templateFileName = "text.docx";
            break;
          case OUTPUT_TYPE.MULTI_CHOICE:
            templateFileName = "multi_choice.docx";
            break;
          case OUTPUT_TYPE.TF_QUESTION:
            templateFileName = "tf_question.docx";
            break;
          case OUTPUT_TYPE.AUDIO:
            templateFileName = "media.docx";
            break;
          case OUTPUT_TYPE.OPEN_QUESTION:
          case OUTPUT_TYPE.OPTIONS:
            templateFileName = "options.docx";
            break;
          default:
            templateFileName = "text.docx";
        }
        if (content.response.outputType === OUTPUT_TYPE.AUDIO) {
          const qrImagePath = this.getFilePath(`qrcode_${content.response?._id}.png`, this.getDirPath(storageDir));
          const templateFilePath = this.getFilePath(templateFileName, dirPath);
          const urlQrCode = `${config.domain}/api/files/content/${content.response.output.audio.audioFileId}`;
          await this.getQrCodeImage(urlQrCode, qrImagePath);
          return generatePromises.push(this.generateDocumentWithInputMedia(ctx, content, templateFilePath, null, qrImagePath, 1));
        }
        if (showAll && [OUTPUT_TYPE.MULTI_CHOICE, OUTPUT_TYPE.TF_QUESTION, OUTPUT_TYPE.OPEN_QUESTION].includes(content.response.outputType)) {
          templateFileName = this.appendFileName(templateFileName, "suffix", "_with_answers");
        }
        const templateFilePath = this.getFilePath(templateFileName, dirPath);
        return generatePromises.push(this.generateDocument(ctx, content, templateFilePath));
      } catch (e) {
        console.error(e)
      }
    },

    handleContents(contents, responses, inputs) {
      const responseGroupByContent = this.groupBy(responses, 'contentId');
      const inputGroupByContent = this.groupBy(inputs, 'contentId');

      contents.forEach(content => {
        content.input = inputGroupByContent[content._id] ? inputGroupByContent[content._id][0] : null;
        content.urlQrCode = inputGroupByContent[content._id] && inputGroupByContent[content._id][0] ? inputGroupByContent[content._id][0].inputData?.url : config.domain;
        content.response = responseGroupByContent[content._id] ? responseGroupByContent[content._id][0] : null;
        content.answers = this.getAnswers(content.response);
        content.correctText = "Correct answers";
      });
      contents = contents.filter(content => !!content.response);

      // return contents.length > 1 ? this.addStringIndex(contents) : contents;
      return contents.length > 1 ? this.addRomanIndex(contents) : contents;
    },

    async addImageToDocx(resultFilePath, imageFilePath, imageId) {
      let docxImager = new DocxImager();
      await docxImager.load(resultFilePath);
      await docxImager.replaceWithLocalImage(imageFilePath, imageId, 'png');
      await docxImager.save(resultFilePath);
    },

    async generateDocument(ctx, data, templateFilePath, convertToPdf = false) {
      const options = {
        renderPrefix: 'report',
        reportName: "Report",
        timezone: 'Asia/Saigon',
        ...(convertToPdf && {convertTo: 'pdf'})
      };
      return new Promise((resolve, reject) => {
        carbone.render(templateFilePath, data, options, (err, result) => {
          if (err) reject(err);
          resolve(result);
        });
      });
    },

    async convertDocxToPDF(inputFilePath, outPDFPath) {
      const fileBuffer = fs.readFileSync(inputFilePath);
      const options = {
        convertTo: 'pdf',
        extension: 'docx',
      };
      const dirPath = this.getDirPath(storageDir);
      if (!outPDFPath) {
        outPDFPath = this.getFilePath(`report_${this.getUniqueID()}.pdf`, dirPath);
      }
      return new Promise((resolve, reject) => {
        carbone.convert(fileBuffer, options, function (err, result) {
          if (err) return reject(err);
          fs.writeFileSync(outPDFPath, result);
          resolve(outPDFPath);
        });
      });
    },

    async generateDocumentWithImage(ctx, data, templateFilePath, imagePath, replaceImageId) {
      console.log("data", data)
      console.log(templateFilePath, imagePath)
      const fileAfterGen = await this.generateDocument(ctx, data, templateFilePath);
      await this.addImageToDocx(fileAfterGen, imagePath, replaceImageId);
      console.log("fileAfterGen", fileAfterGen)
      return fileAfterGen;
    },
    async generateDocumentWithInputMedia(ctx, data, templateFilePath, thumbnailPath, qrCodePath, replaceImageId) {
      const fileAfterGen = await this.generateDocument(ctx, data, templateFilePath);
      await this.addImageToDocx(fileAfterGen, thumbnailPath, replaceImageId);
      await this.addImageToDocx(fileAfterGen, qrCodePath, replaceImageId + 1);
      return fileAfterGen;
    },

    async mergerDocumentDocx(listFiles, pageBreak = true, mergeredDocxPath) {
      if (!mergeredDocxPath) {
        const dirPath = this.getDirPath(storageDir);
        mergeredDocxPath = this.getFilePath(`report_${this.getUniqueID()}.docx`, dirPath);
      }

      const mergeredDocx = new DocxMerger();
      await mergeredDocx.initialize({pageBreak, fonts: 'Cambria', fontSize: '24'}, listFiles);
      const data = await mergeredDocx.save('nodebuffer');
      fs.writeFileSync(mergeredDocxPath, data);
      return mergeredDocxPath;
    },

    getAnswers(response) {
      const {output, outputType} = response || {};
      const {correctAnswers, answersHtml} = output || {};
      switch (outputType) {
        case OUTPUT_TYPE.MULTI_CHOICE:
          return this.transformMultiChoiceAnswer(correctAnswers);
        case OUTPUT_TYPE.TF_QUESTION:
          return this.transformTrueFalseAnswer(correctAnswers);
        case OUTPUT_TYPE.OPEN_QUESTION:
          return this.transformOpenQuestionAnswer(correctAnswers);
        case OUTPUT_TYPE.HTML_QUESTIONS:
          return this.transformHtmlQuestionAnswer(correctAnswers, answersHtml);
        default:
          return "";
      }
    },
    transformMultiChoiceAnswer(correctAnswers) {
      const answers = correctAnswers.map((item, index) => `${index + 1}. ${item.correctAnswer}`);
      const resString = answers.map((answer, index) => (index % 10 === 9 ? `${answer}\n` : `${answer}   `)).join('');

      return [{answer: resString}];
    },
    transformHtmlQuestionAnswer(correctAnswers, answersHtml) {
      return [
        {answer: this.convertHTMLToText(answersHtml).trim()}
      ];
    },

    transformOpenQuestionAnswer(correctAnswers) {
      return correctAnswers?.map((item, index) => {
        return {
          idx: `${index + 1}.  `,
          answer: item.correctAnswer
        };
      });
    },
    transformTrueFalseAnswer(correctAnswers) {
      return correctAnswers?.map((item, index) => {
        return {
          idx: `${index + 1}.  `,
          answer: item.correctAnswer.toUpperCase() === "TRUE" ? item.correctAnswer : `${item.correctAnswer} -- ${item.answerExplain}`
        };
      });
    },
    async checkProjectExist(context) {
      const {params} = context;
      const project = await context.call('projects.get', {id: params.projectId});
      if (project.isDeleted) {
        throw new MoleculerClientError(i18next.t("project_was_deleted"), 404);
      }
    },
    async convertHtmlStringToDocx(htmlString) {
      htmlString = htmlString = htmlString.replace(/<figure[^>]*>[\s\S]*?<\/figure>/g, figureTag => {
        return figureTag.replace(/<img[^>]*>/g, imgTag => {
          const heightMatch = imgTag.match(/height="(\d+)"/);
          const widthMatch = imgTag.match(/width="(\d+)"/);

          if (heightMatch && widthMatch) {
            const height = heightMatch[1];
            const width = widthMatch[1];

            return imgTag
              .replace(/style="[^"]+"/, `style="aspect-ratio:${width}/${height}; height:${height}px; width:${width}px"`)
              .replace(/height="\d+"/, '')
              .replace(/width="\d+"/, '');
          }

          return imgTag;
        });
      }).replace(/<table[^>]*>[\s\S]*?<\/table>/g, table => {
        return table
          .replace(/<th>/g, '<th style="border: 1px solid black;"><b>')
          .replace(/<\/th>/g, '<\/b><\/th>')
          .replace(/<td>/g, '<td style="border: 1px solid black;">');
      });

      const options = {
        font: "Cambria",
        fontSize: "12pt",
        pageSize: {
          width: "8.27in",
          height: "11.69in",
        },
        orientation: 'portrait',
      };

      try {
        const fileName = `report_${this.getUniqueID()}.docx`;
        const filePath = this.getFilePath(fileName, storageDir);

        const fileBuffer = await htmlToDocx(htmlString, null, options);
        fs.writeFileSync(filePath, fileBuffer);

        return filePath;
      } catch (error) {
        console.error('Error creating DOCX file:', error);
      }
    },

    transferComments(comments) {
      const currentDate = new Date();
      const adjustedDate = new Date(currentDate.setHours(currentDate.getHours() + 7));
      return comments.map((comment, idx) => ({
        id: idx,
        author: "Clickee",
        date: adjustedDate,
        children: [
          this.createParagraph(`${comment.text} -> ${comment.suggest}`, 22),
          this.createParagraph(`Explanation: ${comment.explanation}`, 22),
        ],
      }));
    },

    createCommentsSection(comments, studentOutput) {
      const prettyOutput = studentOutput?.replace(/(\n\s*)+/g, '\n\n').trim();
      const keywords = comments.map(comment => comment.text);
      return prettyOutput?.split('\n\n').map(text => {
        const splitParagraphs = this.splitParagraphsByKeywords(text, keywords);
        return (splitParagraphs.length === 1 && !keywords.includes(splitParagraphs[0]))
          ? new Paragraph({children: [new TextRun({text, size: 24})], spacing: {after: 240}})
          : this.renderSectionsWithComments(splitParagraphs, comments);
      });
    },

    createCriteriaAssessmentSection(heading, evaluations) {
      const [score1, score2, score3, score4] = evaluations.map(e => e?.score);
      const overallScore = this.calculateIELTSOverall(score1, score2, score3, score4);

      const evaluationParagraphs = evaluations.flatMap(({category, score, explanation, improve}) => [
        this.createParagraph(category, 24, true, true, 0),
        this.createParagraphTwoChild("Band Score", score, 24, true, 1, 120),
        this.createParagraphTwoChild("Explanation", explanation, 24, true, 1, 120),
        this.createParagraphTwoChild("How to improve", improve, 24, true, 1),
      ]);

      return [
        this.createHeadingParagraph(heading, HeadingLevel.HEADING_2, 28),
        ...evaluationParagraphs,
        this.createParagraphTwoChild("Overall Score", overallScore, 24, true, 0, 240),
      ];
    },

    createEssayAssessmentSection(heading, essayAssessment = []) {
      const createSection = (title, items) => [
        this.createParagraph(title, 24, true, true, 0),
        ...items?.map(item => this.createParagraphTwoChild(item.category, item.feedback, 24, true, 1, 120)),
        new Paragraph({spacing: {after: 120}})
      ];

      const sections = [this.createHeadingParagraph(heading, HeadingLevel.HEADING_2, 28)];

      const introduction = essayAssessment?.introduction ? createSection("Introduction", essayAssessment?.introduction) : [];
      const overview = essayAssessment?.overview ? createSection("Overview", essayAssessment?.overview) : [];
      const mainPoint1 = essayAssessment?.mainPoint1 ? createSection("Main Point 1", essayAssessment?.mainPoint1) : [];
      const mainPoint2 = essayAssessment?.mainPoint2 ? createSection("Main Point 2", essayAssessment?.mainPoint2) : [];
      const overallConsiderations = essayAssessment?.overallConsiderations ? createSection("Overall Considerations", essayAssessment?.overallConsiderations) : [];

      return [...sections, ...introduction, ...overview, ...mainPoint1, ...mainPoint2, ...overallConsiderations];
    },

    createImprovedSection(improvedEssay, headingTitle) {
      const prettyOutput = improvedEssay.replace(/(\n\s*)+/g, '\n\n').trim();

      const sections = prettyOutput.split('\n\n').map(text => {
        return this.createParagraph(text)
      });

      return [
        this.createHeadingParagraph(headingTitle, HeadingLevel.HEADING_2, 28),
        ...sections
      ];

    },

    createVocabularySection(vocabularyList, headingTitle) {
      return [
        this.createHeadingParagraph(headingTitle, HeadingLevel.HEADING_2, 28),
        ...vocabularyList.map(item => {
          const text2 = `(${item.translation}) - ${item.description} - Example: ${item.example}`;
          return this.createParagraphTwoChild(item.word, text2, 24, true, 0, 120)
        })
      ]
    },

    renderSectionsWithComments(splitParagraphs, comments) {
      comments = comments.map((comment, idx) => ({...comment, id: idx}));

      const children = splitParagraphs.map(text => {
        const comment = comments.find(comment => comment.text.toLowerCase() === text.toLowerCase());
        if (!comment) {
          return new TextRun({text, size: 24});
        }

        return [
          new CommentRangeStart(comment.id),
          new TextRun({text, highlight: "cyan", size: 24}),
          new CommentRangeEnd(comment.id),
          new TextRun({children: [new CommentReference(comment.id)], bold: true, size: 24}),
        ];
      }).flat();

      return new Paragraph({children, spacing: {after: 240}});
    },

    createHeadingParagraph(headingTitle, heading, textSize) {
      return new Paragraph({
        children: [
          new TextRun({text: headingTitle, color: "000000", size: textSize, bold: true})
        ],
        heading,
        spacing: {after: 240},
      })
    },

    createParagraph(text, size = 24, bold = false, isBullet = false, level = 0, afterSpacing = 240) {
      const bullet = isBullet ? {level} : undefined;
      return new Paragraph({
        children: [
          new TextRun({text, size, bold})
        ],
        bullet, spacing: {after: afterSpacing}
      });
    },

    createImageParagraph(imageBase64Data, width = 300, height = 300, alignment = AlignmentType.CENTER) {
      return new Paragraph({
        children: [
          new ImageRun({
            data: Buffer.from(imageBase64Data, "base64"),
            transformation: {width, height}
          })
        ],
        alignment,
        spacing: {after: 240}
      });
    },

    createParagraphTwoChild(firstText, secondText, size = 24, isBullet = false, level = 0, afterSpacing = 240) {
      const bullet = isBullet ? {level} : undefined;
      return new Paragraph({
        children: [
          new TextRun({text: firstText, size, bold: true}),
          new TextRun({text: `: ${secondText}`, size, bold: false})
        ],
        bullet,
        spacing: {after: afterSpacing},
      });
    },

    async getFileDownloadTask1(output, essayTopic, studentOutput, language) {
      const {
        suggests: comments, evaluations, essayAssessment, sampleEssay, overallBandScore,
        vocabularies, topicImageBase64, improvedEssay, grammarStructureSuggestion
      } = output;
      const isNotMarkdown = Array.isArray(evaluations);
      const headingPara = [
        this.createHeadingParagraph(essayTopic, HeadingLevel.HEADING_3, 24),
        topicImageBase64 && this.createImageParagraph(topicImageBase64, 500)
      ].filter(Boolean);

      const children = [
        headingPara,
        this.createCommentsSection(comments, studentOutput),
        ...(isNotMarkdown ? [
          evaluations && this.createCriteriaAssessmentSection(titleSection.evaluations[language], evaluations),
          essayAssessment && this.createEssayAssessmentSection(titleSection.essayAssessment[language], essayAssessment),
          this.createImprovedSection(improvedEssay, titleSection.improvedEssay[language]),
          this.createImprovedSection(sampleEssay, titleSection.sampleEssay[language]),
          vocabularies && this.createVocabularySection(vocabularies, titleSection.vocabularies[language]),
        ] : [])
      ].flat();

      const doc = new Document({
        comments: {children: this.transferComments(comments)},
        sections: [{properties: {}, children}]
      });

      const fileName = `${this.getUniqueID()}.docx`;
      const docxPath = this.getFilePath(fileName, this.getDirPath(storageDir));

      try {
        const buffer = await Packer.toBuffer(doc);
        fs.writeFileSync(docxPath, buffer);
      } catch (error) {
        console.error('Failed to write DOCX file:', error);
        throw error; // Propagate the error to handle it upstream
      }


      if (isNotMarkdown) return docxPath;
      const evaluationHtml = `
      <div>
        ${evaluations ?
        `<h2>${titleSection.evaluations[language]}</h2>
          <h3>${titleSection.overallBandScore[language]}: ${this.calculateIELTSOverall(...overallBandScore.map(e => e?.score))}</h3>
            ${evaluations}`
        : ""}
        ${essayAssessment ? `<h2>${titleSection.essayAssessment[language]}</h2>${essayAssessment}` : ""}
        ${improvedEssay ? `<h2>${titleSection.improvedEssay[language]}</h2>${improvedEssay}` : ""}
        ${sampleEssay ? `<h2>${titleSection.sampleEssay[language]}</h2>${sampleEssay}` : ""}
        ${vocabularies ? `<h2>${titleSection.vocabularies[language]}</h2>${vocabularies}` : ""}
        ${grammarStructureSuggestion ? `<h2>${titleSection.grammarStructureSuggestion[language]}</h2>${grammarStructureSuggestion}` : ""}
      </div>
    `;

      const htmlPath = await this.convertHtmlStringToDocx(evaluationHtml);

      const listFilesBinary = [docxPath, htmlPath].map(file => fs.readFileSync(file, 'binary'));
      return this.mergerDocumentDocx(listFilesBinary, false);
    },

    async getFileDownloadTask2(output, essayTopic, studentOutput, language) {
      const {
        suggests: comments, vocabularies, topicImageBase64, sampleEssay, overallBandScore,
        improvedEssay, arguments: args, criteria, ideas, grammarStructureSuggestion
      } = output;
      const isNotMarkdown = Array.isArray(criteria);

      const headingPara = [
        this.createHeadingParagraph(essayTopic, HeadingLevel.HEADING_3, 24),
        topicImageBase64 && this.createImageParagraph(topicImageBase64, 500)
      ].filter(Boolean);
      console.log("language", language)
      const children = [
        headingPara,
        this.createCommentsSection(comments, studentOutput),
        ...(isNotMarkdown ? [
          this.createTask2Criteria(criteria, titleSection.criteria[language]),
          this.createTask2Arguments(args, titleSection.args[language]),
          this.createImprovedSection(improvedEssay, titleSection.improvedEssay[language]),
          this.createImprovedSection(sampleEssay, titleSection.sampleEssay[language]),
          vocabularies && this.createVocabularySection(vocabularies, titleSection.vocabularies[language]),
          this.createIdeasSuggest(ideas, titleSection.ideas[language]),
        ] : [])
      ].flat();


      const doc = new Document({
        comments: {children: this.transferComments(comments)},
        sections: [{properties: {}, children}]
      });

      const fileName = `${this.getUniqueID()}.docx`;
      const docxPath = this.getFilePath(fileName, this.getDirPath(storageDir));
      console.log("docxPath", docxPath)
      try {
        const buffer = await Packer.toBuffer(doc);
        fs.writeFileSync(docxPath, buffer);
      } catch (error) {
        console.error('Failed to write DOCX file:', error);
        throw error; // Propagate the error to handle it upstream
      }

      if (isNotMarkdown) return docxPath;

      const evaluationHtml = `
      <div>
        ${criteria ? `<h2>${titleSection.criteria[language]}</h2>
            <h3>${titleSection.overallBandScore[language]}: ${this.calculateIELTSOverall(...overallBandScore.map(e => e?.score))}</h3>
            ${criteria}` : ""}
        ${args ? `<h2>${titleSection.args[language]}</h2>${args}` : ""}
        ${improvedEssay ? `<h2>${titleSection.improvedEssay[language]}</h2>${improvedEssay}` : ""}
        ${sampleEssay ? `<h2>${titleSection.sampleEssay[language]}</h2>${sampleEssay}` : ""}
        ${vocabularies ? `<h2>${titleSection.vocabularies[language]}</h2>${vocabularies}` : ""}
        ${ideas ? `<h2>${titleSection.ideas[language]}</h2>${ideas}` : ""}
        ${grammarStructureSuggestion ? `<h2>${titleSection.grammarStructureSuggestion[language]}</h2>${grammarStructureSuggestion}` : ""}
      </div>
    `;

      const htmlPath = await this.convertHtmlStringToDocx(evaluationHtml);
      console.log("htmlPath", htmlPath)
      const listFilesBinary = [docxPath, htmlPath].map(file => fs.readFileSync(file, 'binary'));
      return this.mergerDocumentDocx(listFilesBinary, false);
    },

    createTask2Arguments(args, headingTitle) {
      const createSection = (items) => [
        ...items?.map(item => this.createParagraphTwoChild(item.category, item.feedback, 24, true, 1, 120)),
        new Paragraph({spacing: {after: 120}})
      ];

      const createArgumentSection = (title, content) => content ? [
        this.createHeadingParagraph(title, HeadingLevel.HEADING_3, 24),
        this.createParagraph(`${content.original}`, 24, false, true, 0),
        ...createSection(content.feedback),
        this.createParagraphTwoChild(`Improved ${title}`, `${content.improved}`, 24, true, 0)
      ] : [];

      const sections = [this.createHeadingParagraph(headingTitle, HeadingLevel.HEADING_2, 28)];
      const introduction = createArgumentSection("Introduction", args.introduction);
      const mainPoint1 = createArgumentSection("Main point 1", args.mainPoint1);
      const mainPoint2 = createArgumentSection("Main point 2", args.mainPoint2);
      const conclusion = createArgumentSection("Conclusion", args.conclusion);

      return [...sections, ...introduction, ...mainPoint1, ...mainPoint2, ...conclusion];
    },

    createTask2Criteria(criteria, headingTitle) {
      const sections = this.createHeadingParagraph(headingTitle, HeadingLevel.HEADING_2, 28);
      const scores = criteria.map(e => e?.bandScore);
      const overallScore = this.calculateIELTSOverall(...scores);

      const createSection = (items) => {
        const paragraphs = items?.flatMap(item => [
          this.createParagraphTwoChild(item.category, item.bandScore, 24, false, 0, 120),
          ...item.feedback.detailedPoints.flatMap(feed => [
            new Paragraph({
              children: [new TextRun({text: `${feed.aspect}:`, size: 24, bold: true})],
              bullet: {level: 0},
              spacing: {after: 120}
            }),
            new Paragraph({
              children: [
                new TextRun({text: "Detailed Explanation: ", size: 24, bold: true}),
                new TextRun({text: feed.detailedExplanation, size: 24})
              ],
              bullet: {level: 1},
              spacing: {after: 60}
            }),
            new Paragraph({
              children: [
                new TextRun({text: "How To Improve: ", size: 24, bold: true}),
                new TextRun({text: feed.howToImprove, size: 24})
              ],
              bullet: {level: 1},
              spacing: {after: 60}
            })
          ]),
          this.createParagraphTwoChild("Overall Comment", item.feedback.overallComment, 24, true, 0, 120)
        ]) || [];

        paragraphs.push(new Paragraph({spacing: {after: 120}}));
        return paragraphs;
      };

      return [
        sections,
        ...createSection(criteria),
        this.createParagraphTwoChild("Overall Score", overallScore, 24, false, 0, 240),
      ];
    },
    createIdeasSuggest(ideas, headingTitle) {
      return [
        this.createHeadingParagraph(headingTitle, HeadingLevel.HEADING_2, 28),
        ...ideas.map(idea => this.createParagraph(idea, 24, false, true, 0, 120))
      ]
    },
  },


  async started() {
    this.createFolderIfNotExist(storageDir);
  },
};
