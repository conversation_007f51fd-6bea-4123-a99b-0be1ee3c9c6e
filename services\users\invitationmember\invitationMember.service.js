const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./invitationMember.model");
const BaseService = require("../../../mixins/baseService.mixin");
const jwt = require("../../../helpers/jwt");
const {sendEmail, generateChangePasswordEmail} = require("../../../helpers/emailHelper");
const {getConfig} = require("../../../config/config");
const config = getConfig(process.env.NODE_ENV);
const {
  createInvitationEmail,
  createInvitationNewUser
} = require("../emailtemplate/emailtemplate");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: "invitations",
  settings: {
    JWT_SECRET: process.env.JWT_SECRET || "jwt-tradar-secret",
  },
  mixins: [DbMongoose(Model), BaseService, AuthRole],

  hooks: {
    before: {
      "*": "checkPermission",
    },
  },

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.CONTRIBUTOR
    },
    create: {
      rest: {
        method: "POST",
        path: "/",
      },
      permission: "admin",
      role: USER_CODES.ORG_ADMIN,
      async handler(ctx) {
        const {email, organizationId} = ctx.params;
        const users = await ctx.call("users.find", {query: {email}, populate: []});
        let memberObj = {
          email,
          organizationId,
          status: "pending",
          role: "normal",
        };
        const member = await this.adapter.findOne({email, organizationId});

        if (users.length > 0) {
          const user = users[0];

          if (user.organizationId?.toString() === organizationId.toString()) {
            throw new MoleculerClientError(i18next.t("account_already_belongs_to_organization"));
          }

          const confirmInvitationLink = this.createConfirmInvitationLink(user._id, "30d", organizationId);
          const rejectInvitationLink = this.creatRejectInvitationLink(user._id, "30d", organizationId);
          this.sendEmailInvitationMember(user, organizationId, confirmInvitationLink, rejectInvitationLink);
          if (member) {
            return await this.adapter.updateById(member._id, {...memberObj, fullName: user.fullName});
          }
          return await this.adapter.insert({...memberObj, fullName: user.fullName});
        }

        const confirmInvitationLink = this.createConfirmRegisterLink(email, organizationId);
        this.sendEmailInvitationNewAccount(email, organizationId, confirmInvitationLink);
        if (member) {
          return await this.adapter.updateById(member._id, {...memberObj});
        }
        return await this.adapter.insert({...memberObj, fullName: "New Account"});
      }
    },

    resendInvitation: {
      rest: "POST /resend",
      permission: "admin",
      role: USER_CODES.ORG_ADMIN,
      async handler(ctx) {
        const {email, organizationId} = ctx.params;
        const users = await ctx.call("users.find", {query: {email, isDeleted: false}});
        const member = await this.adapter.findOne({email, organizationId});
        if (users.length > 0) {
          const user = users[0];
          const confirmInvitationLink = this.createConfirmInvitationLink(user._id, "30d", organizationId);
          const rejectInvitationLink = this.creatRejectInvitationLink(user._id, "30d", organizationId);
          this.sendEmailInvitationMember(user, organizationId, confirmInvitationLink, rejectInvitationLink);

          return await this.adapter.updateById(member._id, {status: "pending"});
        }
        const confirmInvitationLink = this.createConfirmRegisterLink(email, organizationId);
        this.sendEmailInvitationNewAccount(email, organizationId, confirmInvitationLink);
        return await this.adapter.updateById(member._id, {status: "pending"});
      }
    },
    update: {
      rest: {
        method: "PUT",
        path: "/:id",
      },
      permission: "admin",
      role: USER_CODES.ORG_ADMIN,
      async handler(context) {
        const {id, role} = context.params;
        const member = await this.adapter.findById(id);
        if (role && member?.status === "accepted") {
          await context.emit("userRoleUpdate", {email: member.email, role});
        }
        return this.adapter.updateById(id, context.params);
      },
    },
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      permission: "admin",
      role: USER_CODES.ORG_ADMIN,
      async handler(ctx) {
        const {id} = ctx.params;
        const member = await this.adapter.findById(id);
        const dataRes = await this.adapter.removeById(id);
        await ctx.emit('memberRemoved', {member});
        return dataRes;
      },
    }
  },

  events: {

    userConfirmedRegister: {
      params: {
        user: "object",
      },
      async handler(context) {
        const {user} = context.params;
        const member = await this.adapter.findOne({email: user.email, organizationId: user?.organizationId});
        this.broker.emit("userJoinedOrganization", {user, organizationId: member.organizationId});
        return this.adapter.updateById(member._id, {status: "accepted", fullName: user.fullName});
      }
    },

    userConfirmInvitation: {
      params: {
        user: "object",
      },
      async handler(context) {
        const {user, organizationId} = context.params;
        const member = await this.adapter.findOne({email: user.email, organizationId});
        this.broker.emit("userJoinedOrganization", {user, organizationId});
        await this.adapter.updateMany({
          email: user.email,
          organizationId: {$ne: organizationId}
        }, {$set: {isDeleted: true}});

        await context.emit("userRoleUpdate", {email: member.email, role: member.role});

        return this.adapter.updateById(member._id, {status: "accepted"});
      },
    },
    userRejectInvitation: {
      params: {
        user: "object",
      },
      async handler(context) {
        const {user, organizationId} = context.params;
        const member = await this.adapter.findOne({email: user.email, organizationId});
        return this.adapter.updateById(member._id, {status: "rejected"});
      },
    },
    async "organization.removed"(payload, sender, event) {
      const members = await this.adapter.find({query: {organizationId: payload?.id}});

      await Promise.all(members.map(member => {
        return this.adapter.removeById(member._id)
      }));
    },
  },
  methods: {

    async checkPermission(context) {
      const {action, params, meta} = context;
      const {user} = meta;
      if (user?.isSystemAdmin) return;

      let organizationId = params?.organizationId;
      if(params?.id) {
        const member = await this.adapter.findById(params.id);
        organizationId = member.organizationId;
      }

      if (action?.permission) {
        if (user?.organizationId?.toString() !== organizationId?.toString()) {
          throw new MoleculerClientError(i18next.t("you_need_to_be_an_admin"), 403, "FORBIDDEN");
        }

        const organization = await context.call("organizations.getOne", {id: organizationId});
        if (!organization) {
          throw new MoleculerClientError(i18next.t("error_organization_not_found"), 404);
        }

        if (organization?.active === false) {
          throw new MoleculerClientError(i18next.t("organization_already_locked"), 423);
        }
      }
    },

    createConfirmRegisterLink(email, organizationId) {
      return config.domain + "/confirm-register?email=" + email + "&organizationId=" + organizationId;
    },

    createActivateAccountLink(userId, expiresIn) {
      const accessToken = jwt.issue({id: userId}, expiresIn, this.settings.JWT_SECRET);
      return config.domain + "/account?accessToken=" + accessToken;
    },
    createConfirmInvitationLink(userId, expiresIn, organizationId) {
      const accessToken = jwt.issue({id: userId}, expiresIn, this.settings.JWT_SECRET);
      return config.domain + "/confirm-invitation?organizationId=" + organizationId + "&accessToken=" + accessToken;
    },

    creatRejectInvitationLink(userId, expiresIn, organizationId) {
      const accessToken = jwt.issue({id: userId}, expiresIn, this.settings.JWT_SECRET);
      return config.domain + "/reject-invitation?organizationId=" + organizationId + "&accessToken=" + accessToken;
    },

    async sendEmailInvitationMember(user, organizationId, confirmInvitationLink, rejectInvitationLink) {
      const {email, fullName} = user;
      const organization = await this.broker.call('organizations.get', {id: organizationId?.toString()});
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
        to: email, // list of receivers
        subject: `Lời mời vào tổ chức: ${organization.name}`, // Subject line
        //text: 'Pass moi la 123455', // plaintext body
        html: createInvitationEmail({fullName}, organization.name, confirmInvitationLink, rejectInvitationLink)
      };

      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
          throw new MoleculerClientError(i18next.t("error_send_email"), 500);
        }
      });
    },

    async sendEmailInvitationNewAccount(email, organizationId, confirmInvitationLink) {
      const organization = await this.broker.call('organizations.get', {id: organizationId?.toString()});
      let mailOptions = {
        from: `${i18next.t('email_user_create_from')} <${config.mail.auth.user}>`, // sender address
        to: email, // list of receivers
        subject: `Lời mời sử dụng phần mềm`, // Subject line
        //text: 'Pass moi la 123455', // plaintext body
        html: createInvitationNewUser({account: email}, organization.name, confirmInvitationLink)
      };

      sendEmail(mailOptions, (err) => {
        if (err) {
          console.log(err);
        }
      });
    },
    async seedDB() {
      const users = await this.broker.call('users.find', {
        query: {isDeleted: false, organizationId: {$exists: true}}
      });
      const bulkWriteOperations = users.map(row => ({
        updateOne: {
          filter: {email: row.email},
          update: {
            $set: {
              email: row.email,
              status: "accepted",
              organizationId: row.organizationId,
              fullName: row.fullName,
              role: row.role
            }
          },
          upsert: true,
        },
      }));

      await Model.bulkWrite(bulkWriteOperations, {ordered: false});
    },
  },
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
    const count = await this.adapter.count();
    if (count === 0) {
      return this.seedDB();
    }
  },
};
