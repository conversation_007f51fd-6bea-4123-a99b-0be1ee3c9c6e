const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { FINETUNING, DATASET, INSTRUCTION } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    job: { type: Schema.Types.Mixed },
    datasetId: { type: Schema.Types.ObjectId, ref: DATASET },
    instructionId: { type: Schema.Types.ObjectId, ref: INSTRUCTION },
    gptModel: String,
    fineTunedModel: String,
    status: String,
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(FINETUNING, schema, FINETUNING);
