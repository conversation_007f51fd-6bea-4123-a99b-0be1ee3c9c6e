"use strict";

const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
module.exports = {
  name: "bs",
  mixins: [FunctionsCommon],

  settings: {},

  dependencies: [],
  hooks: {
    before: {
      "*": "checkAPIKey",
    }
  },
  actions: {
    register: {
      rest: {
        method: "POST",
        path: "/register",
      },
      async handler(ctx) {
        const {phone, packageCode} = ctx.params;
        //Check phone
        const {users, packages} = await this.getUserAndPackage(phone, packageCode);
        if (packages.length === 0) return {message: i18next.t("package_not_exist"), code: 3}


        if (users.length > 0) {
          // Đăng ký gói mới cho người dùng
          const result = await this.broker.call("subscriptions.orderFromVNPT", {
            user: users[0],
            packageInfo: packages[0]
          });

          return {message: i18next.t("register_success"), code: 0, result}

        }


        const password = '123456';
        const user = await this.broker.call("users.registerFromPhone", {
          phone,
          password,
          packageId: packages[0]._id,
          type: packages[0].customerTarget
        });
        user.password = password
        return {message: i18next.t("register_success"), code: 0, user}
      },
    },
    unregister: {
      rest: {
        method: "POST",
        path: "/unregister",
      },
      async handler(ctx) {
        const {phone, packageCode} = ctx.params;
        //Check phone
        const {users, packages} = await this.getUserAndPackage(phone, packageCode);
        if (users.length === 0) return {message: i18next.t("phone_not_registered"), code: 2}
        if (packages.length === 0) return {message: i18next.t("package_not_exist"), code: 3}


        const result = await this.broker.call("subscriptions.unActive", {
          userId: users[0]._id,
          packageId: packages[0]._id
        });
        return {message: i18next.t("unregister_success"), code: 0}
      },
    },
    renew: {
      rest: {
        method: "POST",
        path: "/renew",
      },
      async handler(ctx) {
        const {phone, packageCode} = ctx.params;
        const {users, packages} = await this.getUserAndPackage(phone, packageCode);
        //Check phone
        if (users.length === 0) return {message: i18next.t("phone_not_registered"), code: 2}
        //Check packageCode
        if (packages.length === 0) return {message: i18next.t("package_not_exist"), code: 3}
        const customers = await this.broker.call("customers.getOneByUser", {userId: users[0]._id})
        const subscriptions = (await this.broker.call("subscriptions.find", {
          query: {
            customerId: customers._id,
            packageId: packages[0]._id
          }
        })).sort((a, b) => b.endDate - a.endDate);
        if (subscriptions.length === 0) return {message: i18next.t("package_not_registered"), code: 3}

        const subscription = await this.broker.call("subscriptions.renewPackage", {subscriptions: subscriptions[0]});
        if (!subscription) return {message: i18next.t("renew_package_error"), code: 2}
        return {message: i18next.t("renew_package_success"), code: 0}
      },
    },

  },

  events: {},

  methods: {

    async checkAPIKey(ctx) {
      const {clickeeAPIKey} = ctx.meta;
      const setting = await ctx.call("settings.findOne");
      if (setting.clickeeAPIKey !== clickeeAPIKey) throw new MoleculerClientError(i18next.t("invalid_api_key"), 401);
    },

    async timeout(delay) {
      return new Promise((res) => setTimeout(res, delay));
    },
    async getUserAndPackage(phone, packageCode) {
      const users = await this.broker.call("users.find", {
        query: {phone}
      });

      const packages = await this.broker.call("packages.find", {
        query: {code: packageCode}
      });
      return {users, packages};
    }

  },

  created() {
  },

  async started() {
  },

  async stopped() {
  },
};
