const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./docxOptions.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

module.exports = {
  name: "docxoptions",
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      "docxTemplateId": 'docxtemplates.get',
    },
    populateOptions: ["docxTemplateId"],
  },

  hooks: {},

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL
    }
  },
  methods: {},
  events: {},
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
