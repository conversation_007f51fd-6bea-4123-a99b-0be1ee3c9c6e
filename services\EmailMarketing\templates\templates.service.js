const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./templates.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'mkttemplates',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'createdBy': 'users.get',
    },
  },

  hooks: {},

  actions: {
    /**
     * Create a new email template
     */
    create: {
      params: {
        name: {type: 'string', min: 2},
        description: {type: 'string', optional: true},
        subject: {type: 'string', min: 2},
        content: {type: 'string', min: 10},
        variables: {type: 'array', items: 'string', optional: true},
        category: {
          type: 'enum',
          values: ['welcome', 'promotion', 'reminder', 'feedback', 'other'],
          default: 'other'
        },
      },
      async handler(ctx) {
        const {user} = ctx.meta;
        const entity = ctx.params;

        entity.createdBy = user._id;

        // Extract variables from content if not provided
        if (!entity.variables || entity.variables.length === 0) {
          entity.variables = this.extractVariablesFromContent(entity.content);
        }

        const doc = await this.adapter.insert(entity);
        const template = await this.transformDocuments(ctx, {}, doc);

        return template;
      }
    },

    /**
     * Preview an email template with sample data
     */
    preview: {
      rest: {
        method: 'POST',
        path: '/:id/preview'
      },
      params: {
        id: {type: 'string'},
        data: {type: 'object', optional: true},
      },
      async handler(ctx) {
        const {id, data = {}} = ctx.params;

        const template = await this.adapter.findById(id);
        if (!template) {
          throw new MoleculerClientError(i18next.t('Template not found'), 404);
        }
        // Generate sample data for variables not provided
        const sampleData = this.generateSampleData(template.variables, data);
        // Replace variables in subject and content
        const subject = this.replaceVariables(template.subject, sampleData.previewData || sampleData);
        const content = this.replaceVariables(template.content, sampleData.previewData || sampleData);

        return {
          subject,
          content,
          data: sampleData
        };
      }
    },
  },

  methods: {
    /**
     * Extract variables from content using regex
     */
    extractVariablesFromContent(content) {
      const regex = /{([a-zA-Z0-9_]+)}/g;
      const variables = new Set();
      let match;

      while ((match = regex.exec(content)) !== null) {
        variables.add(match[1]);
      }

      return Array.from(variables);
    },

    /**
     * Generate sample data for template variables
     */
    generateSampleData(variables, providedData = {}) {
      const sampleData = {...providedData};

      // Common sample values for known variables
      const commonSamples = {
        name: 'John Doe',
        email: '<EMAIL>',
        trial_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
        personal_link: 'https://clickee.ai/user/123456',
        userFullname: 'John Doe',
        account: '<EMAIL>',
        trialTime: '30 days',
        supportEmail: '<EMAIL>',
        organizationName: 'Acme Corporation',
      };

      // Fill in missing variables with sample data
      variables.forEach(variable => {
        if (sampleData[variable] === undefined) {
          sampleData[variable] = commonSamples[variable] || `[Sample ${variable}]`;
        }
      });

      return sampleData;
    },

    /**
     * Replace variables in a string with their values
     */
    replaceVariables(text, data) {
      return text.replace(/{([a-zA-Z0-9_]+)}/g, (match, variable) => {
        return data[variable] !== undefined ? data[variable] : match;
      });
    },
  }
};
