"use strict";

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 * @typedef {import('moleculer-db').MoleculerDB} MoleculerDB  Moleculer's DB Service Schema
 */
const {TOOL_IDS} = require("../services/tools/tools.constants");
const {PERMISSION_ACCESS} = require("../constants/constant");
const showdown = require('showdown');
const Ajv = require("ajv");
const {convert} = require('html-to-text');
const axios = require('axios');
const QRCode = require("qrcode");
const fs = require("fs");
const https = require("https");
const {marked} = require('marked');
const html2md = require('html-to-md');
module.exports = {
  methods: {

    createDataTree(dataset, idProperty, parentIdProperty, childrenKey = 'children') {
      const hashTable = {};
      const dataTree = [];

      dataset.forEach(aData => {
        const newNode = {...aData, [childrenKey]: []};
        hashTable[aData[idProperty]] = newNode;

        if (aData[parentIdProperty] && hashTable[aData[parentIdProperty]]) {
          hashTable[aData[parentIdProperty]][childrenKey].push(newNode);
        } else {
          dataTree.push(newNode);
        }
      });

      return dataTree;
    },

    async getAllIdInScope(ctx, actionName, currentId, includeDeleted = false) {
      const query = includeDeleted
        ? {parentId: {$in: [currentId]}}
        : {isDeleted: false, parentId: {$in: [currentId]}};

      const donViSet = new Set();
      const parentIs = [currentId];

      while (parentIs.length) {
        const children = await ctx.call(actionName, {query});
        const childIds = children.map(child => child._id);
        parentIs.push(...childIds);
        donViSet.add(...childIds);
      }

      return Array.from(donViSet);
    },

    groupBy(listData, key) {
      return listData.reduce(function (grouped, element) {
        (grouped[element[key]] = grouped[element[key]] || []).push(element);
        return grouped;
      }, {});
    },

    convertObject(list, key) {
      return list.reduce(function (prevValue, currentValue) {
        prevValue[currentValue?.[key]] = currentValue;
        return prevValue;
      }, {});
    },

    extractIdFromList(listData = []) {
      return listData.map(element => element?._id?.toString());
    },

    extractKeyFromList(listData = [], key) {
      return listData.map(element => element[key]?.toString());
    },
    addIndexToListData(listData = []) {
      return listData?.map((element, index) => {
        element.idx = index + 1;
        return element;
      });
    },
    addStringIndex(listData = []) {
      return listData?.map((element, index) => {
        element.idx = `${index + 1}.  `;
        return element;
      });
    },
    addRomanIndex(listData = []) {
      return listData?.map((element, index) => {
        const romanNumerial = this.convertToRoman(index + 1);
        element.idx = `${romanNumerial}.  `;
        return element;
      });
    },
    secondsToHMS(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      const formattedHours = String(hours).padStart(2, '0');
      const formattedMinutes = String(minutes).padStart(2, '0');
      const formattedSeconds = String(remainingSeconds).padStart(2, '0');

      return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    },
    secondsToMS(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      const formattedMinutes = minutes.toString().padStart(2, '0');
      const formattedSeconds = remainingSeconds.toString().padStart(2, '0');
      return `${formattedMinutes}:${formattedSeconds}`;
    },
    getContentTitle(toolId) {
      const titles = {
        [TOOL_IDS.TRANSCRIPT_YOUTUBE]: "Let's Go Through the Video Transcript!",
        [TOOL_IDS.AUDIO_TO_TEXT]: "Let's Go Through the Audio Transcript!",
        [TOOL_IDS.CREATE_SUMMARY_OPTIONS]: "Pick the Right Summary of the Video!",
        [TOOL_IDS.CREATE_QUESTIONS_FOR_VIDEO]: "Watch the Video and Answer the Questions!",
        [TOOL_IDS.CREATE_OPEN_QUESTIONS_FOR_TEXT]: "Let's Answer the Following Questions!",
        [TOOL_IDS.CREATE_THREE_TITLES_FOR_TEXT]: "Which Title Fits Best?",
        [TOOL_IDS.CREATE_ABCD_QUESTIONS_FOR_TEXT]: "Pick the Right Answer!",
        [TOOL_IDS.CREATE_TRUE_FALSE_QUESTIONS_FOR_TEXT]: "Can You Tell which Sentences are True and which are False?",
        [TOOL_IDS.CREATE_WARM_DISCUSSION_QUESTIONS_FOR_VIDEO]: "Let's Discuss These Interesting Questions!",
        [TOOL_IDS.CREATE_WARM_DISCUSSION_QUESTIONS_FOR_TEXT]: "Let's Discuss These Interesting Questions!",
        [TOOL_IDS.CREATE_TEXT_ON_TOPIC]: "Time to Read!",
        [TOOL_IDS.CREATE_QUESTIONS_FOR_AUDIO]: "Listen the Audio and Answer the Questions!",
        [TOOL_IDS.CREATE_OPEN_QUESTIONS_FOR_AUDIO]: "Listen the Audio and Answer the Questions!",
        [TOOL_IDS.CREATE_SUMMARY_OPTIONS_FOR_AUDIO]: "Listen the Audio and Answer the Questions!",
        [TOOL_IDS.CREATE_WARM_DISCUSSION_QUESTIONS_FOR_AUDIO]: "Listen the Audio and Answer the Questions!"
      };

      return titles[toolId] || "Content!";
    },

    getUniqueObjects(arrayData, key) {
      const uniqueObjectsSet = new Set();
      return arrayData.reduce((result, item) => {
        if (!uniqueObjectsSet.has(item[key])) {
          uniqueObjectsSet.add(item[key]);
          result.push(item);
        }
        return result;
      }, []);
    },
    lineBreak(string) {
      return `${string}\n`;
    },

    editAccess(accessLevel) {
      switch (accessLevel) {
        case PERMISSION_ACCESS.NO_PERMISSION:
        case PERMISSION_ACCESS.VIEWER:
          return false;
        case PERMISSION_ACCESS.OWNER:
        case PERMISSION_ACCESS.EDITOR:
          return true;
      }
      return false;
    },
    hasViewAccess(permissionAccess) {
      return permissionAccess !== PERMISSION_ACCESS.NO_PERMISSION;
    },

    convertMarkDownToHTML(string) {
      const options = {
        pedantic: false,
        gfm: true,
        breaks: true,
      };
      const replaceString = string?.replace(/\t/g, '&emsp;');
      let htmlString = marked(replaceString, options);
      htmlString = htmlString
        .replace(/<em>/g, '<i>')
        .replace(/<\/em>/g, '</i>');

      return htmlString;
    },
    checkValidSchema(schema, data) {
      const ajv = new Ajv();
      return ajv.validate(schema, data);
    },

    convertHTMLToText(html) {
      const options = {
        wordwrap: 130,
      };
      return convert(html, options);
    },
    convertHTMLToMarkdown(html) {
      const options = {
        skipTags: ['']
      };
      const force = {};
      return html2md(html, options, force);
    },

    async imageUrlToBase64(imageUrl) {
      try {
        const response = await axios.get(imageUrl, {responseType: 'arraybuffer'});
        return Buffer.from(response.data, 'binary').toString('base64');
      } catch (error) {
        console.error('Error converting image to base64:', error);
        throw error;
      }
    },

    wrapConversationText(text) {
      return text.replace(/([.?]) /g, '$1\n');
    },

    extractQueryTime(params) {
      const {time, fromDate, toDate} = params;
      let createdAtQuery = {};

      switch (time) {
        case 'month':
          const {firstDay, lastDay} = this.getMonthRange();
          createdAtQuery = {$gte: firstDay, $lte: lastDay};
          break;
        case 'week':
          const {firstDay: weekFirstDay, lastDay: weekLastDay} = this.getWeekRange();
          createdAtQuery = {$gte: weekFirstDay, $lte: weekLastDay};
          break;
        case 'custom':
          createdAtQuery = {
            $gte: fromDate ? new Date(fromDate * 1000) : new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            $lte: toDate ? new Date(toDate * 1000) : new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59, 999)
          };
          break;
        default:
          const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
          const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59, 999);
          createdAtQuery = {$gte: startOfMonth, $lte: endOfMonth};
          break;
      }

      return {createdAt: createdAtQuery};
    },
    extractParamsList(params) {
      const {page, limit, sort} = params;
      return {
        page: +page || 1,
        pageSize: +limit || 10,
        sort: sort || '-createdAt',
      };
    },
    extractParamsPage(params) {
      const {page, pageSize, sort} = params;
      return {
        page: +page || 1,
        pageSize: +pageSize || 10,
        sort: sort || '-createdAt',
      };
    },

    getMonthRange() {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      return {firstDay, lastDay};
    },
    getWeekRange() {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
      const lastDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay() + 7);
      return {firstDay, lastDay};
    },
    validateYouTubeUrl(urlToParse) {
      if (urlToParse) {
        const regExp = /^(?:https?:\/\/)?(?:m\.|www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/;
        if (urlToParse.match(regExp)) {
          return true;
        }
      }
      return false;
    },

    getQrCodeImage(url, imagePath) {
      return new Promise((resolve, reject) => {
        QRCode.toFile(imagePath, url, function (err) {
          if (err) {
            reject(err);
          }
          resolve(imagePath);
        });
      });
    },
    saveImageUrlToLocal(imageUrl, localPath) {
      try {
        let download = function (url, dest, cb) {
          let file = fs.createWriteStream(dest);
          let request = https
            .get(url, function (response) {
              response.pipe(file);
              file.on('finish', function () {
                file.close(cb);
              });
            })
            .on('error', function (err) {
              fs.unlink(dest); // Delete the file async if there is an error
              if (cb) cb(err.message);
            });

          request.on('error', function (err) {
            console.log(err);
          });
        };
        return new Promise((resolve, reject) => {
          download(imageUrl, localPath, function (err) {
            if (err) {
              console.log(err);
            } else {
              console.log('Saved image to ' + localPath);
              resolve(localPath);
            }
          });
        });
      } catch (e) {
        console.log(e);
      }
    },

    convertToRoman(num) {
      const roman = [
        {value: 1000, numeral: 'M'},
        {value: 900, numeral: 'CM'},
        {value: 500, numeral: 'D'},
        {value: 400, numeral: 'CD'},
        {value: 100, numeral: 'C'},
        {value: 90, numeral: 'XC'},
        {value: 50, numeral: 'L'},
        {value: 40, numeral: 'XL'},
        {value: 10, numeral: 'X'},
        {value: 9, numeral: 'IX'},
        {value: 5, numeral: 'V'},
        {value: 4, numeral: 'IV'},
        {value: 1, numeral: 'I'}
      ];
      return roman.reduce((acc, {value, numeral}) => {
        const count = Math.floor(num / value);
        num %= value;
        return acc + numeral.repeat(count);
      }, '');
    },

    getUniqueID() {
      return Math.random().toString(36).substring(2, 16);
    },

    splitParagraphsByKeywords(paragraph, keywords) {
      const regex = new RegExp(keywords?.map(k => `(${k})`).join('|'), 'gi');
      return paragraph.split(regex).filter(Boolean);
    },
    replaceKeywords(text, keywords) {
      for (let [key, value] of Object.entries(keywords)) {
        const regex = new RegExp(`\\b${key}\\b`, "gi");
        text = text.replace(regex, value);
      }
      return text;
    },
    calculateIELTSOverall(listening, reading, writing, speaking) {
      const total = listening + reading + writing + speaking;

      const average = total / 4;

      return Math.round(average * 2) / 2;
    },
    imageTypeFromBase64(base64) {
      const types = {
        'i': 'image/png',
        'I': 'image/png',
        '/': 'image/jpeg',
        'R': 'image/gif',
        'U': 'image/webp'
      };

      return types[base64.charAt(0)] || 'image/jpeg';
    },
    normalizeText(text) {
      if (typeof text !== 'string') {
        return '';
      }
      return text
        .toLowerCase()
        // Xóa các dấu câu phổ biến. Bạn có thể tùy chỉnh regex này nếu cần.
        .replace(/[.,!?;:()"'-]/g, '')
        // Thay thế nhiều khoảng trắng liên tiếp bằng một khoảng trắng duy nhất
        .replace(/\s+/g, ' ')
        // Xóa khoảng trắng ở đầu và cuối chuỗi
        .trim();
    },
    calculateWER(referenceText, hypothesisText) {
      const refClean = this.normalizeText(referenceText);
      const hypClean = this.normalizeText(hypothesisText);

      // Tách thành mảng các từ
      const refWords = refClean.split(' ').filter(word => word.length > 0); // Lọc bỏ từ rỗng nếu có
      const hypWords = hypClean.split(' ').filter(word => word.length > 0); // Lọc bỏ từ rỗng nếu có

      const n = refWords.length; // Số từ trong tham chiếu
      const m = hypWords.length; // Số từ trong giả thuyết

      // ---- Xử lý các trường hợp đặc biệt ----
      // Nếu cả hai đều rỗng sau khi chuẩn hóa
      if (n === 0 && m === 0) {
        return 0.0; // Không có lỗi
      }
      // Nếu tham chiếu rỗng nhưng giả thuyết có từ
      if (n === 0) {
        // Theo định nghĩa WER = (S+D+I)/N, mẫu số N=0.
        // Có thể coi là lỗi vô hạn hoặc 100% lỗi chèn.
        // Trả về Infinity hoặc một giá trị lớn để biểu thị điều này.
        // Hoặc, nếu bạn muốn tính số lần chèn, bạn có thể trả về m.
        // Ở đây chúng ta trả về Infinity theo định nghĩa chặt chẽ.
        return Infinity;
      }
      // Nếu giả thuyết rỗng nhưng tham chiếu có từ
      if (m === 0) {
        // Tất cả các từ trong tham chiếu đều bị xóa (D = n).
        return 1.0; // WER = n / n = 1.0 (100% lỗi xóa)
      }

      // ---- Thuật toán Quy hoạch động (Levenshtein cho từ) ----
      // Tạo ma trận khoảng cách (n+1) x (m+1)
      const dp = Array(n + 1).fill(null).map(() => Array(m + 1).fill(0));

      // Khởi tạo hàng đầu tiên (chi phí chèn từ của giả thuyết vào tham chiếu rỗng)
      for (let j = 0; j <= m; j++) {
        dp[0][j] = j; // Cần j thao tác chèn
      }

      // Khởi tạo cột đầu tiên (chi phí xóa từ của tham chiếu để thành giả thuyết rỗng)
      for (let i = 0; i <= n; i++) {
        dp[i][0] = i; // Cần i thao tác xóa
      }

      // Điền vào phần còn lại của ma trận
      for (let i = 1; i <= n; i++) {
        for (let j = 1; j <= m; j++) {
          // Chi phí thay thế: 0 nếu từ giống nhau, 1 nếu khác nhau
          const substitutionCost = refWords[i - 1] === hypWords[j - 1] ? 0 : 1;

          dp[i][j] = Math.min(
            dp[i - 1][j] + 1,          // Lỗi xóa (Deletion) từ tham chiếu
            dp[i][j - 1] + 1,          // Lỗi chèn (Insertion) vào tham chiếu
            dp[i - 1][j - 1] + substitutionCost // Lỗi thay thế (Substitution) hoặc khớp (Match)
          );
        }
      }

      // Tổng số lỗi (S + D + I) là giá trị ở góc dưới cùng bên phải của ma trận
      const totalErrors = dp[n][m];

      // Tính WER
      const wer = totalErrors / n;

      return wer;
    },

    convertAzureScoreToIELTS(vocabularyScore, grammarScore, pronScore, fluencyScore) {
      const scoreMapping = [
        {min: 0, max: 45, score: 2.0},
        {min: 46, max: 55, score: 3.0},
        {min: 56, max: 65, score: 4.0},
        {min: 66, max: 75, score: 5.0},
        {min: 76, max: 80, score: 6.0},
        {min: 81, max: 85, score: 7.0},
        {min: 86, max: 95, score: 8.0},
        {min: 96, max: 100, score: 9.0}
      ];

      const convertSingleScoreToIELTS = (score) => {
        const mapping = scoreMapping.find(({min, max}) => score >= min && score <= max);
        return mapping ? mapping.score : 0;
      };

      return {
        vocabularyScore: vocabularyScore + 0.5,
        grammarScore: grammarScore + 0.5,
        pronScore: convertSingleScoreToIELTS(pronScore),
        fluencyScore: convertSingleScoreToIELTS(fluencyScore)
      };
    }
  }
}
