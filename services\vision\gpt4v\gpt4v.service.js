"use strict";

const fs = require("fs");
const { OpenAI } = require("openai");
const base64 = require('base-64');
const {USER_CODES} = require("../../../constants/constant");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */

const configuration = {
  apiKey:
    process.env.OPENAI_API_KEY ||
    "***************************************************",
};

module.exports = {
  /**
   * Settings
   */
  settings: {},

  hooks: {
    before: {
      "*": "getAPIKey",
    }
  },

  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */
    describeImage: {
      auth: "required",
      timeout: 2 * 60 * 1000,
      rest: {
        method: "GET",
        path: "/describeImage",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      // visibility: "protected",
      async handler(ctx) {
        const { filePath, instruction, imageBuffer, responseId } = ctx.params;
        const { apiKey } = ctx.meta;
        try {
          if (filePath) {
            const sizeCheck = await this.checkSize(filePath);
            if (sizeCheck > 9.96) {
              return {
                error: "File size is greater than 10MB, try smaller video",
              };
            }
          }
          const base64 = imageBuffer ? this.base64FromBuffer(imageBuffer) : this.base64FromFile(filePath);
          // console.log("========================instruction=======================", instruction, base64);
          return this.describeImage(base64, instruction, apiKey, responseId);
        } catch (err) {
          return err;
        }
      },
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    async getAPIKey(ctx) {
      const setting = await ctx.call("settings.findOne");
      return ctx.meta.apiKey = setting?.apiKeyOpenAI || process.env.OPENAI_API_KEY;
    },
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        return fileSizeInBytes / (1024 * 1024);
      } catch (error) {
        return error;
      }
    },
    base64FromFile: (filePath) => {
      try {
        const imageFile = fs.readFileSync(filePath);
        return base64.encode(imageFile).toString('utf-8');
      } catch (error) {
        return error;
      }
    },
    base64FromBuffer: (imageBuffer) => {
      try {
        return imageBuffer.toString("base64");
      } catch (error) {
        return error;
      }
    },
    async describeImage(base64Image, instruction, apiKey, responseId, model) {
      try {
        const payload = {
          "model": model || "gpt-4o-mini",
          "messages": [
            {
              "role": "user",
              "content": [
                {
                  "type": "text",
                  "text": instruction || "Describe the image"
                },
                {
                  "type": "image_url",
                  "image_url": {
                    "url": `data:image/jpeg;base64,${ base64Image }`
                  }
                }
              ]
            }
          ],
          "max_tokens": 300
        };
        console.log("apiKey", apiKey);
        const headers = {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${ apiKey }`
        };

        const postData = async () => {
          try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
              method: 'POST',
              headers: headers,
              body: JSON.stringify(payload)
            });

            return await response.json();
          } catch (error) {
            console.error(error);
          }
        };
        const response = await postData();
        const { completion_tokens, prompt_tokens, total_tokens } = response.usage;
        this.broker.emit("llmGenerateCompleted", {
          id: responseId,
          completionTokens: completion_tokens,
          promptTokens: prompt_tokens,
          totalTokens: total_tokens,
          gptModel: model
        })
        return response.choices[0].message.content;
      } catch (e) {
        console.log(e);
      }
    }
  },

  name: "gpt4v",

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
