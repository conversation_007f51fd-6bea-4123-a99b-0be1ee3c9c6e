"use strict";

const fs = require("fs");
const {USER_CODES} = require("../../../constants/constant");
/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */

const APP_URL = "https://llava.hliu.cc/";

module.exports = {
  /**
   * Settings
   */
  settings: {
  },
  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */
    describeImage: {
      auth: "required",
      timeout: 2 * 60 * 1000,
      rest: {
        method: "GET",
        path: "/describeImage",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      // visibility: "protected",
      async handler(ctx) {
        const {filePath, instruction, imageBuffer} = ctx.params;
        try {
          if (filePath) {
            const sizeCheck = await this.checkSize(filePath);
            if (sizeCheck > 9.96) {
              return {
                error: "File size is greater than 10MB, try smaller video",
              };
            }
          }
          const base64 = imageBuffer ? this.base64FromBuffer(imageBuffer) : this.base64FromFile(filePath);
          console.log("========================instruction=======================", instruction);
          return this.describeImage(base64, instruction);
        } catch (err) {
          return err;
        }
      },
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        return fileSizeInBytes / (1024 * 1024);
      } catch (error) {
        return error;
      }
    },
    base64FromFile: (filePath) => {
      try {
        const base64 = fs.readFileSync(filePath, {encoding: "base64"});
        return `data:image/png;base64,${base64}`;
      } catch (error) {
        return error;
      }
    },
    base64FromBuffer: (imageBuffer) => {
      try {
        const base64 = imageBuffer.toString("base64");
        return `data:image/png;base64,${base64}`;
      } catch (error) {
        return error;
      }
    },
    describeImage: async (base64Image, instruction) => {
      try {
        const {client} = await import('@gradio/client');
        const app = await client(APP_URL);
        console.log(APP_URL)
        const result = await app.predict(7, [
          null,
          "describe image",
          base64Image,
          "Default"
        ]);
        const result2 = await app.predict(8, [
          null,
          "llava-v1.5-13b",
          0.2,
          0.7,
          512
        ]);
        console.log("===============================================================", JSON.stringify(result));
        return result2.data[0][0][1];
      } catch (e) {
        console.log(e);
      }
      try {
        const {client} = await import('@gradio/client');
        const app = await client(APP_URL);
        console.log(APP_URL)
        const result2 = await app.predict(8, [
          null,
          "llava-v1.5-13b",
          0.2,
          0.7,
          512
        ]);
        console.log("===============================================================", JSON.stringify(result2));
        return result2.data[0][0][1];
      } catch (e) {
        console.log(e);
        return e;
      }
    }
  },

  name: "minigpt2",

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
