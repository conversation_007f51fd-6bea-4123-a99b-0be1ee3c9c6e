"use strict";

const Model = require("./whitelist.model");
const DbMongoose = require("../../../mixins/dbMongo.mixin");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRole = require("../../../mixins/authRole.mixin");
const {USER_CODES} = require("../../../constants/constant");

/**
 *
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 */

/** @type {ServiceSchema} */
module.exports = {
  name: "whitelist",
  mixins: [<PERSON><PERSON><PERSON>ong<PERSON><PERSON>(Model), BaseService, FunctionsCommon, AuthRole],
  /**
   * Settings
   */
  settings: {
    // Validator for the `create` & `insert` actions.
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },
  hooks: {},
  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    create: {
      rest: {
        method: "POST",
        path: "/",
      },
      permission: "admin",
      async handler(ctx) {
        const {email, organizationId} = ctx.params;
        const whiteList = await this.adapter.findOne({email});
        if (whiteList) throw new MoleculerClientError(i18next.t("email_already_exists"));

        const waitList = await ctx.call("waitlist.getOne", {email: email});
        if (waitList) {
          await ctx.emit("user.activeStateUser", {email: email});
          await ctx.call("waitlist.remove", {id: waitList._id});
        }

        return await this.adapter.insert({email, organizationId});
      }
    },
    remove: {
      rest: {
        method: "DELETE",
        path: "/:id",
      },
      permission: "admin",
      async handler(ctx) {
        const {id} = ctx.params;
        const whiteList = await this.adapter.findById(id);
        if (!whiteList) throw new MoleculerClientError(i18next.t("email_not_found"), 404, "NOT_FOUND");

        return await this.adapter.removeById(id);
      },
    },
    getAll: {
      rest: {
        method: "GET",
        path: "/manager",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        const {time, email} = ctx.params;

        const query = {email};
        if (time) {
          query.createdAt = this.extractQueryTime(ctx.params);
        }
        const paramsList = this.extractParamsList(ctx.params);
        const params = {
          ...paramsList,
          searchFields: "email",
          query: JSON.stringify(query),
        }
        return ctx.call("whitelist.list", params);
      }
    },
    getOne: {
      rest: {
        method: "GET",
        path: "/getOne",
      },
      role: USER_CODES.SYSTEM_ADMIN,
      async handler(ctx) {
        try {
          const {params} = ctx;
          return await this.adapter.findOne(params)
        } catch (e) {
          console.log(e);
        }
      }
    }
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
