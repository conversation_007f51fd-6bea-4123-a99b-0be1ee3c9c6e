const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const { INVITATION_MEMBER, ORGANIZATION } = require('../../../constants/dbCollections');

const schema = new mongoose.Schema({
  email: {
    type: String, trim: true, index: true, lowercase: true,
    required: "Please fill in an email"
  },
  status: { type: String, enum: ["pending", "accepted", "rejected"], default: "pending" },
  organizationId: { type: mongoose.Schema.Types.ObjectId, ref: ORGANIZATION },
  fullName: { type: String },
  role: { type: String, enum: ["admin", "normal", "contributor"], default: "normal" },
  isDeleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(INVITATION_MEMBER, schema, INVITATION_MEMBER);
