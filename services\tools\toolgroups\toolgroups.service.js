const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./toolgroups.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");

module.exports = {
  name: 'toolgroups',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
  },

  hooks: {
    before: {}
  },

  actions: {},
  methods: {
    async seedDB() {
    },
  },
  events: {},
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },

  async afterConnected() {
  },
};
