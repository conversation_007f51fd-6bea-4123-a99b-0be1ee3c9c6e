"use strict";

const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const Model = require("./voices.model");
const DbMongoose = require("../../mixins/dbMongo.mixin");
const AuthRole = require("../../mixins/authRole.mixin");

module.exports = {
  name: "voices",
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin, AuthRole],

  /**
   * Settings
   */
  settings: {
    entityValidator: {},
    populates: {
      "voiceFileId": 'files.get',
    },
    populateOptions: ["voiceFileId"],
  },

  /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    upload: {
      async handler(ctx) {
        const {name, code, voiceId} = ctx.meta.$multipart;
        ctx.meta.$multipart.folder = "voices";
        ctx.meta.$multipart.userId = ctx.meta.user._id;
        ctx.meta.$multipart.fileType = "audio";
        ctx.meta.bypassCapacityAddition = true;
        const file = await ctx.call("files.upload", ctx.params, {meta: ctx.meta});
        if (voiceId) {
          const voiceUpdated = await this.adapter.updateById(voiceId, {voiceFileId: file._id});
          return await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, voiceUpdated);
        }

        return this.adapter.insert({name, code, voiceFileId: file._id});
      }
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {},

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
