"use strict";

const DbService = require("moleculer-db");
const MongooseAdapter = require("moleculer-db-adapter-mongoose");

/**
 * @typedef {import('moleculer').ServiceSchema} ServiceSchema Moleculer's Service Schema
 * @typedef {import('moleculer').Context} Context Moleculer's Context
 * @typedef {import('moleculer-db').MoleculerDB} MoleculerDB  Moleculer's DB Service Schema
 */

module.exports = function (mongooseModel) {
  const mongoDbUri =
    // process.env.MONGO_URI || "*************************************************************************************************************************";
    // process.env.MONGO_URI || "***********************************************************************************************************";
    // process.env.MONGO_URI || "***********************************************************************************************************************";
    // process.env.MONGO_URI || "********************************************************************************************************************************";
    process.env.MONGO_URI || "*******************************************************************************************************************************************************";
  // process.env.MONGO_URI || "********************************************************************************************************************************";
  // process.env.MONGO_URI || "mongodb+srv://clickee:<EMAIL>/";
  // process.env.MONGO_URI || "mongodb://127.0.0.1:27017/lingotutorsuite";


  return {
    mixins: [DbService],
    adapter: new MongooseAdapter(mongoDbUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    }),
    model: mongooseModel,
    actions: {
      create: {
        visibility: "published",
        auth: "required",
      },
      update: {
        visibility: "published",
        auth: "required",
      },
      list: {
        visibility: "published",
        auth: "required",
      },
      get: {
        visibility: "published",
        auth: "required",
      },
      remove: {
        visibility: "published",
        auth: "required",
      },
      insertMany: {
        auth: "required",
        async handler(ctx) {
          return this.adapter.insertMany(ctx.params);
        },
      },
    },
  };
};
