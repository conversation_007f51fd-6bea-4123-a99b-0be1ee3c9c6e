const sdk = require("microsoft-cognitiveservices-speech-sdk");
const path = require("path");
const fs = require("fs");
const wav = require("wav");
const {Server} = require("socket.io");
const {pronunciationAssessment} = require("./pronunciationAssessment");
const FileMixin = require("../../mixins/file.mixin");
const FunctionCommonMixin = require("../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {getConfig} = require("../../config/config");
const config = getConfig(process.env.NODE_ENV);
const storageDir = path.join(__dirname, "storage");
const {MoleculerClientError} = require("moleculer").Errors;
const {createMessages} = require("../tools/promptengine");

const globalState = new Map();
module.exports = {
  name: "recognition",
  mixins: [FileMixin, FunctionCommonMixin],

  dependencies: [
    "settings"
  ],

  actions: {
    getAudioUrl: {
      rest: 'GET /link/:fileName',
      async handler(ctx) {
        try {
          const {fileName} = ctx.params;
          const filePath = this.getFilePath(fileName, storageDir);
          if (!fs.existsSync(filePath)) return new MoleculerClientError(i18next.t("error_file_not_found"), 404);
          let stat = fs.statSync(filePath);
          ctx.meta.$responseHeaders = {
            "Content-Type": "audio/wav",
            "Content-Length": stat.size,
            "Content-Disposition": `attachment;filename=${fileName}`
          };
          return fs.createReadStream(filePath, {});
        } catch (e) {
          throw new MoleculerClientError(i18next.t("error_file_not_found"), 404);
        }
      }
    },
  },

  methods: {
    async saveAudioFile(state, filePath, socket) {
      try {
        const {audioChunks, userId} = state;
        const file = await this.broker.call("resources.createFromRecordAudio", {
          audioChunks, userId,
          fileName: `record_audio_${socket.id}.wav`
        });
        const fileStream = fs.createWriteStream(filePath);
        const wavWriter = new wav.Writer({
          sampleRate: 16000, // Tần số mẫu (Hz)
          channels: 1, // Số kênh (mono)
          bitDepth: 16, // Độ sâu bit (16-bit PCM)
        });

        wavWriter.pipe(fileStream);

        audioChunks.forEach((chunk) => wavWriter.write(chunk));

        wavWriter.end(() => {
          console.log(`Audio file saved: ${filePath}`);
          const audioUrl = `${config.domain}/api/recognition/link/record_audio_${socket.id}.wav`

          if (globalState[socket.id]) globalState[socket.id].fileId = file._id

          socket.send({state: "audio_file_saved", audioUrl, fileId: file._id})
        });
      } catch (e) {
        console.log(e)
      }
    },

    async handleFeedback(inputData, socket, connectionId) {
      try {
        const [contents, instructionData] = await Promise.all([
          this.broker.call("contents.find", {
            query: {projectId: inputData.projectId, isDeleted: false}
          }),
          this.broker.call("instructions.details", {
            id: inputData.instructionId,
            populateOpts: ["optionIds.knowledgeIds", "outputTypeId"]
          })
        ]);

        const duration = this.getDurationRecordAudio(inputData.results);
        const wpm = this.getWPM(inputData.recognizedText, duration);
        const errorsList = this.listWordByErrorType(inputData.results);
        const isMonotone = this.checkMonotone(inputData.results);

        inputData.contentScore = (inputData.vocabularyScore + inputData.grammarScore + inputData.topicScore) / 3;

        const text = `Input data:
      - Pronunciation Score: ${inputData.pronScore} (Accuracy: ${inputData.accuracyScore}, Fluency: ${inputData.fluencyScore}, Prosody: ${inputData.prosodyScore})
      - Content Score: ${+inputData.contentScore.toFixed(2)} (Vocabulary: ${inputData.vocabularyScore}, Grammar: ${inputData.grammarScore}, Topic Coherence: ${inputData.topicScore})
      - Errors: ${JSON.stringify(errorsList)}
      - Transcript: ${inputData.recognizedText}
      - Audio Data: Duration: ${duration}s, Rate of speech: ${wpm} wpm, Monotony: ${isMonotone}`;

        const html = (+duration < 10)
          ? `Content too short to get feedback details`
          : await this.getOpenAIFeedback(inputData, text, instructionData);

        this.saveDataProject(contents[0]?._id, inputData, html, connectionId);
        socket.send({state: "openai_feedback", html});
        socket.emit("finish-recognition");
      } catch (error) {
        console.error("Error handling feedback:", error);
        socket.send({state: "error", message: "An error occurred while processing feedback."});
      }
    },

    async getOpenAIFeedback(inputData, text, instructionData) {
      const messages = await createMessages({
        inputData,
        text,
        instructionData
      });
      const {
        apiKey,
        modelInterface,
        maxTokens,
        url: endpoint
      } = await this.broker.call("gptmodelprice.getOneByGptModel", {gptModel: instructionData.gptModel});
      const responseFormat = instructionData.outputTypeId?.responseFormat || instructionData.responseFormat;
      const result = await this.broker.call("chatgpt.chatCompletion", {
        messages,
        model: instructionData.gptModel,
        responseFormat,
        temperature: instructionData.temperature,
        modelInterface,
        apiKey,
        max_tokens: maxTokens,
        endpoint
      });

      return this.convertMarkDownToHTML(result);
    },

    async saveDataProject(contentId, inputData, html, connectionId) {
      const input = await this.broker.call("inputs.insert", {
        entity: {
          contentId,
          inputData: {
            topic: inputData.topic,
            instructionId: inputData.instructionId,
          },
          inputType: "audio_stream"
        }
      })

      const audioUrl = `${config.domain}/api/recognition/link/record_audio_${connectionId}.wav`

      const newResponse = {
        inputId: input._id,
        contentId,
        output: {
          html,
          recognizedText: inputData.recognizedText,
          results: inputData.results,
          overallPronunciationScore: inputData.overallPronunciationScore,
          fileId: globalState[connectionId].fileId,
          audioUrl
        },
        state: "done",
        isActivate: true
      };

      const response = await this.broker.call("responses.insert", {
        entity: newResponse
      })
      delete globalState[connectionId];
      this.broker.call("responses.deactivate", {id: response._id, contentId})
      this.broker.call("projects.update", {id: inputData.projectId, lastModifiedBy: inputData.userId})
    },

    listWordByErrorType(results) {
      return results
        .flatMap(item => item["NBest"][0].Words || [])
        .reduce((map, item) => {
          const {
            Word: word,
            PronunciationAssessment: {
              ErrorType: errorType,
              Feedback: {Prosody: {Break: {ErrorTypes: errorTypes = []} = {}} = {}} = {}
            }
          } = item;
          if (errorType && errorType !== "None") {
            map[errorType] = map[errorType] || [];
            map[errorType].push(word);
          }
          errorTypes.forEach(type => {
            if (type !== "None") {
              map[type] = map[type] || [];
              map[type].push(word);
            }
          });
          return map;
        }, {});
    },

    getDurationRecordAudio(results) {
      const lastWord = results
        .flatMap(item => item["NBest"][0].Words || [])
        .pop();

      return lastWord ? (lastWord.Offset + lastWord.Duration) / 10 ** 7 : 0;
    },

    getWPM(recognizedText, duration) {
      const words = recognizedText.split(/\s+/).filter(word => word.length > 0);
      const totalWords = words.length;

      const wpm = +totalWords / (duration / 60);
      return +wpm.toFixed(2);
    },

    checkMonotone(results) {
      return results
        .flatMap(item => (item["NBest"][0].Words || []))
        .some(word => word.PronunciationAssessment?.Feedback?.Prosody?.Intonation?.ErrorTypes?.includes("Monotone"));
    }

  },

  async started() {
  },

  events: {
    teacherConnected: {
      async handler(socket) {
        this.connectionState = {};

        const connectionId = socket.id;
        this.logger.info(`Client connected: ${connectionId}`);
        const {speechKey, serviceRegion, confidenceThreshold} = await this.broker.call("settings.findOne");
        const format = sdk.AudioStreamFormat.getWaveFormat(16000, 16, 1, sdk.AudioFormatTag.PCM);
        this.connectionState[connectionId] = {
          audioBufferQueue: [],
          audioChunks: [],
          audioStream: sdk.AudioInputStream.createPushStream(format),
          inputData: {},
          userId: null,
          startRecognition: false,
          isRecording: true,
          processInterval: null,
          callbackFunction: (data) => this.handleFeedback(data, socket, connectionId),
        };

        const state = this.connectionState[connectionId];
        globalState[connectionId] = state;
        state.processInterval = setInterval(() => {
          if (state.audioBufferQueue.length > 0) {
            while (state.audioBufferQueue.length > 0) {
              const audioData = state.audioBufferQueue.shift();
              try {
                state.audioStream.write(audioData);
              } catch (err) {
                console.error("Error writing audio data to stream:", err);
              }
            }

            if (!state.startRecognition) {
              pronunciationAssessment({
                socket, speechKey, serviceRegion, confidenceThreshold,
                audioStream: state.audioStream,
                inputData: state.inputData,
                callback: state.callbackFunction
              });
              state.startRecognition = true;
            }
          } else if (!state.isRecording) {
            clearInterval(state.processInterval);
            state.audioStream.close();
            console.log(`Stopped processing for client: ${connectionId}`);
          }
        }, 1000);

        socket.on("audio", (data) => {
          state.audioBufferQueue.push(data.buffer);
          state.audioChunks.push(data.buffer);
          state.inputData = data.inputData;
          state.userId = data.userId;
        });

        socket.on("close-recording", async () => {
          console.log(`Closing recording for client: ${connectionId}`);
          state.isRecording = false;
          const outputFilePath = this.getFilePath(`record_audio_${connectionId}.wav`, storageDir);
          await this.saveAudioFile(state, outputFilePath, socket);
        });

        socket.on("disconnect", () => {
          this.logger.info(`Client disconnected: ${connectionId}`);
          clearInterval(state.processInterval);
          state.audioStream.close();
          delete this.connectionState[connectionId];
          // delete globalState[connectionId];
        });
      }
    }
  },
  stopped() {

  },
};
