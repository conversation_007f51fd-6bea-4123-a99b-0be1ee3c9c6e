'use strict';
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {TRANSACTION, VNPT_PAY_TRANSACTION, VNPT_LOG} = require("../../../../constants/dbCollections");

let schema = new Schema(
  {
    date: {type: Date, required: true},
    amount: {type: Number, required: true},
    orderCode: {type: String, required: true},
    type: {type: String, enum: ['vnptpay', 'vnptqr'], required: true},
    /**
     * 0: default
     * 1: payment success
     * 2: payment failed/error
     */
    status: {type: Number, required: true, default: 0},
    paymentLogId: {type: mongoose.Schema.Types.ObjectId, ref: VNPT_LOG},
    transactionId: {type: mongoose.Schema.Types.ObjectId, ref: TRANSACTION},
    is_deleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);
module.exports = mongoose.model(VNPT_PAY_TRANSACTION, schema, VNPT_PAY_TRANSACTION)
