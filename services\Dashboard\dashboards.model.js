const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Schema } = require('mongoose');
const { DASHBOARD } = require('../../constants/dbCollections');

const schema = new mongoose.Schema({
  action: { type: String },
  type: {
    type: String,
    enum: ['GPT_PRICE', 'USER_COST_PER_MONTH',],
  },
  month: { type: String },
  year: { type: String },
  content: { type: Schema.Types.Mixed },
  isDeleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  collation: { locale: 'vi' },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(DASHBOARD, schema, DASHBOARD);
