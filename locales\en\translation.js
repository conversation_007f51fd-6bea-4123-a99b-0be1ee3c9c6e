exports.enTranslation = {
  app_name: 'Clickee.ai',
  userRouter: 'API user',
  error_user_login_username_password_wrong: 'Username or password was wrong',
  error_user_login_account_locked: 'Account locked, please contact to your admin.',
  error_account_not_active: 'Account has not been activated, please check your email to activate your account.',
  email_subject_user_forgot_password: 'Forgot password',
  email_html1_user_forgot_password: 'You have a request to change the password on the Clickee.ai',
  email_html2_user_forgot_password: 'Please click on the link to change your password',
  error_user_account_is_registered: 'Account is registered',
  error_user_email_was_registered: 'The Email was registered',
  error_user_phone_was_registered: 'The phone was registered',
  error_user_alias_already_exists: 'Alias already exists',
  email_user_create_from: 'Clickee AI',
  email_user_create_subject: 'Successful account registration',
  email_user_create_html1: 'You have successfully created an account at Clickee.ai, Account Information:',
  email_user_register_html1: 'You have successfully registered an account at Clickee.ai, Account Information:',
  email_user_update_html1: 'You have successfully updated your account at Clickee.ai, Account Information:',
  email_user_create_html2: 'Full name: ',
  email_user_create_html3: 'Username: ',
  email_user_create_html4: 'Phone: ',
  email_user_create_html5: 'Email: ',
  email_user_create_html6: 'Please login at ',
  error_user_invalid_token: 'Invalid token',
  error_user_invalid_refresh_token: 'Invalid refresh token',
  error_user_invalid_permission: 'Cannot create a new user with over than permission',
  user_domain_account: 'Domain account',
  user_can_not_be_blank: 'can not be blank',
  error_user_no_account_exist: 'No account exists',
  user_account: 'Account',
  user_exist: 'was exist',
  user_delete_insufficient_permissions: 'Insufficient permissions to delete this user',
  user_update_insufficient_permissions: 'Insufficient permissions to update this user',
  email_user_update_subject: 'Successful update account',
  error_old_password_wrong: 'Old password is incorrect',
  error_user_change_message_successful: 'Change password successfully',
  user_password_change: 'Your password has changed',
  hello: 'Hello',
  user_email_password_of: 'Password of: ',
  user_email_password_has_changed: 'has changed',
  user_email_regard: 'Regard',
  user_email_has_registered: 'Email has been registered',
  error_delete_already_operated_location: 'Unable to delete the location that has already been operated',
  setting_code_missing: 'Setting code is missing',
  parent_line_wrong_or_missing: 'Parent line is incorrect or missing',
  cant_delete_assigned_thematic: 'Cannot be delete the assigned thematic',
  device_already_exists: 'Device code already exists, please check and try again',
  status_name: 'Status name',
  status_code: 'Status code',
  already_exists: 'already exists, please check and try again',
  ID_number_already_exists: 'ID number already exists, please check and try again',
  cant_delete_content_with_test_result: 'Cannot delete content with test results',
  cant_delete_content_system_setting: 'Cannot delete content already exists in system setting',
  content_type_cant_empty: 'Content type cannot be empty',
  location_results_available: 'Location check results are available, content type cannot be edited',
  tower_results_available: 'Tower distance check results are available, content type cannot be edited',
  criteria: 'Criteria',
  have_test_result: 'have test result, cannot be delete',
  exist_in_system_setting: 'already exists in system setting, cannot be delete',
  content_name_already_exist: 'Content name already exists, please check and try again',
  delete_unit_child_before_delete_unit_parent: 'Delete the child unit before delete the parent unit',
  cant_delete_unit_assign: 'Cannot delete the unit because the unit has been assigned a job',
  cant_delete_unit_user: 'Cannot delete the unit because the unit still has user',
  unit_code_already_exists: 'Unit code already exists, please check and try again',
  email_already_exists: 'Email already exists, please check and try again',
  have_unexpected_error: 'There was an unexpected error, please contact the administrator',
  drone_code_already_exists: 'Drone code already exists, please check and try again',
  serial_already_exists: 'Serial already exists, please check and try again',
  delete_edit_location_before_delete_construction:
    'Please delete or edit the location before delete the passing construction!',
  location_tower_cant_empty: 'Location - Tower distance cannot be empty',
  location_cant_empty: 'Location cannot be empty',
  delete_profile_child_before_delete_profile_parent: 'Delete the child profile before delete the main profile',
  cant_delete_employee_confirm: 'Employee confirmed to work position, cannot be deleted',
  not_confirm_work_position_not_confirm_leave_work_position:
    'Not confirmed to work position, not confirmed to leave work position',
  not_adequate_correct_safety_measure: 'Failure to take adequate and correct safety measures, unable to accept work',
  not_take_more_safety_measure: "Haven't take more safety measures, can't accept work",
  not_complete_safety_measure_grounding:
    "Safety measures and additional grounding have not been completed yet, can't accept work",
  employee_not_confirm: "Employee hasn't confirmed yet, can't finish work",
  daily_work_not_confirm: "Daily work has not been confirmed, can't finish work",
  handover_not_complete: 'The handover has not been completed, the lock task cannot be confirmed',
  not_complete_power_cut: 'Power cut has not been completed, work cannot be allowed',
  not_complete_grounding: 'Grounding has not been completed, work cannot be allowed',
  not_complete_barriers_signs: 'Barriers - Signs has not been completed, work cannot be allowed',
  not_complete_scope_job: 'Scope of job has not been completed, work cannot be allowed',
  not_complete_warnings_instructions: 'Warnings - Instructionshas not been completed, work cannot be allowed',
  missing_type_of_work: 'Missing type of work',
  missing_leader: 'Missing leader',
  line_hasnt_chosen: "Line hasn't chosen yet",
  tower_hasnt_chosen: "Tower distance hasn't chosen yet",
  location_hasnt_chosen: "Location hasn't chosen yet",
  missing_start_time: 'Missing start time',
  missing_end_time: 'Missing end time',
  start_time_must_before_end_time: 'Start time must before end time',
  exist_process_hasnt_chosen: "Exist need process hasn't chosen yet",
  overdue_task_cant_delivery: "Overdue task can't delivery",
  task_cant_delivery: "Task can't delivery",
  overdue_task_cant_reception: "Overdue task can't reception",
  task_need_delivery: 'Task need delivery',
  leader_can_reception_work: 'Leader can receive work',
  leader_can_refuse_work: 'Leader can refuse work',
  overdue_task_cant_refuse: "Overdue task can't refuse",
  task_need_reception: 'Task need reception',
  leader_can_confirm_work: 'Leader can confirm work',
  leader_reception_task: 'Leader received task',
  task_need_lock: 'Task need lock',
  leader_can_cancel_lock: 'Leader can cancel lock task',
  leader_cancel_lock_task: 'Leader cancel lock task',
  task_need_returned_can_confirm: 'Task need to be return for confirm',
  cant_confirm_lock_not_result: "Lock task can't confirm because task hasn't confirmed result",
  test_result_cant_empty_line: 'The test result cannot be empty "Line"',
  unconfirm_task_cant_return: "The task has not been confirmed can't be returned",
  location_or_tower_hasnt_chosen: "Location or tower distance hasn't chosen yet",
  both_location_tower_not_empty: "Both location and tower distance can't be empty",
  location_not_belong_line: 'Location is not belong line or not yet created',
  tower_not_belong_line: 'Tower distance is not belong line or not yet created',
  missing_circuit_info_of_location: 'Missing circuit information of location',
  measurer_not_belong_unit_line_management: 'Measurer is not belong of the line management unit or not yet created',
  task_number_incorrect_or_dont_create: 'Task number is incorrect or not yet created',
  evaluation_result_incorrect_or_dont_create: 'Evaluation result is incorrect or not yet created',
  measure_direction_incorrect: 'Measure direction is incorrect',
  missing_wire_info: 'Missing wire information',
  incorrect_or_not_available: 'Incorrect or not available',
  missing_location_info: 'Missing location information',
  area_incorrect_or_dont_create: 'Area is incorrect or not yet created',
  conclude_incorrect_or_dont_create: 'Conclude is incorrect or not yet created',
  missing_tower_info: 'Missing tower distance information',
  solution_incorrect_or_dont_create: 'Solution is incorrect or not yet created',
  file_cant_download: "File can't dowload",
  report: 'Report',
  serial_code_existed: 'VT/serial code already exists, please check and try again',
  group_devices_existed: 'Group of devices already exists, please check and try again',
  delete_device_tool_in_tracking_notebook: 'Please delete device and tool in tracking notebook!',
  field_code_existed: 'Field code already exists, please check and try again',
  field_name_existed: 'Field name already exists, please check and try again',
  missing_unit_construction: 'Missing construction management unit',
  management_unit_incorrect_or_dont_create: 'Nanagement unit is incorrect or not yet created',
  missing_unit: 'Missing unit',
  unit_incorrect_or_dont_create: 'Unit is incorrect or not yet created',
  missing_construction: 'Missing construction',
  construction_incorrect_or_dont_create: 'Construction is incorrect or not yet created',
  missing_unit_management: 'Missing management unit',
  incorrect_or_dont_create: 'Incorrect or not yet created',
  parent_construction_incorrect_or_dont_create: 'Parent construction is incorrect or not yet created',
  parent_line_incorrect_or_dont_create: 'Parent line is incorrect or not yet created',
  missing_line_operation: 'Missing operation line',
  line_operation_incorrect_or_dont_create: 'Operation line is incorrect or not yet created',
  tower_distance_existed: 'Tower distance already exists, please check and try again',
  complete_or_cancel_task_assign_collaborate:
    'Please complete or cancel assign task, work sheet of line before change to "Non-Operating" state',
  line_has_been_separation: 'Separated line cannot have status is "Operating"',
  line_code_existed: 'Line code already exists, please check and try again',
  complete_or_cancel_task_assign_collaborate_before_update:
    'Please complete or cancel assign task, work sheet of line before update',
  operation_code_existed: 'Operation code already exists, please check and try again',
  location_code_existed: 'Location code already exists, please check and try again',
  nothing: 'Nothing',
  format_wrong: 'Wrong, correct format is DD/MM/YYYY',
  format_incorrect: 'Format is incorrect',
  notify_user_do_something_work: 'User {0} has just {1} {2} number {3} that you joined',
  notify_user_do_something_jobsheet: 'User {0} has just {1} business number {2} that you joined',
  notify_unit_has_unresolved_exist: 'Unit "{0}" has {1} unresolved existence',
  notify_system_has_ios_code: 'The system has {0} iOS application code left',
  notify_there_are_new_line_problem: 'There are {0} new line problems',
  notify_line_problem_confirm: 'Line problem {0} confirmed',
  notify_line_problem_cancel_confirm: 'Line problem {0} canceled confirm',
  notify_upgraded_new_version_line_flight: 'Line Management and Flight Control has been upgraded to a new version',
  notify_upgraded_new_version_flight: 'Flight Control has been upgraded to a new version',
  notify_upgraded_new_version_line: 'Line Management has been upgraded to a new version',
  notify_default_new_message: 'You have new message',
  DANG_TAO_PHIEU: 'create task',
  HUY_PHIEU: 'cancel task',
  GIAO_PHIEU: 'delivery task',
  TIEP_NHAN: 'receive task',
  TU_CHOI_NHAN: 'reject task',
  KHOA_PHIEU: 'lock task',
  TU_CHOI_KHOA: 'deny lock task',
  XAC_NHAN_KHOA: 'confirm to lock task',
  KIEM_TRA_DINH_KY_NGAY: 'Periodic check of the day',
  KIEM_TRA_DINH_KY_DEM: 'Periodic check of the night',
  KIEM_TRA_SU_CO: 'Check for problems',
  KIEM_TRA_DOT_XUAT: 'Extraordinary examination',
  KIEM_TRA_CHUYEN_DE: 'Thematic test',
  CO_KE_HOACH: 'Power grid troubleshooting',
  KHONG_CO_KE_HOACH: 'Maintenance, enhanced maintenance',
  DO_DIEN_TRO_TIEP_DIA: 'Measure ground resistance',
  DO_NHIET_DO_TIEP_XUC: 'Measure contact temperature',
  DO_KHOANG_CACH_PHA_DAT: 'Measure the ground phase distance',
  CONG_TAC_PHU_TRO: 'Supplementary task',
  KIEM_TRA: 'Inspection',
  SUA_CHUA_BAO_DUONG: 'Inspection and maintenance',
  DO_THONG_SO: 'Measurement task',
  title_delivered_task: 'Notification just delivered task',
  title_locked_task: 'Notification just locked task',
  title_action_task: 'Notification just {0} task',
  title_unresolved_exist: 'Notification unresolved existence',
  title_iOS_app_code: 'Notification iOS application code',
  title_line_problem: 'Notification line problem',
  title_confirm_line_problem: 'Notification confirm line problem',
  title_cancel_confirm_line_problem: 'Notification cancel confirm line problem',
  title_upgraded_new_version: 'Notification upgraded new version',
  title_notifycation_done_action: 'Notification: {0} have id is {1} just done {2}',
  please_check: 'Please check',
  here: 'here',
  system_has: 'The system has',
  ios_app_code: 'iOS application code',
  DUY_TU_KET_HOP_KHAC_PHUC: 'Combine two types of work',
  SUA_CHUA_VI_TRI: 'Fix location',
  SUA_CHUA_KHOANG_COT: 'Fix distance tower',
  KINH_DO: 'Longitude',
  VI_DO: 'Latitude',
  MISSING_DATA: 'Missing data',
  IS_NOT_NUMBER: 'Is not number',
  KHOANG_COT: 'Tower span',
  periodic_measure_grounding_resistance_line_content:
    'Periodic measure the ground resistance of the line {0} years {1}',
  periodic_measure_tangent_temperature_line_content:
    'Periodic contact temperature measurement of the line {0} years {1} times {2}',
  handling_existing_content: 'Content exists at:\r\nLine: {0}\r\nLocation: {1}\r\nTower span: {2}\r\nCategory: {3}',
  periodic_measure_grounding_resistance_line_title: 'Periodic measure ground resistance',
  periodic_measure_tangent_temperature_line_title: 'Periodic contact temperature measurement',
  handling_existing_title: 'Have existing content that needs to be handled',
  bao_cao_phieu_giao_viec: 'Report the assignment sheet of the unit',
  bao_cao_cham_tien_do: 'Report late vote',
  ly_lich_cot: 'Background column, line',
  bieu_mau_dia_phuong: 'The locality has a line going through',
  theo_doi_giao_cheo: 'Crossover tracking table',
  tong_ke_duong_day: 'Total line list',
  so_nhat_ky_van_hanh: 'Operation log book of power transmission team',
  theo_doi_hanh_lang_tuyen: 'Corridor tracking table',
  tong_ke_moi_noi: 'Joint inventory table',
  theo_doi_sua_chua_bao_duong: 'Maintenance and repair log book',
  error_email_not_found: 'Email not found',
  error_user_not_found: 'User not found',
  username_invalid: 'Username is invalid',
  username_has_registered: 'User has been registered',
  phone_has_registered: 'Phone number has been registered',
  insufficient_permission: 'Insufficient permission',
  change_password_successfully: 'Change password successfully',
  tradar_system: 'Clickee.ai',
  account_information_updated_successfully: 'Account information updated successfully',
  account_information: 'Account information',
  full_name: 'Full name',
  username: 'Username',
  phone: 'Phone',
  email: 'Email',
  error_delete_sysadmin: "Can't delete system admin account!",
  error_delete_user_on_mission: "Can't delete user on missions!",
  error_user_login_password_wrong: 'Wrong password',
  email_has_registered: 'Email has registered',
  account_already_exists: 'Account already exists',
  login_fail: 'Please check your email/password',
  enter_valid_email: 'Please enter a valid email address!',
  check_email_for_reset_link:
    'An email has been sent to the administrative email addess on the file. Check the inbox of administrator email account, and click the reset link provided',
  check_email_for_active_account_link:
    'An email has been sent to the administrative email addess on the file. Check the inbox of administrator email account, and click the link to activate your account',
  account_has_been_activated: 'Account has been activated',
  reset_password_successfully: 'Return to the login screen after a few seconds',
  expired_token: 'Expired access token',
  something_went_wrong: 'Something went wrong!',
  organization_users_not_found: 'Not found organization users',
  capacity_exceeded: 'Capacity exceeded',
  error_video_not_found: 'Video not found',
  range_header_is_required: 'Range header is required',
  large_page_range: 'Page range should be less than or equal to 5',
  error_file_not_found: 'File not found!',
  error_data_not_found: 'Data not found!',
  upgrade_your_plan: 'Upgrade your plan to use this feature',
  error_folder_not_found: 'Folder not found',
  dont_have_permission_folder: "You don't have permission to access this folder.",
  dont_have_permission_project: "You don't have permission to access this project.",
  dont_have_permission_template: "You don't have permission to access this template.",
  dont_have_permission_tool: "You don't have permission to access this tool.",
  google_email_not_verified: 'Google email not verified',
  no_refresh_token: 'No refresh token',
  you_need_to_be_an_admin: 'You need to be an admin to perform this action.',
  error_customer_not_found: 'Customer not found',
  incorrect_or_expired: 'Incorrect or expired',
  out_of_limit: 'Out of limit',
  submit_text_tool_limited: 'Submit text tool limited',
  submit_media_tool_limited: 'Submit media tool limited',
  cannot_move_up_content: 'Cannot move up content',
  cannot_move_down_content: 'Cannot move down content',
  error_input_not_found: 'Input not found',
  error_response_not_found: 'Response not found',
  error_workspace_not_found: 'Workspace not found',
  error_docxtemplate_not_found: 'Docx template not found',
  error_example_not_found: 'Example not found',
  error_content_not_found: 'Content not found',
  error_payment_gateway_not_found: 'Payment gateway not found',
  project_was_deleted: 'Project was deleted',
  folder_was_deleted: 'Folder was deleted',
  response_was_deleted: 'Response was deleted',
  error_project_not_found: 'Project not found',
  resource_not_supported: 'Resource file type not supported',
  resource_already_exists: 'Resource already exists',
  error_resource_not_found: 'Resource not found',
  error_tool_not_found: 'Tool not found',
  error_group_feedback_not_found: 'Group feedback not found',
  error_feedback_active: 'Feedback is active',
  role_code_already_exists: 'Role code already exists',
  template_was_deleted: 'Template was deleted',
  account_already_belongs_to_organization: 'Account already belongs to organization!',
  error_send_email: 'Send email error',
  paused_new_account_creation: "We've temporarily paused new account creation. Please check back later.",
  phone_number_already_exists: 'Phone number already exists',
  invitation_reject_success: 'Invitation reject success',
  invitation_has_expired: 'Invitation has expired',
  activation_account_has_expired: 'Activation account has expired',
  invalid_YouTube_URL: 'Invalid YouTube URL',
  correct_answers: 'Correct answers',
  exam_number: 'exam number',
  all_essay_has_been_submitted: 'All essays have been evaluated.',
  response_of_content: 'Response of content!',
  cannot_evaluate_without_content: 'Cannot evaluate without content!',
  no_input_text: 'No input text',
  image_no_text: 'This image has no text',
  audio_no_text: 'This audio has no transcript',
  video_no_text: 'This video has no transcript',
  file_no_text: 'This file has no text',
  input_data_not_supported: 'This type of data no supported',
  email_not_found: 'Email not found',
  email_in_whitelist: 'Email is already in the whitelist',
  email_in_waitlist: 'Email is already in the waiting list',
  email_added_to_waitlist: 'Email has been added to the waiting list',
  email_in_waitlist_try_again: 'Your account is in the waiting list. Please try again later.',
  subscription_expired:
    'Your subscription has expired. Please renew or subscribe to a new package to continue using the service.',
  error_permission_denied: 'You do not have permission to perform this action.',
  error_organization_not_found: 'Organization not found',
  organization_already_locked: 'Organization already locked',
  organization_already_unlocked: 'Organization already unlocked',
  error_project_type_not_found: 'Project type not found',

  submit_writing_limited: 'Submit writing limited',
  submit_speaking_limited: 'Submit speaking limited',
  submit_dictation_limited: 'Submit dictation limited',
  submit_shadowing_limited: 'Submit shadowing limited',
  submit_speaking_room_limited: 'Submit speaking room limited',

  error_gift_code_not_found: 'Gift code not found',
  error_gift_code_out_of_date: 'Gift code out of date',
  discount_already_used: 'Discount already used',
};
