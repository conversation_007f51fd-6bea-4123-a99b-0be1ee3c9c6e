const DbMongoose = require("../../../mixins/dbMongo.mixin");
const Model = require("./groupFeedbacks.model");
const BaseService = require("../../../mixins/baseService.mixin");
const FunctionsCommon = require("../../../mixins/functionsCommon.mixin");
const i18next = require("i18next");
const {MoleculerClientError} = require("moleculer").Errors;
const AuthRoles = require("../../../mixins/authRole.mixin");
const {ObjectId} = require("mongoose").Types;
const {FEEDBACK_TYPE} = require("../../../constants/constant");

module.exports = {
  name: 'groupFeedbacks',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AuthRoles],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: []
  },

  hooks: {},

  actions: {
    getListFeedback: {
      rest: "GET /listFeedback",
      async handler(ctx) {
        const {user} = ctx.meta;
        const [subscription, groupFeedback] = await Promise.all([
          ctx.call("subscriptions.getActive", {userId: user._id}),
          this.adapter.findOne({active: true})
        ]);
        if (!groupFeedback) {
          throw new MoleculerClientError(i18next.t("error_group_feedback_not_found"), 404);
        }

        const userFree = subscription?.packageId?.paidType === "free";
        const feedbackIds = groupFeedback?.feedbacks.map(feedback => ObjectId(feedback?.feedbackId));
        const feedbacks = await ctx.call("feedbacks.find", {
          query: {
            _id: {$in: feedbackIds},
            subscriptionType: userFree ? {$ne: 'paid'} : {$ne: 'free'}
          }
        });
        const groupDataFeedback = this.groupBy(feedbacks, "_id");

        return groupFeedback.feedbacks.filter(feedback => groupDataFeedback[feedback.feedbackId])
          .sort((a, b) => a.index - b.index)
          .map((feedback, index) => {
            return {
              index: index + 1,
              feedback: groupDataFeedback[feedback.feedbackId][0]
            }
          })
      }
    },
    create: {
      rest: "POST /",
      async handler(ctx) {
        const entity = ctx.params;
        if (entity.active) {
          await this.adapter.updateMany({active: true}, {$set: {active: false}});
        }
        return await this.adapter.insert(entity);
      }
    },
    update: {
      rest: "PUT /:id",
      async handler(ctx) {
        const {id} = ctx.params;
        const entity = ctx.params;
        const group = await this.adapter.findById(id);
        if (!group) {
          throw new MoleculerClientError(i18next.t("error_feedback_not_found"), 404);
        }

        if (entity.active) {
          await this.adapter.updateMany({active: true}, {$set: {active: false}});
        }

        return await this.adapter.updateById(id, {$set: entity}, {new: true});
      }
    },
    checkAutoFeedback: {
      rest: "GET /checkAutoFeedback",
      async handler(ctx) {
        const {user} = ctx.meta;
        const [subscription, settings, projects, countFeedbacks] = await Promise.all([
          ctx.call("subscriptions.getActive", {userId: user._id}),
          ctx.call("settings.findOne"),
          ctx.call("projects.find", {query: {ownerId: user._id}, populate: []}),
          ctx.call("submitFeedbacks.count", {query: {userId: user._id}})
        ]);

        if (!settings.showFeedback) {return {showFeedback: false}}
        if(countFeedbacks > 0) {return {showFeedback: false}}

        const userFree = subscription?.packageId?.paidType === "free";
        const submitLimit = userFree ? settings.userFreeSubmit : settings.userPaidSubmit;

        const projectIds = projects.map(project => ObjectId(project._id));
        const contents = await ctx.call("contents.find", {query: {projectId: {$in: projectIds}}});

        const contentIds = contents.map(content => ObjectId(content._id));
        const inputs = await ctx.call("inputs.find", {query: {contentId: {$in: contentIds}}});

        const totalSubmit = inputs.reduce((total, input) => total + input.numberSubmit, 0);
        if(totalSubmit >= submitLimit) {
          return {showFeedback: true};
        }
        return {showFeedback: false};
      }
    }
  },
  methods: {},
  events: {}
};
